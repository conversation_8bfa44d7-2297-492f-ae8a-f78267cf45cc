"""
结合鲁棒成本和绝对后悔值的满意度
"""
import networkx as nx
from gurobipy import *
import matplotlib.pyplot as plt

alpha = 1  # 权重系数
Z_max = 244    # 最大鲁棒成本
S_min = 21  # 下界最短路
S_max = 52  # 上界最短路
Smiddle_max = 146  # 中值最长路
r = 1  # 起点
t = 29  # 终点
N = [i for i in range(1, 30)]
N_rt = [r, t]
N_center = N.copy()
for k in [r, t]:
    N_center.remove(k)
Links = [(1, 2, 6, 10), (1, 3, 6, 8), (2, 3, 6, 10), (1, 4, 5, 9), (3, 4, 6, 10), (1, 5, 3, 5), (4, 5, 1, 4),
         (2, 6, 6, 10), (3, 6, 6, 10), (3, 7, 6, 8), (6, 7, 6, 10), (3, 8, 6, 10), (4, 8, 6, 8), (7, 8, 6, 10),
         (4, 9, 3, 9), (8, 9, 6, 10), (4, 10, 6, 10), (5, 10, 3, 10), (9, 10, 6, 10), (6, 11, 6, 10), (7, 11, 6, 10),
         (7, 12, 6, 8), (11, 12, 6, 10), (7, 13, 6, 10), (8, 13, 6, 8), (12, 13, 6, 10), (8, 14, 6, 10), (9, 14, 6, 8),
         (13, 14, 6, 10), (9, 15, 3, 9), (10, 15, 6, 12), (14, 15, 6, 10), (10, 16, 3, 10), (15, 16, 6, 10),
         (11, 17, 6, 10),
         (12, 17, 6, 10), (12, 18, 6, 8), (13, 18, 6, 8), (17, 18, 6, 10), (13, 19, 6, 10), (14, 19, 6, 8),
         (18, 19, 6, 10),
         (14, 20, 6, 10), (15, 20, 4, 9), (19, 20, 6, 10), (15, 21, 6, 10), (16, 21, 3, 10), (20, 21, 6, 10),
         (17, 22, 6, 10),
         (18, 22, 6, 10), (18, 23, 6, 8), (19, 23, 6, 8), (22, 23, 6, 10), (19, 24, 6, 8), (20, 24, 5, 10),
         (23, 24, 6, 10),
         (20, 25, 6, 10), (21, 25, 3, 11), (24, 25, 6, 10), (22, 26, 6, 10), (23, 26, 6, 10), (23, 27, 6, 8),
         (24, 27, 4, 9),
         (26, 27, 2, 3), (24, 28, 6, 10), (25, 28, 3, 10), (27, 28, 6, 10), (26, 29, 5, 13), (27, 29, 2, 4),
         (28, 29, 3, 13),
         (2, 1, 6, 10), (3, 1, 6, 8), (3, 2, 6, 10), (4, 1, 5, 9), (4, 3, 6, 10), (5, 1, 3, 5), (5, 4, 1, 4),
         (6, 2, 6, 10), (6, 3, 6, 10), (7, 3, 6, 8), (7, 6, 6, 10), (8, 3, 6, 10), (8, 4, 6, 8), (8, 7, 6, 10),
         (9, 4, 3, 9), (9, 8, 6, 10), (10, 4, 6, 10), (10, 5, 3, 10), (10, 9, 6, 10), (11, 6, 6, 10), (11, 7, 6, 10),
         (12, 7, 6, 8), (12, 11, 6, 10), (13, 7, 6, 10), (13, 8, 6, 8), (13, 12, 6, 10), (14, 8, 6, 10), (14, 9, 6, 8),
         (14, 13, 6, 10), (15, 9, 3, 9), (15, 10, 6, 12), (15, 14, 6, 10), (16, 10, 3, 10), (16, 15, 6, 10),
         (17, 11, 6, 10),
         (17, 12, 6, 10), (18, 12, 6, 8), (18, 13, 6, 8), (18, 17, 6, 10), (19, 13, 6, 10), (19, 14, 6, 8),
         (19, 18, 6, 10),
         (20, 14, 6, 10), (20, 15, 4, 9), (20, 19, 6, 10), (21, 15, 6, 10), (21, 16, 3, 10), (21, 20, 6, 10),
         (22, 17, 6, 10),
         (22, 18, 6, 10), (23, 18, 6, 8), (23, 19, 6, 8), (23, 22, 6, 10), (24, 19, 6, 8), (24, 20, 5, 10),
         (24, 23, 6, 10),
         (25, 20, 6, 10), (25, 21, 3, 11), (25, 24, 6, 10), (26, 22, 6, 10), (26, 23, 6, 10), (27, 23, 6, 8),
         (27, 24, 4, 9),
         (27, 26, 2, 3), (28, 24, 6, 10), (28, 25, 3, 10), (28, 27, 6, 10), (29, 26, 5, 13), (29, 27, 2, 4),
         (29, 28, 3, 13)]
G = nx.DiGraph()
for k in range(len(Links)):
    G.add_edge(Links[k][0], Links[k][1], time=[Links[k][2], Links[k][3]],
               time_lb=Links[k][2], time_ub=Links[k][3], time_mid=(Links[k][2] + Links[k][3]) / 2)
time = nx.get_edge_attributes(G, 'time')
time_lb = nx.get_edge_attributes(G, 'time_lb')
time_ub = nx.get_edge_attributes(G, 'time_ub')
# 路网中的所有边
L = []
for item in time_lb.items():
    L.append(item[0])
Lt = tuplelist(L)


# print('路网所有的边：',L)

# 定义加割函数addBendersCuts
def addBendersCuts(d_obj, x):
    # print(x)
    if d.status == GRB.Status.UNBOUNDED:  # 模型无界
        ## 取极射线，向模型中添加可行割
        ray = d.UnbdRay
        # print(ray)
        ## 添加可行割
        m.addConstr(1 - (alpha * (1-((quicksum(time_ub[i, j] * p[i, j] for i, j in L) -
                             quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j])
                                      * ray[i, j] for i, j in L))/Z_max)) + (1 - alpha) *
                    (1-(quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L) - S_min - S_max)/
                     (2*Smiddle_max - S_min - S_max))) <= 0)
        print("*****加入可行割*****")
        ## 没有最优解，默认等于上次迭代的结果
        d_obj.append(d_obj[-1])
    elif d.status == GRB.Status.OPTIMAL:  # 发现最优解
        ## 添加最优割
        m.addConstr(1 - (alpha * (1 - ((quicksum(time_ub[i, j] * p[i, j] for i, j in L) -
                                   quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j])
                                        * w[i, j].x for i, j in L)) / Z_max)) + (1 - alpha) *
                    (1 - (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L) - S_min - S_max) /
                     (2*Smiddle_max - S_min - S_max))) <= z)
        print("*****加入最优割*****")
        ## 将最优解与当前主问题的f(y*)相加构成问题的上界加入到集合中
        d_obj.append(d.objVal)  # 获取最优解
    else:  # 其他状态，模型不可行
        print(d.status)


try:
    ## 初始化下界记录数组
    m_obj = []
    ## 创建限制主问题MP
    m = Model('masterproblem')  # Benders Master Problem
    ## 创建对偶子问题SP_Dual
    d = Model('dual_subproblem')  # dual of Benders SubProblem
    ## 注意，这里是加入限制主问题的变量，而不是初始子问题的变量
    ## 向MP中添加变量
    z = m.addVar(vtype=GRB.CONTINUOUS, name='z')
    p = m.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='p')


    ## 设置MP的目标函数
    m.setObjective(z, GRB.MINIMIZE)
    ## 向对偶子问题中添加变量
    w = d.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='w')

    # 添加约束
    ## 向主问题中加入约束
    ###起点约束
    m_con1 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select(r, '*')) == 1)
    m_con2 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', r)) == 0)
    ###终点约束
    m_con3 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', t)) == 1)
    m_con4 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select(t, '*')) == 0)
    ###中间点约束
    for j in N_center:
        m_con5 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', j))
                             == quicksum(p[j, k] for j, k in Lt.select(j, '*')))
    m_con6 = m.addConstr(quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L) >= 0)
    # 添加辅助变量 u[i]
    u = m.addVars(N, vtype=GRB.BINARY, name='u')

    # 将 u[i] 与 p[i, j] 关联
    for i in N:
        m.addConstr(quicksum(p[i, j] for j in N if (i, j) in Lt) <= u[i] * len(N))

    # 限制总的节点数
    m_con7 = m.addConstr(quicksum(u[i] for i in N) <= 9)
    ###有效路径约束
    # m_con6 = m.addConstr()
    ## 向对偶子问题中加入约束
    ###起点约束
    d_con1 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select(r, '*')) == 1)
    d_con2 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', r)) == 0)
    ###终点约束
    d_con3 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', t)) == 1)
    d_con4 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select(t, '*')) == 0)
    ###中间点约束
    for j in N_center:
        d_con5 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', j)) ==
                             quicksum(w[j, k] for j, k in Lt.select(j, '*')))

    d_con6 = d.addConstr(quicksum((time_lb[i, j] + time_ub[i, j]) * w[i, j] for i, j in L) >= 0)
    # 设置参数 InfUnbdInfo，此变量的解释 https://www.gurobi.com/documentation/9.5/refman/infunbdinfo.html
    # 这个参数是为了返回极射线
    d.Params.InfUnbdInfo = 1
    ## 设置迭代次数
    iteration = 0
    ## 初始化上界记录数组
    d_obj = [1]
    x = []  # x是用来接收变量取值的，与 .x方法两回事，要注意
    print("***第{}次求解主问题MP***".format(iteration + 1))
    ## 第一次求解MP
    m.optimize()
    print('第{}次主问题目标函数'.format(iteration + 1), m.objVal)
    for i, j in L:
        if p[i, j].x == 1:
            print('p[%d,%d] = %d' % (i, j, p[i, j].x))
    ## 将 MP 最优值放入 集合中
    m_obj.append(m.objval)
    ## z.x可以看作子问题最优解，可以理解为子问题的上界
    # while d_obj[-1] > z.x:  # 迭代主循环
    while iteration < 5:
        if iteration == 0:
            ## 第一次调用对偶子问题，需要构造它的目标函数
            d.setObjective(1 - (alpha * (1 - ((quicksum(time_ub[i, j] * p[i, j].x for i, j in L) -
                                   quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j].x)
                                            * w[i, j] for i, j in L)) / Z_max)) + (1 - alpha) *
                    (1 - (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j].x for i, j in L) - S_min - S_max) /
                     (2*Smiddle_max - S_min - S_max))), GRB.MAXIMIZE)
            print("***第{}次求解子问题对偶问题SP_dual***".format(iteration + 1))
            ## 求解对偶子问题
            d.optimize()
            print('第{}次对偶子问题目标函数'.format(iteration + 1), d.objVal)
            for i, j in L:
                if w[i, j].x == 1:
                    print('w[%d,%d] = %d' % (i, j, w[i, j].x))
            ## 调用加割函数
            addBendersCuts(d_obj, x)
            iteration = 1
        else:
            d.setObjective(1 - (alpha * (1 - ((quicksum(time_ub[i, j] * p[i, j].x for i, j in L) -
                                    quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j].x)
                                            * w[i, j] for i, j in L)) / Z_max)) + (1 - alpha) *
                    (1 - (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j].x for i, j in L) - S_min - S_max) /
                      (2*Smiddle_max - S_min - S_max))), GRB.MAXIMIZE)
            print("***第{}次求解子问题对偶问题SP_dual***".format(iteration + 1))
            ## 求解对偶子问题
            d.optimize()
            print('第{}次对偶子问题目标函数'.format(iteration + 1), d.objVal)
            for i, j in L:
                if w[i, j].x == 1:
                    print('w[%d,%d] = %d' % (i, j, w[i, j].x))
            ## 调用加割函数
            addBendersCuts(d_obj, x)  # add Benders Cuts
            ## 迭代次数更新
            iteration = iteration + 1
        print("***第{}次求解主问题MP***".format(iteration + 1))
        ## 求解主问提（加割后）
        m.optimize()
        print('第{}次主问题目标函数'.format(iteration + 1), m.objVal)
        for i, j in L:
            if p[i, j].x == 1:
                print('p[%d,%d] = %d' % (i, j, p[i, j].x))
        ## 将当前fy*加入集合中
        m_obj.append(m.objval)
    ##打印路径决策变量为1的路径
    L_decision = []
    for i, j in L:
        if p[i, j].x == 1:
            L_decision.append((i, j))
    print('加权决策路径', L_decision)

##画出决策路径
    print('画出加权决策路径')
    pos = {1: (0.46035, 5.0452), 2: (0.73589, 7.177), 3: (1.4617, 6.0917), 4: (1.6095, 4.4897), 5: (1.0316, 3.1072),
           6: (1.5827, 8.4173), 7: (2.2749, 7.1641), 8: (2.7251, 5.3165), 9: (2.6781, 3.624), 10: (2.127, 1.7765),
           11: (2.7722, 9.1279), 12: (3.4778, 7.7842), 13: (3.8071, 6.2726), 14: (3.8542, 4.2313), 15: (3.6929, 2.5517),
           16: (3.2359, 0.67829), 17: (4.5128, 9.0504), 18: (5.0302, 7.5), 19: (5.2587, 5.3036), 20: (5.2386, 3.2235),
           21: (4.8353, 1.1305), 22: (6.0921, 8.6886), 23: (6.4751, 6.7119), 24: (6.5558, 4.593), 25: (6.334, 2.0995),
           26: (7.5034, 8.1202), 27: (7.8058, 5.8075), 28: (7.584, 3.1589), 29: (8.9751, 5.7041)}
    fig = plt.figure(figsize=(10, 16))
    plt.subplot(2, 1, 1)
    nx.draw_networkx(G, pos)
    nx.draw_networkx_nodes(G, pos, node_color='w', edgecolors='k')
    nx.draw_networkx_labels(G, pos, font_color='r')
    nx.draw_networkx_edges(G, pos, edge_color='b')
    nx.draw_networkx_edge_labels(G, pos, time, verticalalignment='center_baseline', font_size=12)
    nx.draw_networkx_edges(G, pos, edgelist=L_decision, edge_color='r', width=3)
    plt.show()

    ## 以下部分为绘制图像部分
    x_label = [i for i in range(1, len(m_obj) + 1)]
    print('上界迭代过程', d_obj)  ## 打印上界下降过程
    print('下界迭代过程', m_obj)  ## 打印下界上升过程
    plt.subplot(2, 1, 2)
    plt.plot(x_label, d_obj, 'b^-', alpha=0.8, linewidth=1, label='upper bound')
    plt.plot(x_label, m_obj, 'r^-', alpha=0.8, linewidth=1, label='lower bound')

    ### 显示标签，如果不加这句，即使在plot中加了label='一些数字'的参数，最终还是不会显示标签
    plt.legend(loc="upper right")
    plt.xlabel('iteration')
    plt.ylabel('value')
    plt.show()

except GurobiError as e:
    print('Error code ' + str(e.errno) + ": " + str(e))
except AttributeError:
    print('Encountered an attribute error')
print(m.objVal)