import heapq
from collections import defaultdict


# ================== 基础数据结构与函数 ==================
def get_time_interval(edge_intervals, current_time):
    """ 根据当前时间获取路段的时间区间上下界 """
    for interval in edge_intervals:
        start, end, min_t, max_t = interval
        if start <= current_time < end:
            return min_t, max_t
    return float('inf'), float('inf')  # 默认值


# ================== Yen's算法实现 ==================
def yen_k_shortest_paths(graph, start, target, k=3):
    """ 生成取下界权重的最短路径列表（用于候选路径生成） """

    def dijkstra_lower_bound(start_node):
        # 取下界权重的最短路径计算
        heap = [(0, start_node, [])]
        visited = {}
        while heap:
            cost, node, path = heapq.heappop(heap)
            if node in visited:
                continue
            visited[node] = cost
            new_path = path + [node]
            if node == target:
                return cost, new_path
            for neighbor in graph.get(node, {}):
                edge_intervals = graph[node][neighbor]
                min_t, _ = get_time_interval(edge_intervals, 0)  # 取下界（时间0为基准）
                if neighbor not in visited:
                    heapq.heappush(heap, (cost + min_t, neighbor, new_path.copy()))
        return float('inf'), []

    # 主逻辑
    _, shortest_path = dijkstra_lower_bound(start)
    if not shortest_path:
        return []

    A = [shortest_path]
    B = []

    for _ in range(1, k):
        last_path = A[-1]
        for i in range(len(last_path) - 1):  # 关键修复：遍历到倒数第二个节点
            spur_node = last_path[i]
            next_node = last_path[i + 1]  # 直接从原路径获取下一个节点
            root_path = last_path[:i + 1]

            # 创建临时图并移除spur_node到next_node的边
            temp_graph = defaultdict(dict)
            for u in graph:
                for v in graph[u]:
                    if not (u == spur_node and v == next_node):
                        temp_graph[u][v] = graph[u][v]

            # 计算spur路径
            spur_cost, spur_path = dijkstra_lower_bound(spur_node)
            if spur_path and spur_path[-1] == target:
                full_path = root_path[:-1] + spur_path
                if full_path not in A + B:
                    B.append(full_path)

        if not B:
            break
        A.append(B.pop(0))

    return A[:k]


# ================== 鲁棒成本计算 ==================
def compute_upper_bound_cost(graph, path, start_time):
    current_time = start_time
    total_max = 0
    time_records = []
    for i in range(len(path) - 1):
        u, v = path[i], path[i + 1]
        edge_intervals = graph[u].get(v, [])
        min_t, max_t = get_time_interval(edge_intervals, current_time)
        total_max += max_t
        time_records.append((u, v, current_time, max_t))
        current_time += max_t
    return total_max, time_records


def build_modified_graph(original_graph, time_records):
    modified_graph = defaultdict(dict)
    # 遍历所有边
    for u in original_graph:
        for v in original_graph[u]:
            in_p = any(record[0] == u and record[1] == v for record in time_records)
            if in_p:
                # 找到对应时间记录中的max_t
                for record in time_records:
                    if record[0] == u and record[1] == v:
                        modified_graph[u][v] = record[3]
            else:
                # 动态取下界
                modified_graph[u][v] = lambda t, u=u, v=v: get_time_interval(original_graph[u][v], t)[0]
    return modified_graph


def time_dependent_dijkstra(graph, start, target, start_time):
    heap = [(0, start, start_time, [start])]
    visited = defaultdict(lambda: float('inf'))

    while heap:
        cost, node, curr_time, path = heapq.heappop(heap)
        if node == target:
            return cost, path
        if curr_time >= visited[node]:
            continue
        visited[node] = curr_time
        for neighbor in graph.get(node, {}):
            weight = graph[node][neighbor](curr_time) if callable(graph[node][neighbor]) else graph[node][neighbor]
            new_time = curr_time + weight
            new_cost = cost + weight
            if new_time < visited[neighbor]:
                heapq.heappush(heap, (new_cost, neighbor, new_time, path + [neighbor]))
    return float('inf'), []


def compute_robust_cost(graph, path, start, target, start_time):
    upper_cost, time_records = compute_upper_bound_cost(graph, path, start_time)
    modified_graph = build_modified_graph(graph, time_records)
    p_star_cost, _ = time_dependent_dijkstra(modified_graph, start, target, start_time)
    return upper_cost - p_star_cost if p_star_cost != float('inf') else float('inf')


# ================== 主程序 ==================
if __name__ == "__main__":
    # 定义时间依赖路网
    graph = {
        'A': {
            'B': [
                [0, 480, 20, 30],  # 0:00-8:00
                [480, 600, 30, 50],  # 8:00-10:00
                [600, 1440, 20, 40]  # 10:00-24:00
            ],
            'C': [[0, 1440, 10, 15]]
        },
        'B': {
            'C': [
                [0, 540, 10, 25],  # 0:00-9:00
                [540, 1440, 20, 30]  # 9:00-24:00
            ],
            'D': [[0, 1440, 10, 20]]
        },
        'C': {'D': [[0, 1440, 15, 30]]},
        'D': {}
    }


    # 寻找最小鲁棒路径
    def find_min_robust_path(graph, start, target, start_time, k=3):
        candidate_paths = yen_k_shortest_paths(graph, start, target, k)
        min_cost = float('inf')
        best_path = None
        for path in candidate_paths:
            robust_cost = compute_robust_cost(graph, path, start, target, start_time)
            print(f"路径 {path} 鲁棒成本: {robust_cost}")
            if robust_cost < min_cost:
                min_cost = robust_cost
                best_path = path
        return best_path, min_cost


    # 示例运行
    best_path, min_cost = find_min_robust_path(graph, 'A', 'D', start_time=470)  # 8:00出发
    print(f"最优路径: {best_path}, 最小鲁棒成本: {min_cost}")