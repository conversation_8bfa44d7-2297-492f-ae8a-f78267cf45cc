import amplpy

def solve_shortest_path():
    # 创建AMPL对象
    ampl = amplpy.AMPL()

    # 读取AMPL模型和数据文件
    ampl.read('shortest_path.mod')
    ampl.readData('shortest_path.dat')

    # 设置求解器
    ampl.setOption('solver', 'gurobi')  # 或者你可以选择其他求解器，如 'cplex'，'minos' 等

    # 求解模型
    ampl.solve()

    # 获取目标函数的值
    total_length = ampl.getObjective('Total_Length').value()
    print(f"最小路径总长度: {total_length}")

    # 获取决策变量（flow）并显示选择的边
    flow = ampl.getVariable('flow').getValues()

    print("选择的边:")
    # flow 是一个 DataFrame 对象，转换为字典
    flow_dict = flow.to_dict()
    for edge, value in flow_dict.items():
        if value > 0.5:  # 只有选择的边才会有较大的值
            print(f"从 {edge[0]} 到 {edge[1]}")

if __name__ == "__main__":
    solve_shortest_path()
