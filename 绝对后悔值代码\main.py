# main.py
from gurobipy import *
from data import *
from function import solve_benders

if __name__ == '__main__':
    # 创建主问题和子问题模型
    m = Model('masterproblem')
    d = Model('dual_subproblem')

    # 求解 Benders 的优化问题
    m_obj, d_obj = solve_benders(m, d, alpha, SP_min, SP_max, time_lb, time_ub, L, Lt, r, t, N_center)

    # 打印结果
    print("主问题目标值:", m_obj)
    print("对偶子问题目标值:", d_obj)

    # 可视化加权决策路径
    print('画出加权决策路径')
    pos = {1: (0.46035, 5.0452), 2: (0.73589, 7.177), 3: (1.4617, 6.0917), 4: (1.6095, 4.4897), 5: (1.0316, 3.1072),
           6: (1.5827, 8.4173), 7: (2.2749, 7.1641), 8: (2.7251, 5.3165), 9: (2.6781, 3.624), 10: (2.127, 1.7765),
           11: (2.7722, 9.1279), 12: (3.4778, 7.7842), 13: (3.8071, 6.2726), 14: (3.8542, 4.2313), 15: (3.6929, 2.5517),
           16: (3.2359, 0.67829), 17: (4.5128, 9.0504), 18: (5.0302, 7.5), 19: (5.2587, 5.3036), 20: (5.2386, 3.2235),
           21: (4.8353, 1.1305), 22: (6.0921, 8.6886), 23: (6.4751, 6.7119), 24: (6.5558, 4.593), 25: (6.334, 2.0995),
           26: (7.5034, 8.1202), 27: (7.8058, 5.8075), 28: (7.584, 3.1589), 29: (8.9751, 5.7041)}
    L_decision = [(i, j) for i, j in L if m.getVarByName(f'p[{i},{j}]').x == 1]

    import matplotlib.pyplot as plt
    plt.figure()
    nx.draw(G, pos, with_labels=True)
    nx.draw_networkx_edges(G, pos, edgelist=L_decision, edge_color='r', width=2)
    plt.show()
