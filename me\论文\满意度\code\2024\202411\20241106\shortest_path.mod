set NODES;                      # 节点集合
set ARCS within {NODES, NODES}; # 边集合（有向边）

param source symbolic;          # 源节点
param target symbolic;          # 目标节点
param length {ARCS} >= 0;       # 边的权重/长度

var flow {ARCS} binary;         # 决策变量，是否选择该边
minimize Total_Length: sum { (i, j) in ARCS } length[i, j] * flow[i, j]; # 目标函数

# 单独处理源节点和目标节点的流量平衡
subject to Source_Flow:
    sum { (source, j) in ARCS } flow[source, j] = 1;  # 源节点流出1单位

subject to Target_Flow:
    sum { (i, target) in ARCS } flow[i, target] = 1;  # 目标节点流入1单位

# 其他节点的流量平衡
subject to Flow_Balance {n in NODES diff {source, target}}:
    sum { (i, n) in ARCS } flow[i, n] = sum { (n, j) in ARCS } flow[n, j];
