import numpy as np

# 数据定义，26个节点63条路段
nodes = np.arange(1, 27)  # 26个节点
edges = np.array([
    [1, 2], [1, 3], [1, 4], [1, 5],
    [2, 3], [2, 6], [2, 11],
    [3, 4], [3, 6], [3, 7],
    [4, 5], [4, 7], [4, 8], [4, 9],
    [5, 9], [5, 10],
    [6, 7], [6, 11], [6, 12], [6, 13],
    [7, 8], [7, 13],
    [8, 9], [8, 13], [8, 14],
    [9, 10], [9, 14], [9, 15],
    [10, 15], [10, 16],
    [11, 12],
    [12, 13], [12, 17],
    [13, 14], [13, 17], [13, 18],
    [14, 15], [14, 18], [14, 19],
    [15, 16], [15, 19], [15, 20],
    [16, 20], [16, 21],
    [17, 18], [17, 22],
    [18, 19], [18, 22], [18, 23],
    [19, 20], [19, 23], [19, 24],
    [20, 21], [20, 24],
    [21, 24], [21, 25],
    [22, 23], [22, 26],
    [23, 24], [23, 26],
    [24, 25], [24, 26],
    [25, 26],
    [2, 1], [3, 1], [4, 1], [5, 1],
    [3, 2], [6, 2], [11, 2],
    [4, 3], [6, 3], [7, 3],
    [5, 4], [7, 4], [8, 4], [9, 4],
    [9, 5], [10, 5],
    [7, 6], [11, 6], [12, 6], [13, 6],
    [8, 7], [13, 7],
    [9, 8], [13, 8], [14, 8],
    [10, 9], [14, 9], [15, 9],
    [15, 10], [16, 10],
    [12, 11],
    [13, 12], [17, 12],
    [14, 13], [17, 13], [18, 13],
    [15, 14], [18, 14], [19, 14],
    [16, 15], [19, 15], [20, 15],
    [20, 16], [21, 16],
    [18, 17], [22, 17],
    [19, 18], [22, 18], [23, 18],
    [20, 19], [23, 19], [24, 19],
    [21, 20], [24, 20],
    [24, 21], [25, 21],
    [23, 22], [26, 22],
    [24, 23], [26, 23],
    [25, 24], [26, 24],
    [26, 25]
])

# 边的下界和上界阻抗（需要与边的数量一致）
l = np.array([0.2, 1, 0.9, 0.2, 0.9, 0.8, 1.5, 1.1, 1.5, 0.8, 0.1, 1.1, 0.5, 1.1, 0.9, 0.5, 0.7, 0.7, 1.4,
              0.9, 0.2, 0.4, 0.6, 1.1, 0.6, 0.7, 0.7, 1.2, 1.1, 1, 0.7, 0.3, 0.4, 1.2, 1.5, 0.9, 0.5, 0.9,
              0.6, 1.1, 1, 1, 1.2, 0.8, 1.2, 1, 0.5, 0.5, 0.6, 0.3, 0.9, 0.2, 1.1, 0.5, 1.2, 1.1, 0.7,
              0.9, 2, 0.8, 0.5, 0.5, 2.4])
u = np.array([4, 2, 1.8,  2.1, 1.7, 2.1, 3.2, 1.7, 2.1, 1.5, 1.8, 1.3, 1.3, 2.4, 1.5, 1.5, 1.2, 2.1, 2.3,
              1, 1.5, 2.5, 1.6, 3.5, 1.7, 1.4, 3, 1.8, 1.7, 2.4, 2.1, 1.5, 1.9, 1.9, 3.2, 1.9, 2.5, 2.5,
              2.1, 1.6, 2.8, 1.7, 2.7, 2.2, 2.5, 1.6, 1.5, 1.7, 2.2, 2.3, 2.5, 2.6, 2.4, 2.2, 2.8, 2.7, 1.8,
              2.8, 2.2, 2.3, 1.4, 3.8, 4.4])

# 起点和终点
start_node = 1
end_node = 26

"""
    (1, 2, 0.2, 4), (1, 3, 1, 2), (1, 4, 0.9, 1.8), (1, 5, 0.2, 2.1),
    (2, 3, 0.9, 1.7), (2, 6, 0.8, 2.1),(2, 11, 1.5, 3.2),
    (3, 4, 1.1, 1.7), (3, 6, 1.5, 2.1), (3, 7, 0.8, 1.5),
    (4, 5, 0.1, 1.8), (4, 7, 1.1, 1.3),(4, 8, 0.5, 1.3), (4, 9, 1.1, 2.4),
    (5, 9, 0.9, 1.5), (5, 10, 0.5, 1.5),
    (6, 7, 0.7, 1.2), (6, 11, 0.7, 2.1),(6, 12, 1.4, 2.3), (6, 13, 0.9, 1),
    (7, 8, 0.2, 1.5), (7, 13, 0.4, 2.5),
    (8, 9, 0.6, 1.6), (8, 13, 1.1, 3.5),(8, 14, 0.6, 1.7),
    (9, 10, 0.7, 1.4), (9, 14, 0.7, 3), (9, 15, 1.2, 1.8),
    (10, 15, 1.1, 1.7), (10, 16, 1, 2.4),
    (11, 12, 0.7, 2.1),
    (12, 13, 0.3, 1.5), (12, 17, 0.4, 1.9),
    (13, 14, 1.2, 1.9), (13, 17, 1.5, 3.2),(13, 18, 0.9, 1.9),
    (14, 15, 0.5, 2.5), (14, 18, 0.9, 2.5), (14, 19, 0.6, 2.1),
    (15, 16, 1.1, 1.6), (15, 19, 1, 2.8),(15, 20, 1, 1.7),
    (16, 20, 1.2, 2.7), (16, 21, 0.8, 2.2),
    (17, 18, 1.2, 2.5), (17, 22, 1, 1.6),
    (18, 19, 0.5, 1.5),(18, 22, 0.5, 1.7),(18, 23, 0.6, 2.2),
    (19, 20, 0.3, 2.3), (19, 23, 0.9, 2.5), (19, 24, 0.2, 2.6),
    (20, 21, 1.1, 2.4),(20, 24, 0.5, 2.2),
    (21, 24, 1.2, 2.8), (21, 25, 1.1, 2.7),
    (22, 23, 0.7, 1.8), (22, 26, 0.9, 2.8),
    (23, 24, 2, 2.2), (23, 26, 0.8, 2.3),
    (24, 25, 0.5, 1.4), (24, 26, 0.5, 3.8),
    (25, 26, 2.4, 4.4),
    (2, 1, 0.2, 4), (3, 1, 1, 2), (4, 1, 0.9, 1.8), (5, 1, 0.2, 2.1),
    (3, 2, 0.9, 1.7), (6, 2, 0.8, 2.1), (11, 2, 1.5, 3.2),
    (4, 3, 1.1, 1.7), (6, 3, 1.5, 2.1), (7, 3, 0.8, 1.5),
    (5, 4, 0.1, 1.8), (7, 4, 1.1, 1.3), (8, 4, 0.5, 1.3), (9, 4, 1.1, 2.4),
    (9, 5, 0.9, 1.5), (10, 5, 0.5, 1.5),
    (7, 6, 0.7, 1.2), (11, 6, 0.7, 2.1), (12, 6, 1.4, 2.3), (13, 6, 0.9, 1),
    (8, 7, 0.2, 1.5), (13, 7, 0.4, 2.5),
    (9, 8, 0.6, 1.6), (13, 8, 1.1, 3.5), (14, 8, 0.6, 1.7),
    (10, 9, 0.7, 1.4), (14, 9, 0.7, 3), (15, 9, 1.2, 1.8),
    (15, 10, 1.1, 1.7), (16, 10, 1, 2.4),
    (12, 11, 0.7, 2.1),
    (13, 12, 0.3, 1.5), (17, 12, 0.4, 1.9),
    (14, 13, 1.2, 1.9), (17, 13, 1.5, 3.2), (18, 13, 0.9, 1.9),
    (15, 14, 0.5, 2.5), (18, 14, 0.9, 2.5), (19, 14, 0.6, 2.1),
    (16, 15, 1.1, 1.6), (19, 15, 1, 2.8), (20, 15, 1, 1.7),
    (20, 16, 1.2, 2.7), (21, 16, 0.8, 2.2),
    (18, 17, 1.2, 2.5), (22, 17, 1, 1.6),
    (19, 18, 0.5, 1.5), (22, 18, 0.5, 1.7), (23, 18, 0.6, 2.2),
    (20, 19, 0.3, 2.3), (23, 19, 0.9, 2.5), (24, 19, 0.2, 2.6),
    (21, 20, 1.1, 2.4), (24, 20, 0.5, 2.2),
    (24, 21, 1.2, 2.8), (25, 21, 1.1, 2.7),
    (23, 22, 0.7, 1.8), (26, 22, 0.9, 2.8),
    (24, 23, 2, 2.2), (26, 23, 0.8, 2.3),
    (25, 24, 0.5, 1.4), (26, 24, 0.5, 3.8),
    (26, 25, 2.4, 4.4)
"""

"""
(1, 2, 4.2), (1, 3, 3.0), (1, 4, 2.7), (1, 5, 2.3), 
(2, 3, 2.6), (2, 6, 2.9), (2, 11, 4.7), 
(3, 4, 2.8), (3, 6, 3.6), (3, 7, 2.3), 
(4, 5, 1.9), (4, 7, 2.4), (4, 8, 1.8), (4, 9, 3.5), 
(5, 9, 2.4), (5, 10, 2.0), 
(6, 7, 1.9), (6, 11, 2.8), (6, 12, 3.7), (6, 13, 1.9), 
(7, 8, 1.7), (7, 13, 2.9), 
(8, 9, 2.2), (8, 13, 4.6), (8, 14, 2.3), 
(9, 10, 2.1), (9, 14, 3.7), (9, 15, 3.0), 
(10, 15, 2.8), (10, 16, 3.4), 
(11, 12, 2.8), 
(12, 13, 1.8), (12, 17, 2.3), 
(13, 14, 3.1), (13, 17, 4.7), (13, 18, 2.8), 
(14, 15, 3.0), (14, 18, 3.4), (14, 19, 2.7), 
(15, 16, 2.7), (15, 19, 3.8), (15, 20, 2.7), 
(16, 20, 3.9), (16, 21, 3.0), 
(17, 18, 3.7), (17, 22, 2.6), 
(18, 19, 2.0), (18, 22, 2.2), (18, 23, 2.8), 
(19, 20, 2.6), (19, 23, 3.4), (19, 24, 2.8), 
(20, 21, 3.5), (20, 24, 2.7), 
(21, 24, 4.0), (21, 25, 3.8), 
(22, 23, 2.5), (22, 26, 3.7), 
(23, 24, 4.2), (23, 26, 3.1), 
(24, 25, 1.9), (24, 26, 4.3), 
(25, 26, 6.8), 
(2, 1, 4.2), (3, 1, 3.0), (4, 1, 2.7), (5, 1, 2.3), 
(3, 2, 2.6), (6, 2, 2.9), (11, 2, 4.7), 
(4, 3, 2.8), (6, 3, 3.6), (7, 3, 2.3), 
(5, 4, 1.9), (7, 4, 2.4), (8, 4, 1.8), (9, 4, 3.5), 
(9, 5, 2.4), (10, 5, 2.0), 
(7, 6, 1.9), (11, 6, 2.8), (12, 6, 3.7), (13, 6, 1.9), 
(8, 7, 1.7), (13, 7, 2.9), 
(9, 8, 2.2), (13, 8, 4.6), (14, 8, 2.3), 
(10, 9, 2.1), (14, 9, 3.7), (15, 9, 3.0), 
(15, 10, 2.8), (16, 10, 3.4), 
(12, 11, 2.8), 
(13, 12, 1.8), (17, 12, 2.3), 
(14, 13, 3.1), (17, 13, 4.7), (18, 13, 2.8), 
(15, 14, 3.0), (18, 14, 3.4), (19, 14, 2.7), 
(16, 15, 2.7), (19, 15, 3.8), (20, 15, 2.7), 
(20, 16, 3.9), (21, 16, 3.0), 
(18, 17, 3.7), (22, 17, 2.6), 
(19, 18, 2.0), (22, 18, 2.2), (23, 18, 2.8), 
(20, 19, 2.6), (23, 19, 3.4), (24, 19, 2.8), 
(21, 20, 3.5), (24, 20, 2.7), 
(24, 21, 4.0), (25, 21, 3.8), 
(23, 22, 2.5), (26, 22, 3.7), 
(24, 23, 4.2), (26, 23, 3.1), 
(25, 24, 1.9), (26, 24, 4.3), 
(26, 25, 6.8)
"""