# function.py
from gurobipy import *
from data import *

# 定义加割函数addBendersCuts
def addBendersCuts(m, d, d_obj, p, w, L, time_lb, time_ub, SP_min, SP_max, alpha):
    if d.status == GRB.Status.UNBOUNDED:  # 模型无界
        ray = d.UnbdRay
        m.addConstr(alpha * (quicksum(time_ub[i, j] * p[i, j] for i, j in L) -
                             quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j])
                                      * ray[i, j] for i, j in L)) + (1 - alpha) *
                    (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L)
                     - SP_min - SP_max) <= 0)
        print("*****加入可行割*****")
        d_obj.append(d_obj[-1])
    elif d.status == GRB.Status.OPTIMAL:
        m.addConstr(alpha * (quicksum(time_ub[i, j] * p[i, j] for i, j in L) -
                             quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j])
                                      * w[i, j].x for i, j in L)) + (1 - alpha) *
                    (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L)
                     - SP_min - SP_max) <= z)
        print("*****加入最优割*****")
        d_obj.append(d.objVal)
    else:
        print(d.status)

# 主问题和子问题的求解函数
def solve_benders(m, d, alpha, SP_min, SP_max, time_lb, time_ub, L, Lt, r, t, N_center, iteration_limit=8):
    z = m.addVar(vtype=GRB.CONTINUOUS, name='z')
    p = m.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='p')

    m.setObjective(z, GRB.MINIMIZE)

    w = d.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='w')

    m.addConstr(quicksum(p[i, j] for i, j in Lt.select(r, '*')) == 1)
    m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', r)) == 0)
    m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', t)) == 1)
    m.addConstr(quicksum(p[i, j] for i, j in Lt.select(t, '*')) == 0)

    for j in N_center:
        m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', j)) ==
                    quicksum(p[j, k] for j, k in Lt.select(j, '*')))

    d.addConstr(quicksum(w[i, j] for i, j in Lt.select(r, '*')) == 1)
    d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', r)) == 0)
    d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', t)) == 1)
    d.addConstr(quicksum(w[i, j] for i, j in Lt.select(t, '*')) == 0)

    for j in N_center:
        d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', j)) ==
                    quicksum(w[j, k] for j, k in Lt.select(j, '*')))

    d.Params.InfUnbdInfo = 1
    iteration = 0
    d_obj = [299]
    m_obj = []

    print("***第{}次求解主问题MP***".format(iteration + 1))
    m.optimize()
    m_obj.append(m.objVal)

    while iteration < iteration_limit:
        d.setObjective(alpha * (quicksum(time_ub[i, j] * p[i, j].x for i, j in L) -
                                quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j].x)
                                         * w[i, j] for i, j in L)) + (1 - alpha) *
                       (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j].x for i, j in L)
                        - SP_min - SP_max), GRB.MAXIMIZE)
        print("***第{}次求解子问题对偶问题SP_dual***".format(iteration + 1))
        d.optimize()
        addBendersCuts(m, d, d_obj, p, w, L, time_lb, time_ub, SP_min, SP_max, alpha)

        print("***第{}次求解主问题MP***".format(iteration + 1))
        m.optimize()
        m_obj.append(m.objVal)
        iteration += 1

    return m_obj, d_obj
