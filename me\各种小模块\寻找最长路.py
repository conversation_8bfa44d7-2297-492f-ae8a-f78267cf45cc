import collections

# 输入边集合
edges = [
    (1, 2, 16), (1, 3, 12), (2, 3, 17), (1, 4, 14), (3, 4, 16), (1, 5, 10), (4, 5, 7),
    (2, 6, 16), (3, 6, 16), (3, 7, 14), (6, 7, 16), (3, 8, 16), (4, 8, 14), (7, 8, 16),
    (4, 9, 12), (8, 9, 16), (4, 10, 16), (5, 10, 13), (9, 10, 16), (6, 11, 16), (7, 11, 16),
    (7, 12, 14), (11, 12, 16), (7, 13, 16), (8, 13, 14), (12, 13, 16), (8, 14, 16), (9, 14, 14),
    (13, 14, 16), (9, 15, 12), (10, 15, 18), (14, 15, 16), (10, 16, 13), (15, 16, 16),
    (11, 17, 16),
    (12, 17, 16), (12, 18, 14), (13, 18, 14), (17, 18, 16), (13, 19, 16), (14, 19, 14),
    (18, 19, 16),
    (14, 20, 16), (15, 20, 13), (19, 20, 16), (15, 21, 16), (16, 21, 13), (20, 21, 16),
    (17, 22, 16),
    (18, 22, 16), (18, 23, 14), (19, 23, 14), (22, 23, 16), (19, 24, 14), (20, 24, 15),
    (23, 24, 16),
    (20, 25, 16), (21, 25, 14), (24, 25, 16), (22, 26, 16), (23, 26, 16), (23, 27, 14),
    (24, 27, 13),
    (26, 27, 7), (24, 28, 16), (25, 28, 13), (27, 28, 16), (26, 29, 18), (27, 29, 8),
    (28, 29, 16),
    (2, 1, 16), (3, 1, 12), (3, 2, 17), (4, 1, 14), (4, 3, 16), (5, 1, 10), (5, 4, 7),
    (6, 2, 16), (6, 3, 16), (7, 3, 14), (7, 6, 16), (8, 3, 16), (8, 4, 14), (8, 7, 16),
    (9, 4, 12), (9, 8, 16), (10, 4, 16), (10, 5, 13), (10, 9, 16), (11, 6, 16), (11, 7, 16),
    (12, 7, 14), (12, 11, 16), (13, 7, 16), (13, 8, 14), (13, 12, 16), (14, 8, 16), (14, 9, 14),
    (14, 13, 16), (15, 9, 12), (15, 10, 18), (15, 14, 16), (16, 10, 13), (16, 15, 16),
    (17, 11, 16),
    (17, 12, 16), (18, 12, 14), (18, 13, 14), (18, 17, 16), (19, 13, 16), (19, 14, 14),
    (19, 18, 16),
    (20, 14, 16), (20, 15, 13), (20, 19, 16), (21, 15, 16), (21, 16, 13), (21, 20, 16),
    (22, 17, 16),
    (22, 18, 16), (23, 18, 14), (23, 19, 14), (23, 22, 16), (24, 19, 14), (24, 20, 15),
    (24, 23, 16),
    (25, 20, 16), (25, 21, 14), (25, 24, 16), (26, 22, 16), (26, 23, 16), (27, 23, 14),
    (27, 24, 13),
    (27, 26, 7), (28, 24, 16), (28, 25, 13), (28, 27, 16), (29, 26, 18), (29, 27, 8),
    (29, 28, 16)
]

# 构建图
graph = collections.defaultdict(list)
for u, v, w in edges:                            # 最主要是这个地方，要与上面的数据形式对应
    graph[u].append((v, w))


# 动态规划求解
def max_flow_path(graph, start, end, max_nodes):
    # dp[i][k]：从起点到 i 经过 k 个节点的最大流量
    dp = collections.defaultdict(lambda: -float('inf'))
    dp[(start, 1)] = 0  # 起点初始化
    path = collections.defaultdict(list)  # 保存路径

    for k in range(2, max_nodes + 1):  # 逐步增加节点数限制
        for u in graph:
            for v, w in graph[u]:
                # 判断v是否已经在到达u且经过k-1个节点的路径中出现过
                if v not in path[(u, k - 1)]:                     # 判断每个节点最多经过一次
                    if dp[(u, k - 1)] + w > dp[(v, k)]:
                        dp[(v, k)] = dp[(u, k - 1)] + w
                        path[(v, k)] = path[(u, k - 1)] + [u]

    # 输出结果
    max_flow = max(dp[(end, k)] for k in range(1, max_nodes + 1))
    best_path = []
    for k in range(1, max_nodes + 1):
        if dp[(end, k)] == max_flow:
            best_path = path[(end, k)] + [end]
            break

    return max_flow, best_path


# 求解最大流路径
start, end, max_nodes = 1, 29, 100
max_flow, best_path = max_flow_path(graph, start, end, max_nodes)

print("最大流量:", max_flow)
print("最佳路径:", best_path)