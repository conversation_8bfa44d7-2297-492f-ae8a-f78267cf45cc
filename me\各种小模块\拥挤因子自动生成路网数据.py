import numpy as np


def generate_time_dependent_data(edges, l_base, u_base, congestion_factors):
    """
    生成不同时段的时间依赖路网数据。
    :param edges: 路段 (i, j) 列表
    :param l_base: 每个路段的基础下界阻抗
    :param u_base: 每个路段的基础上界阻抗
    :param congestion_factors: 不同时段的 (f(t), g(t)) 值
    :return: 包含不同时间段的 l(t), u(t)
    """
    time_dependent_data = {}

    for period, (f_t, g_t) in congestion_factors.items():
        l_t = [round(l * (1 + f_t), 2) for l in l_base]
        u_t = [round(u * (1 + g_t), 2) for u in u_base]
        time_dependent_data[period] = list(zip(edges, l_t, u_t))

    return time_dependent_data


# 路网基础数据
edges = [(1, 2), (2, 1), (1, 3), (3, 1), (1, 4), (4, 1), (1, 5), (5, 1),
         (2, 3), (3, 2), (2, 6), (6, 2), (2, 7), (7, 2), (3, 4), (4, 3),
         (3, 7), (7, 3), (3, 8), (8, 3), (4, 5), (5, 4), (4, 8), (8, 4),
         (4, 9), (9, 4), (5, 9), (9, 5), (5, 10), (10, 5), (6, 7), (7, 6),
         (6, 11), (11, 6), (7, 8), (8, 7), (7, 11), (11, 7), (7, 12), (12, 7),
         (8, 9), (9, 8), (8, 12), (12, 8), (8, 13), (13, 8), (9, 10), (10, 9),
         (9, 13), (13, 9), (9, 14), (14, 9), (10, 14), (14, 10), (10, 15), (15, 10),
         (11, 12), (12, 11), (11, 16), (16, 11), (11, 17), (17, 11), (12, 13), (13, 12),
         (12, 17), (17, 12), (12, 18), (18, 12), (13, 14), (14, 13), (13, 18), (18, 13),
         (13, 19), (19, 13), (14, 15), (15, 14), (14, 19), (19, 14), (15, 19), (19, 15),
         (16, 17), (17, 16), (16, 20), (20, 16), (17, 18), (18, 17), (17, 20), (20, 17),
         (18, 19), (19, 18), (18, 20), (20, 18), (19, 20), (20, 19)]

l_base = [9, 9, 7, 7, 6, 6, 10, 10, 6, 6,
          12, 12, 10, 10, 11, 11, 8, 8, 10, 10,
          9, 9, 7, 7, 6, 6, 7, 7, 9, 9,
          8, 8, 6, 6, 9, 9, 8, 8, 6, 6,
          8, 8, 11, 11, 6, 6, 10, 10, 11, 11,
          6, 6, 10, 10, 10, 10, 8, 8, 6, 6,
          7, 7, 8, 8, 6, 6, 9, 9, 9, 9,
          10, 10, 6, 6, 7, 7, 8, 8, 6, 6,
          9, 9, 7, 10, 8, 8, 6, 6, 9, 9,  # 补充缺失的6个值
          7, 8, 9, 10]

u_base = [14, 14, 13, 13, 12, 12, 15, 15,
          11, 11, 15, 15, 14, 14, 15, 15,
          12, 12, 15, 15, 14, 14, 13, 13,
          12, 12, 12, 12, 15, 15, 14, 14,
          12, 12, 14, 14, 14, 14, 11, 11,
          12, 12, 15, 15, 11, 11, 14, 14,
          15, 15, 11, 11, 14, 14, 14, 14,
          12, 12, 11, 11, 15, 15, 13, 13,
          11, 11, 15, 15, 15, 15, 13, 13,
          11, 11, 12, 12, 15, 15, 10, 10,
          15, 15, 14, 14, 15, 15, 12, 12,
          15, 15, 13, 14, 15, 15]

# 不同时段的 (f(t), g(t))
congestion_factors = {
    "00:00-07:00": (-0.5, -0.1),
    "07:00-09:00": (0.5, 1.1),
    "09:00-17:00": (0, 0),
    "17:00-19:00": (0.6, 0.9),
    "19:00-00:00": (0.08, 0.1)
}

# 生成不同时段数据
time_dependent_data = generate_time_dependent_data(edges, l_base, u_base, congestion_factors)

# 打印示例
for period, values in time_dependent_data.items():
    print(f"{period}:")
    for edge, l_t, u_t in values[:94]:  # 打印前94条
        print(f"  Edge {edge}: l(t)={l_t}, u(t)={u_t}")
    print("...")
