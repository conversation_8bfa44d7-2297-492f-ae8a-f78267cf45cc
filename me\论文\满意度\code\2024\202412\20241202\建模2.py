

# 失败


import gurobipy as gp
from gurobipy import GRB
import networkx as nx

# 数据初始化
edges_data = [
    (1, 2, 0.2, 4), (1, 3, 1, 2), (1, 4, 0.9, 1.8), (1, 5, 0.2, 2.1),
    (2, 3, 0.9, 1.7), (2, 6, 0.8, 2.1), (2, 11, 1.5, 3.2),
    (3, 4, 1.1, 1.7), (3, 6, 1.5, 2.1), (3, 7, 0.8, 1.5),
    (4, 5, 0.1, 1.8), (4, 7, 1.1, 1.3), (4, 8, 0.5, 1.3), (4, 9, 1.1, 2.4),
    (5, 9, 0.9, 1.5), (5, 10, 0.5, 1.5),
    (6, 7, 0.7, 1.2), (6, 11, 0.7, 2.1), (6, 12, 1.4, 2.3), (6, 13, 0.9, 1),
    (7, 8, 0.2, 1.5), (7, 13, 0.4, 2.5),
    (8, 9, 0.6, 1.6), (8, 13, 1.1, 3.5), (8, 14, 0.6, 1.7),
    (9, 10, 0.7, 1.4), (9, 14, 0.7, 3), (9, 15, 1.2, 1.8),
    (10, 15, 1.1, 1.7), (10, 16, 1, 2.4),
    (11, 12, 0.7, 2.1),
    (12, 13, 0.3, 1.5), (12, 17, 0.4, 1.9),
    (13, 14, 1.2, 1.9), (13, 17, 1.5, 3.2), (13, 18, 0.9, 1.9),
    (14, 15, 0.5, 2.5), (14, 18, 0.9, 2.5), (14, 19, 0.6, 2.1),
    (15, 16, 1.1, 1.6), (15, 19, 1, 2.8), (15, 20, 1, 1.7),
    (16, 20, 1.2, 2.7), (16, 21, 0.8, 2.2),
    (17, 18, 1.2, 2.5), (17, 22, 1, 1.6),
    (18, 19, 0.5, 1.5), (18, 22, 0.5, 1.7), (18, 23, 0.6, 2.2),
    (19, 20, 0.3, 2.3), (19, 23, 0.9, 2.5), (19, 24, 0.2, 2.6),
    (20, 21, 1.1, 2.4), (20, 24, 0.5, 2.2),
    (21, 24, 1.2, 2.8), (21, 25, 1.1, 2.7),
    (22, 23, 0.7, 1.8), (22, 26, 0.9, 2.8),
    (23, 24, 2, 2.2), (23, 26, 0.8, 2.3),
    (24, 25, 0.5, 1.4), (24, 26, 0.5, 3.8),
    (25, 26, 2.4, 4.4)
]

# 构建网络图
G = nx.DiGraph()
for u, v, l, u_bound in edges_data:
    G.add_edge(u, v, lower=l, upper=u_bound)

# 创建Gurobi模型
model = gp.Model("RobustShortestPath")

# 定义变量
p = model.addVars(G.edges, vtype=GRB.BINARY, name="p")  # p[i, j]: 是否在鲁棒最短路径中
x_rt = model.addVars(G.nodes, vtype=GRB.CONTINUOUS, name="x_rt", lb=0)  # 从起点到每个节点的最短路径阻抗

# 定义目标函数
model.setObjective(
    gp.quicksum(G.edges[e]['upper'] * p[e] for e in G.edges) - gp.quicksum(x_rt[node] for node in G.nodes),
    GRB.MINIMIZE
)

# 起点和终点
source = 1
target = 26

# 1. 最短路径约束：动态更新最短路径阻抗
# x[r,j] <= x[r,i] + l[i,j] + (u[i,j] - l[i,j]) * p[i,j]
for i, j in G.edges:
    model.addConstr(
        x_rt[j] <= x_rt[i] + G.edges[i, j]['lower'] + (G.edges[i, j]['upper'] - G.edges[i, j]['lower']) * p[i, j],
        name=f"shortest_path_constraint_{i}_{j}"
    )

# x[r, r] = 0 (起点到起点的阻抗为0)
model.addConstr(x_rt[source] == 0, name="source_to_source_zero")

# x[r, j] >= 0 (路径阻抗非负)
for j in G.nodes:
    model.addConstr(x_rt[j] >= 0, name=f"non_negative_x_{j}")

# 2. 流量守恒约束
# 起点的流量守恒约束：流入 - 流出 = -1
model.addConstr(
    gp.quicksum(p[i, source] for i in G.predecessors(source)) - gp.quicksum(p[source, j] for j in G.successors(source)) == -1,
    name="flow_conservation_source"
)

# 终点的流量守恒约束：流入 - 流出 = 1
model.addConstr(
    gp.quicksum(p[i, target] for i in G.predecessors(target)) - gp.quicksum(p[target, j] for j in G.successors(target)) == 1,
    name="flow_conservation_target"
)

# 其他节点的流量守恒约束：流入 - 流出 = 0
for node in G.nodes:
    if node != source and node != target:
        model.addConstr(
            gp.quicksum(p[i, node] for i in G.predecessors(node)) - gp.quicksum(p[node, j] for j in G.successors(node)) == 0,
            name=f"flow_conservation_{node}"
        )



# 求解模型
model.optimize()

# 输出结果
if model.status == GRB.OPTIMAL:
    robust_path = [e for e in G.edges if p[e].x > 0.5]
    print("鲁棒最短路径:", robust_path)
    print("鲁棒成本:", sum(G.edges[e]['upper'] * p[e].x for e in G.edges))
else:
    print("未找到最优解")
