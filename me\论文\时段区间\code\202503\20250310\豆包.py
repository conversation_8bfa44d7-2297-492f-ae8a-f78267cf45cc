import heapq

# 生成从起点到终点的所有路径
def find_all_paths(graph, start, end, path=[]):
    path = path + [start]
    if start == end:
        return [path]
    if start not in graph:
        return []
    paths = []
    for node in graph[start]:
        if node not in path:
            new_paths = find_all_paths(graph, node, end, path)
            for p in new_paths:
                paths.append(p)
    return paths

# Dijkstra 算法，用于计算最短路径
def dijkstra(graph, start, end, impedance, start_time):
    dist = {node: float('inf') for node in graph}
    dist[start] = 0
    pq = [(0, start, start_time)]
    while pq:
        current_dist, current_node, current_time = heapq.heappop(pq)
        if current_dist > dist[current_node]:
            continue
        for neighbor in graph[current_node]:
            # 根据当前时间确定时段
            if 0 <= current_time < 480:
                period = 0
            elif 480 <= current_time < 960:
                period = 1
            else:
                period = 2
            edge_impedance = impedance[(current_node, neighbor)][period][0]  # 取下界
            new_dist = dist[current_node] + edge_impedance
            new_time = current_time + edge_impedance
            if new_dist < dist[neighbor]:
                dist[neighbor] = new_dist
                heapq.heappush(pq, (new_dist, neighbor, new_time))
    return dist[end]

# 计算路径的鲁棒成本
def calculate_robust_cost(graph, path, impedance, start_time):
    current_time = start_time
    # 计算路径上路段的区间阻抗上界之和 a
    a = 0
    for i in range(len(path) - 1):
        u, v = path[i], path[i + 1]
        # 根据当前时间确定时段
        if 0 <= current_time < 480:
            period = 0
        elif 480 <= current_time < 960:
            period = 1
        else:
            period = 2
        a += impedance[(u, v)][period][1]  # 上界
        current_time += impedance[(u, v)][period][1]

    # 构建新的图，将不在路径上的路段阻抗设置为下界
    new_impedance = {}
    for u in graph:
        for v in graph[u]:
            if (u, v) in [(path[i], path[i + 1]) for i in range(len(path) - 1)]:
                new_impedance[(u, v)] = impedance[(u, v)][period][1]
            else:
                # 根据开始时间确定时段
                if 0 <= start_time < 480:
                    period = 0
                elif 480 <= start_time < 960:
                    period = 1
                else:
                    period = 2
                new_impedance[(u, v)] = impedance[(u, v)][period][0]  # 下界

    # 计算下界最短路长度 b
    b = dijkstra(graph, path[0], path[-1], new_impedance, start_time)

    # 计算鲁棒成本
    return a - b

# 主函数，找到最小鲁棒成本的路径
def find_min_robust_path(graph, start, end, impedance, start_time):
    all_paths = find_all_paths(graph, start, end)
    min_robust_cost = float('inf')
    min_robust_path = None
    for path in all_paths:
        robust_cost = calculate_robust_cost(graph, path, impedance, start_time)
        if robust_cost < min_robust_cost:
            min_robust_cost = robust_cost
            min_robust_path = path
    return min_robust_path, min_robust_cost

# 示例数据
num_nodes = 29
edges = [(i, j) for i in range(1, num_nodes + 1) for j in range(i + 1, num_nodes + 1)][:70]
# 每条边的三个时段的区间阻抗，格式为 [(下界, 上界), (下界, 上界), (下界, 上界)]
impedance = {edge: [(1, 2), (2, 3), (3, 4)] for edge in edges}

# 构建图的邻接表表示
graph = {i: [] for i in range(1, num_nodes + 1)}
for u, v in edges:
    graph[u].append(v)
    graph[v].append(u)

# 设定开始时间
start_time = 470
start = 1
end = 29

# 找到最小鲁棒成本的路径
min_robust_path, min_robust_cost = find_min_robust_path(graph, start, end, impedance, start_time)

print("最小鲁棒成本的路径:", min_robust_path)
print("最小鲁棒成本:", min_robust_cost)