
# 半成功


import networkx as nx

def robust_shortest_path(G, r, t):
    # 初始化最佳路径及其成本
    best_path = None
    best_cost = float('inf')

    # 遍历所有简单路径
    for path in nx.all_simple_paths(G, source=r, target=t):
        cost = robust_cost(G, path, r, t)
        if cost < best_cost:
            best_cost = cost
            best_path = path

    return best_path if best_path is not None else "无可行路径"

def robust_cost(G, path, r, t):
    # 计算路径的鲁棒成本
    cost = sum(G[path[i]][path[i + 1]]['upper_bound'] for i in range(len(path) - 1))

    # 移除路径对应的边并计算替代最短路径成本
    G_copy = G.copy()
    G_copy.remove_edges_from([(path[i], path[i + 1]) for i in range(len(path) - 1)])

    try:
        shortest_path_cost = nx.dijkstra_path_length(G_copy, r, t, weight='lower_bound')
        cost -= shortest_path_cost
    except nx.NetworkXNoPath:
        cost = float('inf')  # 如果无连通路径，设置为无穷大

    return cost

# 创建有向图
G = nx.DiGraph()
edges = [
    (1, 2, 0.2, 4), (1, 3, 1, 2), (1, 4, 0.9, 1.8), (1, 5, 0.2, 2.1),
    (2, 3, 0.9, 1.7), (2, 6, 0.8, 2.1), (2, 11, 1.5, 3.2),
    (3, 4, 1.1, 1.7), (3, 6, 1.5, 2.1), (3, 7, 0.8, 1.5),
    (4, 5, 0.1, 1.8), (4, 7, 1.1, 1.3), (4, 8, 0.5, 1.3), (4, 9, 1.1, 2.4),
    (5, 9, 0.9, 1.5), (5, 10, 0.5, 1.5),
    (6, 7, 0.7, 1.2), (6, 11, 0.7, 2.1), (6, 12, 1.4, 2.3), (6, 13, 0.9, 1),
    (7, 8, 0.2, 1.5), (7, 13, 0.4, 2.5),
    (8, 9, 0.6, 1.6), (8, 13, 1.1, 3.5), (8, 14, 0.6, 1.7),
    (9, 10, 0.7, 1.4), (9, 14, 0.7, 3), (9, 15, 1.2, 1.8),
    (10, 15, 1.1, 1.7), (10, 16, 1, 2.4),
    (11, 12, 0.7, 2.1),
    (12, 13, 0.3, 1.5), (12, 17, 0.4, 1.9),
    (13, 14, 1.2, 1.9), (13, 17, 1.5, 3.2), (13, 18, 0.9, 1.9),
    (14, 15, 0.5, 2.5), (14, 18, 0.9, 2.5), (14, 19, 0.6, 2.1),
    (15, 16, 1.1, 1.6), (15, 19, 1, 2.8), (15, 20, 1, 1.7),
    (16, 20, 1.2, 2.7), (16, 21, 0.8, 2.2),
    (17, 18, 1.2, 2.5), (17, 22, 1, 1.6),
    (18, 19, 0.5, 1.5), (18, 22, 0.5, 1.7), (18, 23, 0.6, 2.2),
    (19, 20, 0.3, 2.3), (19, 23, 0.9, 2.5), (19, 24, 0.2, 2.6),
    (20, 21, 1.1, 2.4), (20, 24, 0.5, 2.2),
    (21, 24, 1.2, 2.8), (21, 25, 1.1, 2.7),
    (22, 23, 0.7, 1.8), (22, 26, 0.9, 2.8),
    (23, 24, 2, 2.2), (23, 26, 0.8, 2.3),
    (24, 25, 0.5, 1.4), (24, 26, 0.5, 3.8),
    (25, 26, 2.4, 4.4),
]
for u, v, lb, ub in edges:
    G.add_edge(u, v, lower_bound=lb, upper_bound=ub)

# 起点和终点
r, t = 1, 26

# 计算鲁棒最短路径
robust_path = robust_shortest_path(G, r, t)
print("鲁棒最短路径:", robust_path)
