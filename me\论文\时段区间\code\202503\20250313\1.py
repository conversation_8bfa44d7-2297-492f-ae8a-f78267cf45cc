import heapq
from collections import defaultdict, deque
import multiprocessing
import time
import matplotlib.pyplot as plt

# RoadNetwork类保持不变，省略以节省空间
class RoadNetwork:
    def __init__(self, network_data):
        self.graph = defaultdict(list)
        self._initialize_graph(network_data)
        self.robust_cost_cache = {}  # 缓存鲁棒成本计算结果

    def _initialize_graph(self, network_data):
        # 从输入数据初始化图
        for source, neighbors in network_data.items():
            for target, time_ranges in neighbors.items():
                self.graph[source].append((target, time_ranges))

    def get_impedance(self, impedance_ranges, time):
        # 根据时间选择合适的阻抗
        for start, end, low, high in impedance_ranges:
            if start <= time < end:
                return low, high
        return None

    def dijkstra(self, start, end, time, lower_bound=True):
        # Dijkstra 算法，用于计算最短路径
        priority_queue = [(0, start, time)]
        dist = {start: 0}
        prev = {start: None}

        while priority_queue:
            current_dist, current_node, current_time = heapq.heappop(priority_queue)

            if current_node == end:
                path = []
                while current_node is not None:
                    path.append(current_node)
                    current_node = prev[current_node]
                path.reverse()
                return path, current_dist

            if current_dist > dist.get(current_node, float('inf')):
                continue

            for neighbor, impedance_ranges in self.graph[current_node]:
                impedance = self.get_impedance(impedance_ranges, current_time)
                if impedance is None:
                    continue
                if lower_bound:
                    cost = impedance[0]
                else:
                    cost = impedance[1]

                new_dist = current_dist + cost
                new_time = current_time + cost
                if new_time >= 1440:
                    new_time %= 1440

                if new_dist < dist.get(neighbor, float('inf')):
                    dist[neighbor] = new_dist
                    prev[neighbor] = current_node
                    heapq.heappush(priority_queue, (new_dist, neighbor, new_time))

        return None, float('inf')

    def calculate_robust_cost(self, path, start_time):
        key = (tuple(path), start_time)
        if key in self.robust_cost_cache:
            return self.robust_cost_cache[key]

        # 计算路径的鲁棒成本
        # 计算原路径最坏时间
        a = 0
        current_time = start_time
        for i in range(len(path) - 1):
            u = path[i]
            v = path[i + 1]
            for neighbor, impedance_ranges in self.graph[u]:
                if neighbor == v:
                    impedance = self.get_impedance(impedance_ranges, current_time)
                    if impedance is None:
                        continue
                    a += impedance[1]
                    current_time += impedance[1]
                    if current_time >= 1440:
                        current_time %= 1440
                    break

        # 计算备选路径最佳时间和路径
        remaining_graph = defaultdict(list)
        for u in self.graph:
            for v, impedance_ranges in self.graph[u]:
                if (u, v) not in zip(path[:-1], path[1:]):
                    remaining_graph[u].append((v, impedance_ranges))

        remaining_network = RoadNetwork({})
        remaining_network.graph = remaining_graph
        alternative_path, b = remaining_network.dijkstra(path[0], path[-1], start_time, lower_bound=True)

        robust_cost = a - b
        result = (robust_cost, a, b, alternative_path)
        self.robust_cost_cache[key] = result
        return result

    def find_min_robust_path(self, start, end, start_time):
        # 找到最小鲁棒成本的路径
        all_paths = []
        priority_queue = [(0, deque([start]), start_time)]
        min_robust_cost = float('inf')

        while priority_queue:
            current_dist, current_path, current_time = heapq.heappop(priority_queue)
            current_node = current_path[-1]

            # 剪枝：如果当前路径成本已超过最小鲁棒成本，跳过
            if current_dist >= min_robust_cost:
                continue

            if current_node == end:
                path_list = list(current_path)
                all_paths.append(path_list)
                continue

            for neighbor, impedance_ranges in self.graph[current_node]:
                if neighbor not in current_path:
                    impedance = self.get_impedance(impedance_ranges, current_time)
                    if impedance is None:
                        continue
                    new_dist = current_dist + impedance[0]
                    new_time = current_time + impedance[0]
                    if new_time >= 1440:
                        new_time %= 1440
                    new_path = current_path.copy()
                    new_path.append(neighbor)
                    heapq.heappush(priority_queue, (new_dist, new_path, new_time))

        min_robust_path = None
        worst_case_time = 0
        best_case_time = 0
        alternative_path = None

        # 并行计算鲁棒成本
        pool = multiprocessing.Pool()
        results = pool.map(calculate_robust_cost, [(self, path, start_time) for path in all_paths])
        pool.close()
        pool.join()

        for path, (robust_cost, a, b, alt_path) in zip(all_paths, results):
            if robust_cost < min_robust_cost:
                min_robust_cost = robust_cost
                min_robust_path = path
                worst_case_time = a
                best_case_time = b
                alternative_path = alt_path

        return min_robust_path, min_robust_cost, worst_case_time, best_case_time, alternative_path

network_data = {
  "1": {
    "2": [[0, 480, 2, 10], [480, 600, 5, 15], [600, 1440, 3, 20]],
    "3": [[0, 480, 4, 12], [480, 600, 6, 16], [600, 1440, 5, 18]],
    "4": [[0, 480, 3, 11], [480, 600, 7, 17], [600, 1440, 4, 19]],
    "5": [[0, 480, 5, 13], [480, 600, 8, 18], [600, 1440, 6, 20]]
  },
  "2": {
    "1": [[0, 480, 2, 10], [480, 600, 5, 15], [600, 1440, 3, 20]],
    "3": [[0, 480, 3, 9], [480, 600, 7, 14], [600, 1440, 4, 12]],
    "6": [[0, 480, 6, 14], [480, 600, 9, 17], [600, 1440, 7, 19]],
    "7": [[0, 480, 4, 11], [480, 600, 6, 15], [600, 1440, 5, 13]]
  },
  "3": {
    "1": [[0, 480, 4, 12], [480, 600, 6, 16], [600, 1440, 5, 18]],
    "2": [[0, 480, 3, 9], [480, 600, 7, 14], [600, 1440, 4, 12]],
    "4": [[0, 480, 5, 13], [480, 600, 8, 16], [600, 1440, 6, 17]],
    "7": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "8": [[0, 480, 4, 11], [480, 600, 6, 14], [600, 1440, 5, 13]]
  },
  "4": {
    "1": [[0, 480, 3, 11], [480, 600, 7, 17], [600, 1440, 4, 19]],
    "3": [[0, 480, 5, 13], [480, 600, 8, 16], [600, 1440, 6, 17]],
    "5": [[0, 480, 6, 14], [480, 600, 9, 18], [600, 1440, 7, 20]],
    "8": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "9": [[0, 480, 5, 12], [480, 600, 7, 15], [600, 1440, 6, 14]]
  },
  "5": {
    "1": [[0, 480, 5, 13], [480, 600, 8, 18], [600, 1440, 6, 20]],
    "4": [[0, 480, 6, 14], [480, 600, 9, 18], [600, 1440, 7, 20]],
    "9": [[0, 480, 7, 15], [480, 600, 10, 19], [600, 1440, 8, 21]],
    "10": [[0, 480, 6, 13], [480, 600, 8, 16], [600, 1440, 7, 17]]
  },
  "6": {
    "2": [[0, 480, 6, 14], [480, 600, 9, 17], [600, 1440, 7, 19]],
    "7": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "11": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "7": {
    "2": [[0, 480, 4, 11], [480, 600, 6, 15], [600, 1440, 5, 13]],
    "3": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "6": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "8": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "11": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "12": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "8": {
    "3": [[0, 480, 4, 11], [480, 600, 6, 14], [600, 1440, 5, 13]],
    "4": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "7": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "9": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "12": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "13": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "9": {
    "4": [[0, 480, 5, 12], [480, 600, 7, 15], [600, 1440, 6, 14]],
    "5": [[0, 480, 7, 15], [480, 600, 10, 19], [600, 1440, 8, 21]],
    "8": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "10": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "13": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "14": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "10": {
    "5": [[0, 480, 6, 13], [480, 600, 8, 16], [600, 1440, 7, 17]],
    "9": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "14": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "15": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "11": {
    "6": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "7": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "12": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "16": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "17": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "12": {
    "7": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "8": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "11": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "13": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "17": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "18": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "13": {
    "8": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "9": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "12": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "14": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "18": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "19": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "14": {
    "9": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "10": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "13": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "15": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "19": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "15": {
    "10": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "14": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "19": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "16": {
    "11": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "17": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "20": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "17": {
    "11": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "12": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "16": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "18": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "20": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "18": {
    "12": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "13": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "17": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "19": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "20": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "19": {
    "13": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "14": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "15": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "18": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "20": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]]
  },
  "20": {
    "16": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "17": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "18": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "19": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]]
  }
}

if __name__ == '__main__':
    start_time_total = time.time()
    network = RoadNetwork(network_data)
    start = '1'
    end = '20'
    start_time = 470

    # 获取路径结果并收集数据
    (min_robust_path, min_robust_cost,
     worst_case_time, best_case_time,
     alternative_path) = network.find_min_robust_path(start, end, start_time)

    # 提取绘图数据
    all_results = network.all_results
    robust_costs = [x[1] for x in all_results]
    path_lengths = [len(x[0]) for x in all_results]
    a_values = [x[2] for x in all_results]
    b_values = [x[3] for x in all_results]

    # 创建三个子图的画布
    plt.figure(figsize=(18, 6))

    # 子图1：鲁棒成本分布
    plt.subplot(1, 3, 1)
    plt.hist(robust_costs, bins=15, edgecolor='black', alpha=0.7)
    plt.axvline(min_robust_cost, color='r', linestyle='--')
    plt.title('鲁棒成本分布')
    plt.xlabel('鲁棒成本')
    plt.ylabel('频次')

    # 子图2：成本趋势分析
    plt.subplot(1, 3, 2)
    sorted_indices = sorted(range(len(robust_costs)), key=lambda k: robust_costs[k])
    sorted_costs = [robust_costs[i] for i in sorted_indices]
    plt.plot(sorted_costs, 'b-', marker='o')
    plt.axhline(min_robust_cost, color='r', linestyle='--')
    plt.title('成本优化趋势')
    plt.xlabel('候选路径排序')
    plt.ylabel('鲁棒成本')

    # 子图3：时空关系分析
    plt.subplot(1, 3, 3)
    scatter = plt.scatter(a_values, b_values, c=robust_costs,
                        cmap='viridis', alpha=0.6, s=100)
    plt.colorbar(scatter, label='鲁棒成本')
    plt.plot([min(a_values), max(a_values)],
            [min(a_values), max(a_values)],
            'r--', alpha=0.3)
    plt.title('时空关系分析')
    plt.xlabel('原路径最坏时间')
    plt.ylabel('备选路径最佳时间')

    plt.tight_layout()
    plt.show()

    # 文本输出保持不变
    print("\n最小鲁棒成本的路径:", min_robust_path)
    print("最小鲁棒成本:", min_robust_cost)
    print("原路径最坏时间:", worst_case_time)
    print("备选路径最佳时间:", best_case_time)
    print("备选路径:", alternative_path)
    print("总计算时间:", round(time.time() - start_time_total, 2), "秒")