from ortools.linear_solver import pywraplp
import networkx as nx
from gurobipy import *
import matplotlib.pyplot as plt

# 创建求解器
solver = pywraplp.Solver.CreateSolver('SCIP')

r = 1  # 起点
t = 29  # 终点
N = [i for i in range(1, 30)]
N_rt = [r, t]
N_center = N.copy()
for k in [r, t]:
    N_center.remove(k)
Links = [(1, 2, 6, 10), (1, 3, 6, 8), (2, 3, 6, 10), (1, 4, 5, 9), (3, 4, 6, 10), (1, 5, 3, 5), (4, 5, 1, 4),
         (2, 6, 6, 10), (3, 6, 6, 10), (3, 7, 6, 8), (6, 7, 6, 10), (3, 8, 6, 10), (4, 8, 6, 8), (7, 8, 6, 10),
         (4, 9, 3, 9), (8, 9, 6, 10), (4, 10, 6, 10), (5, 10, 3, 10), (9, 10, 6, 10), (6, 11, 6, 10), (7, 11, 6, 10),
         (7, 12, 6, 8), (11, 12, 6, 10), (7, 13, 6, 10), (8, 13, 6, 8), (12, 13, 6, 10), (8, 14, 6, 10), (9, 14, 6, 8),
         (13, 14, 6, 10), (9, 15, 3, 9), (10, 15, 6, 12), (14, 15, 6, 10), (10, 16, 3, 10), (15, 16, 6, 10),
         (11, 17, 6, 10),
         (12, 17, 6, 10), (12, 18, 6, 8), (13, 18, 6, 8), (17, 18, 6, 10), (13, 19, 6, 10), (14, 19, 6, 8),
         (18, 19, 6, 10),
         (14, 20, 6, 10), (15, 20, 4, 9), (19, 20, 6, 10), (15, 21, 6, 10), (16, 21, 3, 10), (20, 21, 6, 10),
         (17, 22, 6, 10),
         (18, 22, 6, 10), (18, 23, 6, 8), (19, 23, 6, 8), (22, 23, 6, 10), (19, 24, 6, 8), (20, 24, 5, 10),
         (23, 24, 6, 10),
         (20, 25, 6, 10), (21, 25, 3, 11), (24, 25, 6, 10), (22, 26, 6, 10), (23, 26, 6, 10), (23, 27, 6, 8),
         (24, 27, 4, 9),
         (26, 27, 2, 3), (24, 28, 6, 10), (25, 28, 3, 10), (27, 28, 6, 10), (26, 29, 5, 13), (27, 29, 2, 4),
         (28, 29, 3, 13),
         (2, 1, 6, 10), (3, 1, 6, 8), (3, 2, 6, 10), (4, 1, 5, 9), (4, 3, 6, 10), (5, 1, 3, 5), (5, 4, 1, 4),
         (6, 2, 6, 10), (6, 3, 6, 10), (7, 3, 6, 8), (7, 6, 6, 10), (8, 3, 6, 10), (8, 4, 6, 8), (8, 7, 6, 10),
         (9, 4, 3, 9), (9, 8, 6, 10), (10, 4, 6, 10), (10, 5, 3, 10), (10, 9, 6, 10), (11, 6, 6, 10), (11, 7, 6, 10),
         (12, 7, 6, 8), (12, 11, 6, 10), (13, 7, 6, 10), (13, 8, 6, 8), (13, 12, 6, 10), (14, 8, 6, 10), (14, 9, 6, 8),
         (14, 13, 6, 10), (15, 9, 3, 9), (15, 10, 6, 12), (15, 14, 6, 10), (16, 10, 3, 10), (16, 15, 6, 10),
         (17, 11, 6, 10),
         (17, 12, 6, 10), (18, 12, 6, 8), (18, 13, 6, 8), (18, 17, 6, 10), (19, 13, 6, 10), (19, 14, 6, 8),
         (19, 18, 6, 10),
         (20, 14, 6, 10), (20, 15, 4, 9), (20, 19, 6, 10), (21, 15, 6, 10), (21, 16, 3, 10), (21, 20, 6, 10),
         (22, 17, 6, 10),
         (22, 18, 6, 10), (23, 18, 6, 8), (23, 19, 6, 8), (23, 22, 6, 10), (24, 19, 6, 8), (24, 20, 5, 10),
         (24, 23, 6, 10),
         (25, 20, 6, 10), (25, 21, 3, 11), (25, 24, 6, 10), (26, 22, 6, 10), (26, 23, 6, 10), (27, 23, 6, 8),
         (27, 24, 4, 9),
         (27, 26, 2, 3), (28, 24, 6, 10), (28, 25, 3, 10), (28, 27, 6, 10), (29, 26, 5, 13), (29, 27, 2, 4),
         (29, 28, 3, 13)]
G = nx.DiGraph()
for k in range(len(Links)):
    G.add_edge(Links[k][0], Links[k][1], time=[Links[k][2], Links[k][3]],
               l=Links[k][2], u=Links[k][3], time_mid=(Links[k][2] + Links[k][3]) / 2)
time = nx.get_edge_attributes(G, 'time')
l = nx.get_edge_attributes(G, 'l')
u = nx.get_edge_attributes(G, 'u')
# 路网中的所有边
L = []
for item in l.items():
    L.append(item[0])
Lt = tuplelist(L)
edge_info = list(G.edges)
n = len(G.nodes)

# 定义变量
p = {}
for (i, j) in L:
    p[(i, j)] = solver.BoolVar(f'p[{i},{j}]')


# 目标1：最小化第一部分: sum(u[i,j] * p[i,j])
obj_part1 = solver.IntVar(0, solver.infinity(), 'obj_part1')

# 目标2：最小化第二部分: sum(l[i,j] * (1 - p[i,j]))
obj_part2 = solver.IntVar(0, solver.infinity(), 'obj_part2')

# 目标函数
objective = solver.Objective()

# 设置第一部分目标: sum(u[i,j] * p[i,j])
for (i, j) in L:
    objective.SetCoefficient(p[(i, j)], u[i, j])
objective.SetMinimization()

# 设置第二部分目标: sum(l[i,j] * (1 - p[i,j])
for (i, j) in L:
    objective.SetCoefficient(p[(i, j)], -l[i, j])  # 计算 (1 - p[i,j]) 通过 -l[i,j] 来处理

# 最小化目标1 - 目标2
objective.SetMinimization()

# 约束条件
# 约束1: 每个节点流出的总流量至少为1（除了源节点和汇节点）
for i in N:
    if i != r and i != t:
        constraint = solver.Constraint(1, solver.infinity())
        for j in N:
            if (i, j) in L:
                constraint.SetCoefficient(p[(i, j)], 1)

# 约束2: 流量平衡约束
for i in N:
    balance_constraint = solver.Constraint(-solver.infinity(), solver.infinity())
    for j in N:
        if (i, j) in L:
            balance_constraint.SetCoefficient(p[(i, j)], 1)
        if (j, i) in L:
            balance_constraint.SetCoefficient(p[(j, i)], -1)
    if i == r:
        balance_constraint.SetLb(1)  # 对源节点设置下界
    elif i == t:
        balance_constraint.SetUb(-1)  # 对汇节点设置上界
    else:
        balance_constraint.SetLb(0)  # 其他节点流量平衡为0
        balance_constraint.SetUb(0)  # 确保流入流出相等


# 添加约束3：每个节点最多经过一次
for node in N_center:
    solver.Add(sum(p[(i, j)] for (i, j) in edge_info if j == node) +
               sum(p[(i, j)] for (i, j) in edge_info if i == node) <= 1)

# 添加约束4：路径最多经过9个节点
solver.Add(sum(p[(i, j)] for (i, j) in edge_info if i == r) <= 9)

# 求解问题
status = solver.Solve()

# 输出结果
if status == pywraplp.Solver.OPTIMAL:
    print('Optimal solution found:')
    for (i, j) in L:
        if p[(i, j)].solution_value() > 0.5:
            print(f'Edge ({i},{j}) is used.')
else:
    print('No optimal solution found.')