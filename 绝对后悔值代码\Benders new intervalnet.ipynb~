{"cells": [{"cell_type": "code", "execution_count": 1, "id": "e8785e9d", "metadata": {"ExecuteTime": {"end_time": "2024-10-23T11:02:49.186936Z", "start_time": "2024-10-23T11:02:47.837107Z"}}, "outputs": [{"ename": "TypeError", "evalue": "expected str, bytes or os.PathLike object, not NoneType", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mTypeError\u001b[0m                                 Trace<PERSON> (most recent call last)", "Input \u001b[1;32mIn [1]\u001b[0m, in \u001b[0;36m<cell line: 2>\u001b[1;34m()\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mnetworkx\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnx\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mmat<PERSON>lotlib\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpyplot\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mplt\u001b[39;00m\n\u001b[0;32m      5\u001b[0m alpha \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0.5\u001b[39m\u001b[38;5;66;03m# 权重系数\u001b[39;00m\n", "File \u001b[1;32mD:\\anaconda3.9.12\\lib\\gurobipy\\__init__.py:3\u001b[0m, in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mos\u001b[39;00m\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(os, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124madd_dll_directory\u001b[39m\u001b[38;5;124m'\u001b[39m):\n\u001b[1;32m----> 3\u001b[0m   os\u001b[38;5;241m.\u001b[39madd_dll_directory(\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpath\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mjoin\u001b[49m\u001b[43m(\u001b[49m\u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetenv\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mGUROBI_HOME\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mbin\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m)\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mgurobipy\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m\n", "File \u001b[1;32mD:\\anaconda3.9.12\\lib\\ntpath.py:78\u001b[0m, in \u001b[0;36mjoin\u001b[1;34m(path, *paths)\u001b[0m\n\u001b[0;32m     77\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mjoin\u001b[39m(path, \u001b[38;5;241m*\u001b[39mpaths):\n\u001b[1;32m---> 78\u001b[0m     path \u001b[38;5;241m=\u001b[39m \u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfspath\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     79\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(path, \u001b[38;5;28mbytes\u001b[39m):\n\u001b[0;32m     80\u001b[0m         sep \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mb\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n", "\u001b[1;31mTypeError\u001b[0m: expected str, bytes or os.PathLike object, not NoneType"]}], "source": ["import networkx as nx\n", "from gurobipy import *\n", "import matplotlib.pyplot as plt\n", "\n", "alpha = 0.5# 权重系数\n", "SP_min = 25  # 下界最短路\n", "SP_max = 44  # 上界最短路\n", "r = 1   # 起点\n", "t = 29  # 终点\n", "N = [i for i in range(1, 30)]\n", "N_rt = [r, t]\n", "N_center = N.copy()\n", "for k in [r, t]:\n", "    N_center.remove(k)\n", "Links = [(1,2,6,10),(1,3,6,8),(2,3,6,10),(1,4,5,9),(3,4,6,10),(1,5,3,5),(4,5,1,4),\n", "         (2,6,6,10),(3,6,6,10),(3,7,6,8),(6,7,6,10),(3,8,6,10),(4,8,6,8),(7,8,6,10),\n", "         (4,9,3,9),(8,9,6,10),(4,10,6,10),(5,10,3,10),(9,10,6,10),(6,11,6,10),(7,11,6,10),\n", "         (7,12,6,8),(11,12,6,10),(7,13,6,10),(8,13,6,8),(12,13,6,10),(8,14,6,10),(9,14,6,8),\n", "         (13,14,6,10),(9,15,3,9),(10,15,6,12),(14,15,6,10),(10,16,3,10),(15,16,6,10),(11,17,6,10),\n", "         (12,17,6,10),(12,18,6,8),(13,18,6,8),(17,18,6,10),(13,19,6,10),(14,19,6,8),(18,19,6,10),\n", "         (14,20,6,10),(15,20,4,9),(19,20,6,10),(15,21,6,10),(16,21,3,10),(20,21,6,10),(17,22,6,10),\n", "         (18,22,6,10),(18,23,6,8),(19,23,6,8),(22,23,6,10),(19,24,6,8),(20,24,5,10),(23,24,6,10),\n", "         (20,25,6,10),(21,25,3,11),(24,25,6,10),(22,26,6,10),(23,26,6,10),(23,27,6,8),(24,27,4,9),\n", "         (26,27,2,3),(24,28,6,10),(25,28,3,10),(27,28,6,10),(26,29,5,13),(27,29,2,4),(28,29,3,13),\n", "         (2,1,6,10),(3,1,6,8),(3,2,6,10),(4,1,5,9),(4,3,6,10),(5,1,3,5),(5,4,1,4),\n", "         (6,2,6,10),(6,3,6,10),(7,3,6,8),(7,6,6,10),(8,3,6,10),(8,4,6,8),(8,7,6,10),\n", "         (9,4,3,9),(9,8,6,10),(10,4,6,10),(10,5,3,10),(10,9,6,10),(11,6,6,10),(11,7,6,10),\n", "         (12,7,6,8),(12,11,6,10),(13,7,6,10),(13,8,6,8),(13,12,6,10),(14,8,6,10),(14,9,6,8),\n", "         (14,13,6,10),(15,9,3,9),(15,10,6,12),(15,14,6,10),(16,10,3,10),(16,15,6,10),(17,11,6,10),\n", "         (17,12,6,10),(18,12,6,8),(18,13,6,8),(18,17,6,10),(19,13,6,10),(19,14,6,8),(19,18,6,10),\n", "         (20,14,6,10),(20,15,4,9),(20,19,6,10),(21,15,6,10),(21,16,3,10),(21,20,6,10),(22,17,6,10),\n", "         (22,18,6,10),(23,18,6,8),(23,19,6,8),(23,22,6,10),(24,19,6,8),(24,20,5,10),(24,23,6,10),\n", "         (25,20,6,10),(25,21,3,11),(25,24,6,10),(26,22,6,10),(26,23,6,10),(27,23,6,8),(27,24,4,9),\n", "         (27,26,2,3),(28,24,6,10),(28,25,3,10),(28,27,6,10),(29,26,5,13),(29,27,2,4),(29,28,3,13)]\n", "G = nx.DiGraph()\n", "for k in range(len(Links)):\n", "    G.add_edge(Links[k][0], Links[k][1], time=[Links[k][2], <PERSON>s[k][3]],\n", "               time_lb=Links[k][2], time_ub=Links[k][3], time_mid=(Links[k][2] + <PERSON>s[k][3]) / 2)\n", "time = nx.get_edge_attributes(G, 'time')\n", "time_lb = nx.get_edge_attributes(G, 'time_lb')\n", "time_ub = nx.get_edge_attributes(G, 'time_ub')\n", "# 路网中的所有边\n", "L = []\n", "for item in time_lb.items():\n", "    L.append(item[0])\n", "Lt = tuplelist(L)\n", "\n", "\n", "# print('路网所有的边：',L)\n", "\n", "# 定义加割函数addBendersCuts\n", "def addBendersCuts(d_obj, x):\n", "    # print(x)\n", "    if d.status == GRB.Status.UNBOUNDED:  # 模型无界\n", "        ## 取极射线，向模型中添加可行割\n", "        ray = d.Unbd<PERSON>ay\n", "        # print(ray)\n", "        ## 添加可行割\n", "        m.addConstr(alpha * (quicksum(time_ub[i, j] * p[i, j] for i, j in L) -\n", "                             quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j])\n", "                                      * ray[i, j] for i, j in L)) + (1 - alpha) *\n", "                    (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L)\n", "                     - SP_min - SP_max) <= 0)\n", "        print(\"*****加入可行割*****\")\n", "        ## 没有最优解，默认等于上次迭代的结果\n", "        d_obj.append(d_obj[-1])\n", "    elif d.status == GRB.Status.OPTIMAL:  # 发现最优解\n", "        ## 添加最优割\n", "        m.addConstr(alpha * (quicksum(time_ub[i, j] * p[i, j] for i, j in L) -\n", "                             quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j])\n", "                                      * w[i, j].x for i, j in L)) + (1 - alpha) *\n", "                    (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L)\n", "                     - SP_min - SP_max) <= z)\n", "        print(\"*****加入最优割*****\")\n", "        ## 将最优解与当前主问题的f(y*)相加构成问题的上界加入到集合中\n", "        d_obj.append(d.objVal)  # 获取最优解\n", "    else:  # 其他状态，模型不可行\n", "        print(d.status)\n", "\n", "\n", "try:\n", "    ## 初始化下界记录数组\n", "    m_obj = []\n", "    ## 创建限制主问题MP\n", "    m = Model('masterproblem')  # Benders Master Problem\n", "    ## 创建对偶子问题SP_Dual\n", "    d = Model('dual_subproblem')  # dual of Benders SubProblem\n", "    ## 注意，这里是加入限制主问题的变量，而不是初始子问题的变量\n", "    ## 向MP中添加变量\n", "    z = m.addVar(vtype=GRB.CONTINUOUS, name='z')\n", "    p = m.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='p')\n", "\n", "    ## 设置MP的目标函数\n", "    m.setObjective(z, GRB.MINIMIZE)\n", "    ## 向对偶子问题中添加变量\n", "    w = d.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='w')\n", "\n", "    # 添加约束\n", "    ## 向主问题中加入约束\n", "    ###起点约束\n", "    m_con1 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select(r, '*')) == 1)\n", "    m_con2 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', r)) == 0)\n", "    ###终点约束\n", "    m_con3 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', t)) == 1)\n", "    m_con4 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select(t, '*')) == 0)\n", "    ###中间点约束\n", "    for j in N_center:\n", "        m_con5 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', j))\n", "                             == quicksum(p[j, k] for j, k in Lt.select(j, '*')))\n", "    ###有效路径约束\n", "    #m_con6 = m.addConstr()\n", "    ## 向对偶子问题中加入约束\n", "    ###起点约束\n", "    d_con1 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select(r, '*')) == 1)\n", "    d_con2 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', r)) == 0)\n", "    ###终点约束\n", "    d_con3 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', t)) == 1)\n", "    d_con4 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select(t, '*')) == 0)\n", "    ###中间点约束\n", "    for j in N_center:\n", "        d_con5 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', j)) ==\n", "                             quicksum(w[j, k] for j, k in Lt.select(j, '*')))\n", "    # 设置参数 InfUnbdInfo，此变量的解释 https://www.gurobi.com/documentation/9.5/refman/infunbdinfo.html\n", "    # 这个参数是为了返回极射线\n", "    d.<PERSON>.InfUnbdInfo = 1\n", "    ## 设置迭代次数\n", "    iteration = 0\n", "    ## 初始化上界记录数组\n", "    d_obj = [299]\n", "    x = []  # x是用来接收变量取值的，与 .x方法两回事，要注意\n", "    print(\"***第{}次求解主问题MP***\".format(iteration + 1))\n", "    ## 第一次求解MP\n", "    m.optimize()\n", "    print('第{}次主问题目标函数'.format(iteration + 1), m.objVal)\n", "    for i, j in L:\n", "        if p[i, j].x == 1:\n", "            print('p[%d,%d] = %d' % (i, j, p[i, j].x))\n", "    ## 将 MP 最优值放入 集合中\n", "    m_obj.append(m.objval)\n", "    ## z.x可以看作子问题最优解，可以理解为子问题的上界\n", "    # while d_obj[-1] > z.x:  # 迭代主循环\n", "    while iteration < 8:\n", "        if iteration == 0:\n", "            ## 第一次调用对偶子问题，需要构造它的目标函数\n", "            d.setObjective(alpha * (quicksum(time_ub[i, j] * p[i, j].x for i, j in L) -\n", "                                    quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j].x)\n", "                                             * w[i, j] for i, j in L)) + (1 - alpha) *\n", "                           (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j].x for i, j in L)\n", "                            - SP_min - SP_max), GRB.MAXIMIZE)\n", "            print(\"***第{}次求解子问题对偶问题SP_dual***\".format(iteration + 1))\n", "            ## 求解对偶子问题\n", "            d.optimize()\n", "            print('第{}次对偶子问题目标函数'.format(iteration + 1), d.objVal)\n", "            for i, j in L:\n", "                if w[i, j].x == 1:\n", "                    print('w[%d,%d] = %d' % (i, j, w[i, j].x))\n", "            ## 调用加割函数\n", "            addBendersCuts(d_obj, x)\n", "            iteration = 1\n", "        else:\n", "            d.setObjective(alpha * (quicksum(time_ub[i, j] * p[i, j].x for i, j in L) -\n", "                                    quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j].x)\n", "                                             * w[i, j] for i, j in L)) + (1 - alpha) *\n", "                           (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j].x for i, j in L)\n", "                            - SP_min - SP_max), GRB.MAXIMIZE)\n", "            print(\"***第{}次求解子问题对偶问题SP_dual***\".format(iteration + 1))\n", "            ## 求解对偶子问题\n", "            d.optimize()\n", "            print('第{}次对偶子问题目标函数'.format(iteration + 1), d.objVal)\n", "            for i, j in L:\n", "                if w[i, j].x == 1:\n", "                    print('w[%d,%d] = %d' % (i, j, w[i, j].x))\n", "            ## 调用加割函数\n", "            addBendersCuts(d_obj, x)  # add Benders Cuts\n", "            ## 迭代次数更新\n", "            iteration = iteration + 1\n", "        print(\"***第{}次求解主问题MP***\".format(iteration + 1))\n", "        ## 求解主问提（加割后）\n", "        m.optimize()\n", "        print('第{}次主问题目标函数'.format(iteration + 1), m.objVal)\n", "        for i, j in L:\n", "            if p[i, j].x == 1:\n", "                print('p[%d,%d] = %d' % (i, j, p[i, j].x))\n", "        ## 将当前fy*加入集合中\n", "        m_obj.append(m.objval)\n", "    ##打印路径决策变量为1的路径\n", "    L_decision = []\n", "    for i, j in L:\n", "        if p[i, j].x == 1:\n", "            L_decision.append((i, j))\n", "    print('加权决策路径', L_decision)\n", "    \n", "    ##画出决策路径\n", "    print('画出加权决策路径')\n", "    pos = {1:(0.46035,5.0452),2:(0.73589,7.177),3:(1.4617,6.0917),4:(1.6095,4.4897),5:(1.0316,3.1072),\n", "           6:(1.5827,8.4173),7:(2.2749,7.1641),8:(2.7251,5.3165),9:(2.6781,3.624),10:(2.127,1.7765),\n", "           11:(2.7722,9.1279),12:(3.4778,7.7842),13:(3.8071,6.2726),14:(3.8542,4.2313),15:(3.6929,2.5517),\n", "           16:(3.2359,0.67829),17:(4.5128,9.0504),18:(5.0302,7.5),19:(5.2587,5.3036),20:(5.2386,3.2235),\n", "           21:(4.8353,1.1305),22:(6.0921,8.6886),23:(6.4751,6.7119),24:(6.5558,4.593),25:(6.334,2.0995),\n", "           26:(7.5034,8.1202),27:(7.8058,5.8075),28:(7.584,3.1589),29:(8.9751,5.7041)}\n", "    fig = plt.figure(figsize=(10,16))\n", "    plt.subplot(2,1,1)\n", "    nx.draw_networkx(G,pos)\n", "    nx.draw_networkx_nodes(G,pos,node_color = 'w',edgecolors = 'k')\n", "    nx.draw_networkx_labels(G,pos,font_color = 'r')\n", "    nx.draw_networkx_edges(G,pos,edge_color = 'b')\n", "    nx.draw_networkx_edge_labels(G,pos,time,verticalalignment = 'center_baseline',font_size = 12)\n", "    nx.draw_networkx_edges(G,pos,edgelist = L_decision,edge_color = 'r',width = 3)\n", "    plt.show()\n", "    \n", "    ## 以下部分为绘制图像部分\n", "    x_label = [i for i in range(1, len(m_obj) + 1)]\n", "    print('上界迭代过程', d_obj)  ## 打印上界下降过程\n", "    print('下界迭代过程', m_obj)  ## 打印下界上升过程\n", "    plt.subplot(2,1,2)\n", "    plt.plot(x_label, d_obj, 'b^-', alpha=0.8, linewidth=1, label='upper bound')\n", "    plt.plot(x_label, m_obj, 'r^-', alpha=0.8, linewidth=1, label='lower bound')\n", "\n", "    ### 显示标签，如果不加这句，即使在plot中加了label='一些数字'的参数，最终还是不会显示标签\n", "    plt.legend(loc=\"upper right\")\n", "    plt.xlabel('iteration')\n", "    plt.ylabel('value')\n", "    plt.show()\n", "\n", "except <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "    print('Error code ' + str(e.errno) + \": \" + str(e))\n", "except AttributeError:\n", "    print('Encountered an attribute error')\n", "print(m.objVal)"]}, {"cell_type": "code", "execution_count": null, "id": "a662b607", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 5}