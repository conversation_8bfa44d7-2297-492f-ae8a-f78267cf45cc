import pulp
import numpy as np
from collections import defaultdict


class RobustTravelOptimizer:
    def __init__(self, nodes, arcs, time_periods, start_node, end_node, latest_arrival_time):
        self.nodes = nodes
        self.arcs = arcs
        self.time_periods = time_periods
        self.M = list(time_periods.keys())
        self.s = start_node
        self.d = end_node
        self.T = latest_arrival_time
        self.B = 10000  # 大常数

        # 存储阻抗数据
        self.L = defaultdict(dict)  # 阻抗下界
        self.U = defaultdict(dict)  # 阻抗上界

    def set_impedance(self, arc, period, lower_bound, upper_bound):
        self.L[arc][period] = lower_bound
        self.U[arc][period] = upper_bound

    def build_fixed_impedance_model(self, impedance_values):
        model = pulp.LpProblem("Fixed_Impedance_Travel_Planning", pulp.LpMaximize)

        # 决策变量
        t0 = pulp.LpVariable('t0', lowBound=0, cat='Continuous')
        t = {i: pulp.LpVariable(f't_{i}', lowBound=0, cat='Continuous') for i in self.nodes}
        x = {(i, j, m): pulp.LpVariable(f'x_{i}_{j}_{m}', cat='Binary')
             for (i, j) in self.arcs for m in self.M}

        # 目标函数：最大化出发时刻
        model += t0

        # === 约束条件 ===
        # 1. 起点时间约束
        model += t[self.s] == t0

        # 2. 时间传播约束
        for (i, j) in self.arcs:
            model += t[j] == t[i] + pulp.lpSum(
                impedance_values.get((i, j, m), 0) * x[i, j, m] for m in self.M
            )

        # 3. 终点时间约束
        model += t[self.d] <= self.T

        # 4. 时段连接约束
        for j in self.nodes:
            if j != self.s and j != self.d:
                # 进入边：以j为终点的边
                incoming = pulp.lpSum(
                    m * x[i, j, m]
                    for (i, to) in self.arcs
                    if to == j
                    for m in self.M
                )
                # 离开边：以j为起点的边
                outgoing = pulp.lpSum(
                    n * x[j, k, n]
                    for (fr, k) in self.arcs
                    if fr == j
                    for n in self.M
                )
                model += incoming <= outgoing

        # 5. 路段经过次数约束
        for (i, j) in self.arcs:
            model += pulp.lpSum(x[i, j, m] for m in self.M) <= 1

        # 6. 流量守恒约束
        for j in self.nodes:
            # 流入：以j为终点的边
            inflow = pulp.lpSum(
                x[i, j, m]
                for (i, to) in self.arcs
                if to == j
                for m in self.M
            )
            # 流出：以j为起点的边
            outflow = pulp.lpSum(
                x[j, k, n]
                for (fr, k) in self.arcs
                if fr == j
                for n in self.M
            )

            if j == self.s:
                model += outflow - inflow == 1
            elif j == self.d:
                model += outflow - inflow == -1
            else:
                model += outflow - inflow == 0

        # 7. 时段一致性约束
        for (i, j) in self.arcs:
            for m in self.M:
                start, end = self.time_periods[m]
                model += t[i] >= start - self.B * (1 - x[i, j, m])
                model += t[i] <= end + self.B * (1 - x[i, j, m])

        # 8. 时间范围约束
        model += t0 >= 0
        model += t0 <= self.T

        self.fixed_model = model
        self.fixed_vars = {'t0': t0, 't': t, 'x': x}
        return model

    def build_robust_model(self):
        model = pulp.LpProblem("Robust_Travel_Optimization", pulp.LpMaximize)

        # === 第一阶段变量 ===
        t0 = pulp.LpVariable('t0', lowBound=0, cat='Continuous')
        t = {i: pulp.LpVariable(f't_{i}', lowBound=0, cat='Continuous') for i in self.nodes}
        x = {(i, j, m): pulp.LpVariable(f'x_{i}_{j}_{m}', cat='Binary')
             for (i, j) in self.arcs for m in self.M}

        # === 第二阶段变量（对偶变量）===
        lambda_d = pulp.LpVariable('lambda_d', lowBound=0, cat='Continuous')
        mu = {i: pulp.LpVariable(f'mu_{i}', cat='Continuous') for i in self.nodes}
        v_min = pulp.LpVariable('v_min', lowBound=0, cat='Continuous')
        v_max = pulp.LpVariable('v_max', lowBound=0, cat='Continuous')
        beta = {(i, j): pulp.LpVariable(f'beta_{i}_{j}', lowBound=0, cat='Continuous') for (i, j) in self.arcs}
        eta = {(i, j, m): pulp.LpVariable(f'eta_{i}_{j}_{m}', lowBound=0, cat='Continuous')
               for (i, j) in self.arcs for m in self.M}
        theta = {(i, j, m): pulp.LpVariable(f'theta_{i}_{j}_{m}', lowBound=0, cat='Continuous')
                 for (i, j) in self.arcs for m in self.M}
        
        # 新增：用于线性化mu[j] * x[i,j,m]的辅助变量
        z = {(i, j, m): pulp.LpVariable(f'z_{i}_{j}_{m}', cat='Continuous')
             for (i, j) in self.arcs for m in self.M}

        # === 目标函数 ===
        model += t0 - lambda_d

        # === 第一阶段约束 ===
        # 1. 起点时间约束
        model += t[self.s] == t0

        # 2. 时间传播约束 (使用上界阻抗)
        for (i, j) in self.arcs:
            model += t[j] == t[i] + pulp.lpSum(
                self.U[(i, j)][m] * x[i, j, m] for m in self.M
            )

        # 3. 终点时间约束
        model += t[self.d] <= self.T

        # 4. 时段连接约束
        for j in self.nodes:
            if j != self.s and j != self.d:
                # 进入边：以j为终点的边
                incoming = pulp.lpSum(
                    m * x[i, j, m]
                    for (i, to) in self.arcs
                    if to == j
                    for m in self.M
                )
                # 离开边：以j为起点的边
                outgoing = pulp.lpSum(
                    n * x[j, k, n]
                    for (fr, k) in self.arcs
                    if fr == j
                    for n in self.M
                )
                model += incoming <= outgoing

        # 5. 路段经过次数约束
        for (i, j) in self.arcs:
            model += pulp.lpSum(x[i, j, m] for m in self.M) <= 1

        # 6. 流量守恒约束
        for j in self.nodes:
            # 流入：以j为终点的边
            inflow = pulp.lpSum(
                x[i, j, m]
                for (i, to) in self.arcs
                if to == j
                for m in self.M
            )
            # 流出：以j为起点的边
            outflow = pulp.lpSum(
                x[j, k, n]
                for (fr, k) in self.arcs
                if fr == j
                for n in self.M
            )

            if j == self.s:
                model += outflow - inflow == 1
            elif j == self.d:
                model += outflow - inflow == -1
            else:
                model += outflow - inflow == 0

        # 7. 时段一致性约束
        for (i, j) in self.arcs:
            for m in self.M:
                start, end = self.time_periods[m]
                model += t[i] >= start - self.B * (1 - x[i, j, m])
                model += t[i] <= end + self.B * (1 - x[i, j, m])

        # 8. 时间范围约束
        model += t0 >= 0
        model += t0 <= self.T

        # === 第二阶段约束（对偶约束）===
        # 1. 起点对偶约束
        model += 1 - mu[self.s] - v_min + v_max == 0

        # 2. 节点时间传播对偶约束
        for (i, j) in self.arcs:
            for m in self.M:
                model += mu[j] - mu[i] + eta[i, j, m] - theta[i, j, m] == 0

        # 3. 阻抗对偶约束 - 线性化处理
        for (i, j) in self.arcs:
            for m in self.M:
                L_ijm = self.L[(i, j)][m]
                U_ijm = self.U[(i, j)][m]
                
                # 线性化约束：z[i,j,m] = mu[j] * x[i,j,m]
                # 当x[i,j,m] = 1时，z[i,j,m] = mu[j]
                # 当x[i,j,m] = 0时，z[i,j,m] = 0
                model += z[i, j, m] <= mu[j] + self.B * (1 - x[i, j, m])
                model += z[i, j, m] >= mu[j] - self.B * (1 - x[i, j, m])
                model += z[i, j, m] <= self.B * x[i, j, m]
                model += z[i, j, m] >= -self.B * x[i, j, m]
                
                # 使用线性化变量z替代原来的乘积
                model += -L_ijm * mu[j] - (U_ijm - L_ijm) * z[i, j, m] + beta[i, j] + \
                         self.B * (eta[i, j, m] - theta[i, j, m]) >= 0 - self.B * (1 - x[i, j, m])

        # 4. 对偶目标函数约束
        dual_obj = mu[self.d] * self.T + pulp.lpSum(
            -beta[i, j] + self.B * (eta[i, j, m] - theta[i, j, m]) +
            self.time_periods[m][0] * eta[i, j, m] - self.time_periods[m][1] * theta[i, j, m]
            for (i, j) in self.arcs for m in self.M
        )
        model += lambda_d == dual_obj

        self.robust_model = model
        self.robust_vars = {
            't0': t0, 't': t, 'x': x, 'lambda_d': lambda_d,
            'mu': mu, 'v_min': v_min, 'v_max': v_max,
            'beta': beta, 'eta': eta, 'theta': theta, 'z': z
        }
        return model

    def solve(self, model, time_limit=100):
        # 将CPLEX_PY替换为PULP_CBC_CMD
        solver = pulp.PULP_CBC_CMD(timeLimit=time_limit, threads=4)
        model.solve(solver)
        return pulp.LpStatus[model.status]

    def get_solution(self, model_type='robust'):
        if model_type == 'fixed' and hasattr(self, 'fixed_vars'):
            vars = self.fixed_vars
            model = self.fixed_model
        elif model_type == 'robust' and hasattr(self, 'robust_vars'):
            vars = self.robust_vars
            model = self.robust_model
        else:
            return None

        if model.status != pulp.LpStatusOptimal:
            return None

        solution = {
            't0': pulp.value(vars['t0']) if 't0' in vars else None,
            'path': [],
            'arrival_times': {},
            'robust_cost': None
        }

        # 提取到达时间
        if 't' in vars:
            solution['arrival_times'] = {
                i: pulp.value(vars['t'][i]) for i in self.nodes
                if i in vars['t'] and pulp.value(vars['t'][i]) is not None
            }

        # 提取路径
        if 'x' in vars:
            for (i, j) in self.arcs:
                for m in self.M:
                    if (i, j, m) in vars['x'] and pulp.value(vars['x'][(i, j, m)]) == 1:
                        solution['path'].append((i, j, m))

        # 计算鲁棒成本
        if model_type == 'robust' and 'lambda_d' in vars and 't0' in vars:
            t0_val = pulp.value(vars['t0'])
            lambda_d_val = pulp.value(vars['lambda_d'])
            if t0_val is not None and lambda_d_val is not None:
                solution['robust_cost'] = t0_val - lambda_d_val

        return solution


# ========================
# 示例使用
# ========================

if __name__ == "__main__":
    # 1. 定义路网和时间段
    nodes = [1, 2, 3, 4, 5]
    arcs = [(1, 2), (1, 3), (2, 4), (3, 4), (4, 5)]

    # 时间段定义 (m: (start, end))
    time_periods = {
        1: (0, 15),  # 8:00-8:15
        2: (15, 30),  # 8:15-8:30
        3: (30, 45),  # 8:30-8:45
        4: (45, 60)  # 8:45-9:00
    }

    # 2. 创建优化器
    optimizer = RobustTravelOptimizer(
        nodes=nodes,
        arcs=arcs,
        time_periods=time_periods,
        start_node=1,
        end_node=5,
        latest_arrival_time=60  # 9:00 (60分钟)
    )

    # 3. 设置阻抗数据 (基于论文表1的示例)
    impedance_data = {
        (1, 2): {1: (11.04, 13.37), 2: (7.70, 10.09), 3: (6.58, 9.68), 4: (4.53, 7.47)},
        (1, 3): {1: (9.50, 12.20), 2: (8.20, 10.80), 3: (7.10, 9.50), 4: (5.80, 8.20)},
        (2, 4): {1: (12.30, 15.10), 2: (10.50, 13.20), 3: (8.90, 11.60), 4: (7.20, 9.80)},
        (3, 4): {1: (8.20, 10.90), 2: (7.50, 9.80), 3: (6.80, 9.20), 4: (5.50, 7.90)},
        (4, 5): {1: (10.20, 12.80), 2: (9.10, 11.50), 3: (8.30, 10.70), 4: (7.60, 9.90)}
    }

    # 添加阻抗数据
    for arc, periods in impedance_data.items():
        for period, (L, U) in periods.items():
            optimizer.set_impedance(arc, period, L, U)

    # 4. 构建并求解鲁棒模型
    print("构建鲁棒模型...")
    try:
        robust_model = optimizer.build_robust_model()
        print("求解鲁棒模型...")
        status = optimizer.solve(robust_model, time_limit=100)
        robust_solution = optimizer.get_solution('robust')
    except Exception as e:
        print(f"构建或求解鲁棒模型时出错: {e}")
        robust_solution = None

    # 5. 构建并求解固定阻抗模型（使用平均阻抗）
    print("\n构建固定阻抗模型...")
    fixed_impedance = {}
    for arc, periods in impedance_data.items():
        for period, (L, U) in periods.items():
            fixed_impedance[(arc[0], arc[1], period)] = (L + U) / 2

    try:
        fixed_model = optimizer.build_fixed_impedance_model(fixed_impedance)
        print("求解固定阻抗模型...")
        status_fixed = optimizer.solve(fixed_model)
        fixed_solution = optimizer.get_solution('fixed')
    except Exception as e:
        print(f"构建或求解固定阻抗模型时出错: {e}")
        fixed_solution = None

    # 6. 打印结果
    print("\n" + "=" * 50)
    print("鲁棒优化模型结果")
    print("=" * 50)
    if robust_solution:
        print(f"最晚出发时刻: {robust_solution['t0']:.2f} 分钟" if robust_solution[
                                                                       't0'] is not None else "最晚出发时刻: 未求解")
        if robust_solution['path']:
            print(f"路径: {robust_solution['path']}")
        else:
            print("路径: 未找到")

        if robust_solution['robust_cost'] is not None:
            print(f"鲁棒成本: {robust_solution['robust_cost']:.2f}")

        if robust_solution['arrival_times']:
            print("节点到达时刻:")
            for node, time in robust_solution['arrival_times'].items():
                if time is not None:
                    print(f"  节点 {node}: {time:.2f} 分钟")
        else:
            print("节点到达时刻: 未求解")
    else:
        print("未找到鲁棒模型解决方案")

    print("\n" + "=" * 50)
    print("固定阻抗模型结果")
    print("=" * 50)
    if fixed_solution:
        print(f"最晚出发时刻: {fixed_solution['t0']:.2f} 分钟" if fixed_solution[
                                                                      't0'] is not None else "最晚出发时刻: 未求解")
        if fixed_solution['path']:
            print(f"路径: {fixed_solution['path']}")
        else:
            print("路径: 未找到")

        if fixed_solution['arrival_times']:
            print("节点到达时刻:")
            for node, time in fixed_solution['arrival_times'].items():
                if time is not None:
                    print(f"  节点 {node}: {time:.2f} 分钟")
        else:
            print("节点到达时刻: 未求解")
    else:
        print("未找到固定阻抗模型解决方案")