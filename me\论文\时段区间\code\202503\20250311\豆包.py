import heapq
from collections import defaultdict

# 定义路网类
class RoadNetwork:
    def __init__(self, network_data):
        self.graph = defaultdict(list)
        self._initialize_graph(network_data)

    def _initialize_graph(self, network_data):
        # 从输入数据初始化图
        for source, neighbors in network_data.items():
            for target, time_ranges in neighbors.items():
                self.graph[source].append((target, time_ranges))

    def get_impedance(self, impedance_ranges, time):
        # 根据时间选择合适的阻抗
        for start, end, low, high in impedance_ranges:
            if start <= time < end:
                return low, high
        return None

    def dijkstra(self, start, end, time, lower_bound=True):
        # Dijkstra 算法，用于计算最短路径
        priority_queue = [(0, start, time)]
        dist = {start: 0}
        prev = {start: None}

        while priority_queue:
            current_dist, current_node, current_time = heapq.heappop(priority_queue)

            if current_node == end:
                path = []
                while current_node is not None:
                    path.append(current_node)
                    current_node = prev[current_node]
                path.reverse()
                return path, current_dist

            if current_dist > dist.get(current_node, float('inf')):
                continue

            for neighbor, impedance_ranges in self.graph[current_node]:
                impedance = self.get_impedance(impedance_ranges, current_time)
                if impedance is None:
                    continue
                if lower_bound:
                    cost = impedance[0]
                else:
                    cost = impedance[1]

                new_dist = current_dist + cost
                new_time = current_time + cost
                if new_time >= 1440:
                    new_time %= 1440

                if new_dist < dist.get(neighbor, float('inf')):
                    dist[neighbor] = new_dist
                    prev[neighbor] = current_node
                    heapq.heappush(priority_queue, (new_dist, neighbor, new_time))

        return None, float('inf')

    def calculate_robust_cost(self, path, start_time):
        # 计算路径的鲁棒成本
        # 计算原路径最坏时间
        a = 0
        current_time = start_time
        for i in range(len(path) - 1):
            u = path[i]
            v = path[i + 1]
            for neighbor, impedance_ranges in self.graph[u]:
                if neighbor == v:
                    impedance = self.get_impedance(impedance_ranges, current_time)
                    if impedance is None:
                        continue
                    a += impedance[1]
                    current_time += impedance[1]
                    if current_time >= 1440:
                        current_time %= 1440
                    break

        # 计算备选路径最佳时间和路径
        remaining_graph = defaultdict(list)
        for u in self.graph:
            for v, impedance_ranges in self.graph[u]:
                if (u, v) not in zip(path[:-1], path[1:]):
                    remaining_graph[u].append((v, impedance_ranges))

        remaining_network = RoadNetwork({})
        remaining_network.graph = remaining_graph
        alternative_path, b = remaining_network.dijkstra(path[0], path[-1], start_time, lower_bound=True)

        robust_cost = a - b
        return robust_cost, a, b, alternative_path

    def find_min_robust_path(self, start, end, start_time):
        # 找到最小鲁棒成本的路径
        all_paths = []
        priority_queue = [(0, [start], start_time)]

        while priority_queue:
            current_dist, current_path, current_time = heapq.heappop(priority_queue)
            current_node = current_path[-1]

            if current_node == end:
                all_paths.append(current_path)
                continue

            for neighbor, impedance_ranges in self.graph[current_node]:
                if neighbor not in current_path:
                    impedance = self.get_impedance(impedance_ranges, current_time)
                    if impedance is None:
                        continue
                    new_dist = current_dist + impedance[0]
                    new_time = current_time + impedance[0]
                    if new_time >= 1440:
                        new_time %= 1440
                    new_path = current_path + [neighbor]
                    heapq.heappush(priority_queue, (new_dist, new_path, new_time))

        min_robust_cost = float('inf')
        min_robust_path = None
        worst_case_time = 0
        best_case_time = 0
        alternative_path = None

        for path in all_paths:
            robust_cost, a, b, alt_path = self.calculate_robust_cost(path, start_time)
            if robust_cost < min_robust_cost:
                min_robust_cost = robust_cost
                min_robust_path = path
                worst_case_time = a
                best_case_time = b
                alternative_path = alt_path

        return min_robust_path, min_robust_cost, worst_case_time, best_case_time, alternative_path



network_data = {
  "1": {
    "2": [[0, 480, 2, 10], [480, 600, 5, 15], [600, 1440, 3, 20]],
    "3": [[0, 480, 4, 12], [480, 600, 6, 16], [600, 1440, 5, 18]],
    "4": [[0, 480, 3, 11], [480, 600, 7, 17], [600, 1440, 4, 19]],
    "5": [[0, 480, 5, 13], [480, 600, 8, 18], [600, 1440, 6, 20]]
  },
  "2": {
    "1": [[0, 480, 2, 10], [480, 600, 5, 15], [600, 1440, 3, 20]],
    "3": [[0, 480, 3, 9], [480, 600, 7, 14], [600, 1440, 4, 12]],
    "6": [[0, 480, 6, 14], [480, 600, 9, 17], [600, 1440, 7, 19]],
    "7": [[0, 480, 4, 11], [480, 600, 6, 15], [600, 1440, 5, 13]]
  },
  "3": {
    "1": [[0, 480, 4, 12], [480, 600, 6, 16], [600, 1440, 5, 18]],
    "2": [[0, 480, 3, 9], [480, 600, 7, 14], [600, 1440, 4, 12]],
    "4": [[0, 480, 5, 13], [480, 600, 8, 16], [600, 1440, 6, 17]],
    "7": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "8": [[0, 480, 4, 11], [480, 600, 6, 14], [600, 1440, 5, 13]]
  },
  "4": {
    "1": [[0, 480, 3, 11], [480, 600, 7, 17], [600, 1440, 4, 19]],
    "3": [[0, 480, 5, 13], [480, 600, 8, 16], [600, 1440, 6, 17]],
    "5": [[0, 480, 6, 14], [480, 600, 9, 18], [600, 1440, 7, 20]],
    "8": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "9": [[0, 480, 5, 12], [480, 600, 7, 15], [600, 1440, 6, 14]]
  },
  "5": {
    "1": [[0, 480, 5, 13], [480, 600, 8, 18], [600, 1440, 6, 20]],
    "4": [[0, 480, 6, 14], [480, 600, 9, 18], [600, 1440, 7, 20]],
    "9": [[0, 480, 7, 15], [480, 600, 10, 19], [600, 1440, 8, 21]],
    "10": [[0, 480, 6, 13], [480, 600, 8, 16], [600, 1440, 7, 17]]
  },
  "6": {
    "2": [[0, 480, 6, 14], [480, 600, 9, 17], [600, 1440, 7, 19]],
    "7": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "11": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "7": {
    "2": [[0, 480, 4, 11], [480, 600, 6, 15], [600, 1440, 5, 13]],
    "3": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "6": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "8": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "11": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "12": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "8": {
    "3": [[0, 480, 4, 11], [480, 600, 6, 14], [600, 1440, 5, 13]],
    "4": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "7": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "9": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "12": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "13": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "9": {
    "4": [[0, 480, 5, 12], [480, 600, 7, 15], [600, 1440, 6, 14]],
    "5": [[0, 480, 7, 15], [480, 600, 10, 19], [600, 1440, 8, 21]],
    "8": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "10": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "13": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "14": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "10": {
    "5": [[0, 480, 6, 13], [480, 600, 8, 16], [600, 1440, 7, 17]],
    "9": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "14": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "15": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "11": {
    "6": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "7": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "12": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "16": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "17": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "12": {
    "7": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "8": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "11": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "13": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "17": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "18": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "13": {
    "8": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "9": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "12": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "14": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "18": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "19": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "14": {
    "9": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "10": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "13": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "15": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "19": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "15": {
    "10": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "14": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "19": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "16": {
    "11": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "17": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "20": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "17": {
    "11": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "12": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "16": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "18": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "20": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "18": {
    "12": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "13": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "17": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "19": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "20": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "19": {
    "13": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "14": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "15": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "18": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "20": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]]
  },
  "20": {
    "16": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "17": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "18": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "19": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]]
  }
}

network = RoadNetwork(network_data)
start = '1'
end = '12'
start_time = 470

min_robust_path, min_robust_cost, worst_case_time, best_case_time, alternative_path = network.find_min_robust_path(start, end, start_time)
print("最小鲁棒成本的路径:", min_robust_path)
print("最小鲁棒成本:", min_robust_cost)
print("原路径最坏时间:", worst_case_time)
print("备选路径最佳时间:", best_case_time)
print("备选路径:", alternative_path)