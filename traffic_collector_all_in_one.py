# -*- coding: utf-8 -*-
"""
路网数据采集系统 - 单文件完整版本
Traffic Data Collection System - All-in-One Version

功能特性：
- 基于高德地图API的实时路网通行时间采集
- 支持单次采集和连续定时采集
- 自动处理API频率限制和异常重试
- CSV格式数据存储和完整日志记录
- 实时进度监控和统计信息显示

使用方法：
1. 设置高德地图API密钥（环境变量或修改代码中的API_KEY）
2. 确保路网数据文件存在
3. 运行命令：
   - 单次采集：python traffic_collector_all_in_one.py --single
   - 连续采集：python traffic_collector_all_in_one.py --continuous
   - 测试配置：python traffic_collector_all_in_one.py --test-config

作者：AI Assistant
版本：1.0.0
"""

import os
import sys
import csv
import json
import time
import logging
import logging.handlers
import requests
import threading
import argparse
import signal
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Callable
from dataclasses import dataclass, field
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import re

# ================================
# 配置参数区域 - 可根据需要修改
# ================================

class Config:
    """系统配置类 - 所有可配置参数都在这里"""
    
    # 高德地图API配置
    API_KEY = "7fc11539ead7b69f460073d205714b4b"  # 请替换为您的API密钥
    API_BASE_URL = "https://restapi.amap.com/v3"
    API_DIRECTION_URL = f"{API_BASE_URL}/direction/driving"
    
    # API频率限制
    MAX_REQUESTS_PER_SECOND = 2      # 每秒最大请求数（降低以减少限制错误）
    MAX_REQUESTS_PER_DAY = 10000    # 每日最大请求数
    REQUEST_TIMEOUT = 10             # 请求超时时间（秒）
    MAX_RETRIES = 3                  # 最大重试次数
    RETRY_DELAY = 1.0               # 重试延迟（秒）

    # 数据采集配置
    COLLECTION_INTERVAL = 16        # 采集间隔（秒，默认2分钟）
    BATCH_SIZE = 2                  # 每批处理的路段数量（严格控制并发，符合3次/秒限制）
    BATCH_DELAY = 3.0               # 批次间延迟（秒，确保不超过API限制）
    
    # 文件路径配置
    ROAD_NETWORK_FILE = "me/论文/时段划分/数据采集/路网经纬度.txt"
    OUTPUT_DIR = "data_output"
    LOG_DIR = "logs"
    
    # 文件格式配置
    CSV_FILENAME_PREFIX = "traffic_data"
    CSV_ENCODING = "utf-8-sig"      # 支持中文的CSV编码
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_FILE_BACKUP_COUNT = 5
    
    @classmethod
    def get_api_key(cls):
        """获取API密钥，优先从环境变量读取"""
        return os.getenv('AMAP_API_KEY', cls.API_KEY)
    
    @classmethod
    def validate(cls):
        """验证配置"""
        api_key = cls.get_api_key()
        if api_key == "YOUR_AMAP_API_KEY_HERE":
            print("❌ 错误：请设置有效的高德地图API密钥")
            print("   方法1：设置环境变量 AMAP_API_KEY")
            print("   方法2：修改代码中的 Config.API_KEY")
            return False
        
        if not os.path.exists(cls.ROAD_NETWORK_FILE):
            print(f"❌ 错误：路网数据文件不存在: {cls.ROAD_NETWORK_FILE}")
            return False
        
        return True
    
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        os.makedirs(cls.LOG_DIR, exist_ok=True)
        os.makedirs(os.path.join(cls.OUTPUT_DIR, "csv"), exist_ok=True)
        os.makedirs(os.path.join(cls.OUTPUT_DIR, "json"), exist_ok=True)
        os.makedirs(os.path.join(cls.OUTPUT_DIR, "summary"), exist_ok=True)

# ================================
# 数据模型定义区域
# ================================

@dataclass
class Coordinate:
    """坐标点类"""
    longitude: float  # 经度
    latitude: float   # 纬度
    
    def __str__(self) -> str:
        return f"{self.longitude},{self.latitude}"
    
    @classmethod
    def from_string(cls, coord_str: str):
        """从字符串创建坐标对象"""
        try:
            lon, lat = map(float, coord_str.strip().split(','))
            return cls(longitude=lon, latitude=lat)
        except (ValueError, IndexError) as e:
            raise ValueError(f"无效的坐标格式: {coord_str}") from e

@dataclass
class RoadSegment:
    """路段类"""
    segment_id: str           # 路段ID
    start_node: str          # 起始节点ID
    end_node: str            # 终止节点ID
    start_coord: Coordinate  # 起始坐标
    end_coord: Coordinate    # 终止坐标
    
    @classmethod
    def from_line(cls, line: str):
        """从文本行创建路段对象"""
        try:
            # 解析格式: "1-2：117.286508,31.750165               117.291575,31.750181"
            parts = line.strip().split('：')
            if len(parts) != 2:
                raise ValueError(f"无效的行格式: {line}")
            
            segment_id = parts[0].strip()
            coords_part = parts[1].strip()
            
            # 提取起始和终止节点ID
            start_node, end_node = segment_id.split('-')
            
            # 分割坐标（使用正则表达式处理多个空格）
            coords = re.split(r'\s+', coords_part)
            if len(coords) != 2:
                raise ValueError(f"坐标格式错误: {coords_part}")
            
            start_coord = Coordinate.from_string(coords[0])
            end_coord = Coordinate.from_string(coords[1])
            
            return cls(
                segment_id=segment_id,
                start_node=start_node,
                end_node=end_node,
                start_coord=start_coord,
                end_coord=end_coord
            )
        except Exception as e:
            raise ValueError(f"解析路段数据失败: {line}") from e

@dataclass
class TrafficData:
    """交通数据类"""
    segment_id: str                    # 路段ID
    collection_time: datetime          # 采集时间
    duration: Optional[int] = None     # 通行时间（秒）
    distance: Optional[int] = None     # 距离（米）
    traffic_status: Optional[str] = None  # 交通状况
    traffic_lights: Optional[int] = None  # 红绿灯数量
    tolls: Optional[int] = None        # 收费站数量
    restriction: Optional[int] = None  # 限行状态
    error_message: Optional[str] = None  # 错误信息
    
    def to_csv_row(self) -> List[str]:
        """转换为CSV行格式"""
        return [
            self.segment_id,
            self.collection_time.strftime('%Y-%m-%d %H:%M:%S'),
            str(self.duration) if self.duration is not None else '',
            str(self.distance) if self.distance is not None else '',
            self.traffic_status or '',
            str(self.traffic_lights) if self.traffic_lights is not None else '',
            str(self.tolls) if self.tolls is not None else '',
            str(self.restriction) if self.restriction is not None else '',
            self.error_message or ''
        ]
    
    @staticmethod
    def get_csv_headers() -> List[str]:
        """获取CSV表头"""
        return [
            '路段ID', '采集时间', '通行时间(秒)', '距离(米)', '交通状况',
            '红绿灯数量', '收费站数量', '限行状态', '错误信息'
        ]

@dataclass
class RoadNetwork:
    """路网类"""
    segments: List[RoadSegment] = field(default_factory=list)
    
    def __len__(self) -> int:
        return len(self.segments)
    
    def add_segment(self, segment: RoadSegment):
        """添加路段"""
        self.segments.append(segment)
    
    @classmethod
    def load_from_file(cls, file_path: str):
        """从文件加载路网数据"""
        network = cls()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:  # 跳过空行
                        continue
                    
                    try:
                        segment = RoadSegment.from_line(line)
                        network.add_segment(segment)
                    except ValueError as e:
                        print(f"警告: 第{line_num}行数据解析失败: {e}")
                        continue
        
        except FileNotFoundError:
            raise FileNotFoundError(f"路网数据文件不存在: {file_path}")
        except Exception as e:
            raise Exception(f"加载路网数据失败: {e}")
        
        return network

# ================================
# 日志系统设置区域
# ================================

def setup_logging():
    """设置日志系统"""
    Config.create_directories()
    
    # 设置日志级别
    log_level = getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO)
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter(Config.LOG_FORMAT, datefmt='%Y-%m-%d %H:%M:%S')
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 主日志文件处理器
    main_log_file = os.path.join(Config.LOG_DIR, "traffic_collector.log")
    main_handler = logging.handlers.RotatingFileHandler(
        main_log_file,
        maxBytes=Config.LOG_FILE_MAX_SIZE,
        backupCount=Config.LOG_FILE_BACKUP_COUNT,
        encoding='utf-8'
    )
    main_handler.setLevel(log_level)
    main_handler.setFormatter(formatter)
    root_logger.addHandler(main_handler)
    
    # 错误日志文件处理器
    error_log_file = os.path.join(Config.LOG_DIR, "error.log")
    error_handler = logging.handlers.RotatingFileHandler(
        error_log_file,
        maxBytes=Config.LOG_FILE_MAX_SIZE,
        backupCount=Config.LOG_FILE_BACKUP_COUNT,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)
    root_logger.addHandler(error_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

# ================================
# 高德地图API调用区域
# ================================

@dataclass
class APIRateLimit:
    """API限制管理类"""
    requests_count: int = 0
    last_reset_time: datetime = None
    daily_requests: int = 0
    daily_reset_time: datetime = None

    def __post_init__(self):
        if self.last_reset_time is None:
            self.last_reset_time = datetime.now()
        if self.daily_reset_time is None:
            self.daily_reset_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

class AmapAPIClient:
    """高德地图API客户端"""

    def __init__(self):
        self.api_key = Config.get_api_key()
        self.base_url = Config.API_BASE_URL
        self.direction_url = Config.API_DIRECTION_URL
        self.timeout = Config.REQUEST_TIMEOUT
        self.max_retries = Config.MAX_RETRIES
        self.retry_delay = Config.RETRY_DELAY

        # 速率限制管理
        self.rate_limit = APIRateLimit()

        # 设置日志
        self.logger = logging.getLogger(__name__)

        # 创建会话以复用连接
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'TrafficDataCollector/1.0',
            'Accept': 'application/json'
        })

    def _check_rate_limit(self) -> bool:
        """检查API调用频率限制"""
        now = datetime.now()

        # 检查每秒限制
        if (now - self.rate_limit.last_reset_time).total_seconds() >= 1:
            self.rate_limit.requests_count = 0
            self.rate_limit.last_reset_time = now

        if self.rate_limit.requests_count >= Config.MAX_REQUESTS_PER_SECOND:
            self.logger.warning("达到每秒API调用限制，等待...")
            time.sleep(1)
            return self._check_rate_limit()

        # 检查每日限制
        if now.date() > self.rate_limit.daily_reset_time.date():
            self.rate_limit.daily_requests = 0
            self.rate_limit.daily_reset_time = now.replace(hour=0, minute=0, second=0, microsecond=0)

        if self.rate_limit.daily_requests >= Config.MAX_REQUESTS_PER_DAY:
            self.logger.error("达到每日API调用限制")
            return False

        return True

    def _make_request(self, url: str, params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """发起API请求"""
        if not self._check_rate_limit():
            return None

        # 添加API密钥
        params['key'] = self.api_key

        for attempt in range(self.max_retries + 1):
            try:
                self.logger.debug(f"API请求 (尝试 {attempt + 1}): {url}")

                response = self.session.get(url, params=params, timeout=self.timeout)

                # 更新计数器
                self.rate_limit.requests_count += 1
                self.rate_limit.daily_requests += 1

                response.raise_for_status()
                data = response.json()

                # 检查API响应状态
                if data.get('status') == '1':
                    self.logger.debug("API请求成功")
                    return data
                else:
                    error_msg = data.get('info', '未知错误')
                    self.logger.error(f"API返回错误: {error_msg}")

                    # 某些错误不需要重试
                    if data.get('infocode') in ['10001', '10002', '10003']:  # 密钥错误等
                        break

                    if attempt < self.max_retries:
                        time.sleep(self.retry_delay * (attempt + 1))
                        continue

                    return None

            except requests.exceptions.Timeout:
                self.logger.warning(f"API请求超时 (尝试 {attempt + 1})")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue

            except requests.exceptions.RequestException as e:
                self.logger.error(f"API请求异常 (尝试 {attempt + 1}): {e}")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue

            except json.JSONDecodeError as e:
                self.logger.error(f"API响应JSON解析失败: {e}")
                if attempt < self.max_retries:
                    time.sleep(self.retry_delay * (attempt + 1))
                    continue

        return None

    def get_driving_route(self, start_coord: Coordinate, end_coord: Coordinate) -> Optional[TrafficData]:
        """获取驾车路径规划信息"""
        params = {
            'origin': str(start_coord),
            'destination': str(end_coord),
            'strategy': 0,  # 速度优先
            'extensions': 'all',  # 返回详细信息
            'output': 'json'
        }

        try:
            data = self._make_request(self.direction_url, params)

            if not data:
                return None

            # 解析路径信息
            route = data.get('route', {})
            paths = route.get('paths', [])

            if not paths:
                self.logger.warning("未找到路径信息")
                return None

            # 取第一条路径
            path = paths[0]

            # 提取交通信息
            duration = int(path.get('duration', 0))  # 秒
            distance = int(path.get('distance', 0))  # 米
            traffic_lights = int(path.get('traffic_lights', 0))  # 红绿灯数量
            tolls = int(path.get('tolls', 0))  # 收费站数量
            restriction = int(path.get('restriction', 0))  # 限行状态

            # 获取交通状况描述
            traffic_status = self._get_traffic_status(duration, distance)

            traffic_data = TrafficData(
                segment_id="",  # 将在调用处设置
                collection_time=datetime.now(),
                duration=duration,
                distance=distance,
                traffic_status=traffic_status,
                traffic_lights=traffic_lights,
                tolls=tolls,
                restriction=restriction
            )

            self.logger.debug(f"路径信息: 时长={duration}秒, 距离={distance}米")
            return traffic_data

        except Exception as e:
            self.logger.error(f"获取路径信息失败: {e}")
            return None

    def _get_traffic_status(self, duration: int, distance: int) -> str:
        """根据时长和距离判断交通状况"""
        if distance == 0:
            return "未知"

        # 计算平均速度 (km/h)
        speed = (distance / 1000) / (duration / 3600) if duration > 0 else 0

        if speed >= 40:
            return "畅通"
        elif speed >= 25:
            return "缓慢"
        elif speed >= 15:
            return "拥堵"
        else:
            return "严重拥堵"

    def get_segment_traffic_data(self, segment: RoadSegment) -> TrafficData:
        """获取路段交通数据"""
        try:
            traffic_data = self.get_driving_route(segment.start_coord, segment.end_coord)

            if traffic_data:
                traffic_data.segment_id = segment.segment_id
                return traffic_data
            else:
                # 创建错误数据记录
                return TrafficData(
                    segment_id=segment.segment_id,
                    collection_time=datetime.now(),
                    error_message="API调用失败"
                )

        except Exception as e:
            self.logger.error(f"获取路段 {segment.segment_id} 交通数据失败: {e}")
            return TrafficData(
                segment_id=segment.segment_id,
                collection_time=datetime.now(),
                error_message=str(e)
            )

    def get_api_usage_stats(self) -> Dict[str, Any]:
        """获取API使用统计"""
        return {
            'daily_requests': self.rate_limit.daily_requests,
            'daily_limit': Config.MAX_REQUESTS_PER_DAY,
            'remaining_daily': Config.MAX_REQUESTS_PER_DAY - self.rate_limit.daily_requests,
            'current_second_requests': self.rate_limit.requests_count,
            'per_second_limit': Config.MAX_REQUESTS_PER_SECOND
        }

    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()

# ================================
# 数据存储区域
# ================================

class DataStorage:
    """数据存储管理器"""

    def __init__(self):
        self.output_dir = Path(Config.OUTPUT_DIR)
        self.csv_encoding = Config.CSV_ENCODING
        self.logger = logging.getLogger(__name__)

        # 确保输出目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # 创建子目录
        self.csv_dir = self.output_dir / "csv"
        self.json_dir = self.output_dir / "json"
        self.summary_dir = self.output_dir / "summary"

        for dir_path in [self.csv_dir, self.json_dir, self.summary_dir]:
            dir_path.mkdir(exist_ok=True)

    def save_traffic_data(self, traffic_data_list: List[TrafficData], session_id: str) -> bool:
        """保存交通数据"""
        try:
            # 保存为CSV格式
            csv_success = self._save_as_csv(traffic_data_list, session_id)

            # 保存为JSON格式（备份）
            json_success = self._save_as_json(traffic_data_list, session_id)

            # 更新汇总文件
            summary_success = self._update_summary(traffic_data_list, session_id)

            if csv_success:
                self.logger.info(f"成功保存 {len(traffic_data_list)} 条数据到CSV文件")

            return csv_success and json_success and summary_success

        except Exception as e:
            self.logger.error(f"保存数据失败: {e}")
            return False

    def _save_as_csv(self, traffic_data_list: List[TrafficData], session_id: str) -> bool:
        """保存为CSV格式"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{Config.CSV_FILENAME_PREFIX}_{timestamp}.csv"
            file_path = self.csv_dir / filename

            with open(file_path, 'w', newline='', encoding=self.csv_encoding) as csvfile:
                writer = csv.writer(csvfile)

                # 写入表头
                headers = TrafficData.get_csv_headers()
                writer.writerow(headers)

                # 写入数据
                for traffic_data in traffic_data_list:
                    writer.writerow(traffic_data.to_csv_row())

            self.logger.debug(f"CSV文件保存成功: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存CSV文件失败: {e}")
            return False

    def _save_as_json(self, traffic_data_list: List[TrafficData], session_id: str) -> bool:
        """保存为JSON格式"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"traffic_data_{timestamp}.json"
            file_path = self.json_dir / filename

            # 转换为字典列表
            data_dicts = []
            for traffic_data in traffic_data_list:
                data_dict = {
                    'segment_id': traffic_data.segment_id,
                    'collection_time': traffic_data.collection_time.isoformat(),
                    'duration': traffic_data.duration,
                    'distance': traffic_data.distance,
                    'traffic_status': traffic_data.traffic_status,
                    'traffic_lights': traffic_data.traffic_lights,
                    'tolls': traffic_data.tolls,
                    'restriction': traffic_data.restriction,
                    'error_message': traffic_data.error_message
                }
                data_dicts.append(data_dict)

            # 添加元数据
            json_data = {
                'session_id': session_id,
                'collection_time': datetime.now().isoformat(),
                'total_records': len(traffic_data_list),
                'data': data_dicts
            }

            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(json_data, jsonfile, ensure_ascii=False, indent=2)

            self.logger.debug(f"JSON文件保存成功: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"保存JSON文件失败: {e}")
            return False

    def _update_summary(self, traffic_data_list: List[TrafficData], session_id: str) -> bool:
        """更新汇总文件"""
        try:
            summary_file = self.summary_dir / "collection_summary.csv"

            # 计算统计信息
            total_records = len(traffic_data_list)
            successful_records = sum(1 for data in traffic_data_list if not data.error_message)
            failed_records = total_records - successful_records
            success_rate = (successful_records / total_records * 100) if total_records > 0 else 0

            # 计算平均通行时间
            valid_durations = [data.duration for data in traffic_data_list
                             if data.duration is not None and not data.error_message]
            avg_duration = sum(valid_durations) / len(valid_durations) if valid_durations else 0

            # 准备汇总数据
            summary_data = {
                'session_id': session_id,
                'collection_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_records': total_records,
                'successful_records': successful_records,
                'failed_records': failed_records,
                'success_rate': f"{success_rate:.2f}%",
                'avg_duration_seconds': f"{avg_duration:.1f}" if avg_duration > 0 else "N/A"
            }

            # 检查文件是否存在
            file_exists = summary_file.exists()

            with open(summary_file, 'a', newline='', encoding=self.csv_encoding) as csvfile:
                fieldnames = summary_data.keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # 如果文件不存在，写入表头
                if not file_exists:
                    writer.writeheader()

                # 写入数据
                writer.writerow(summary_data)

            self.logger.debug(f"汇总文件更新成功: {summary_file}")
            return True

        except Exception as e:
            self.logger.error(f"更新汇总文件失败: {e}")
            return False

    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            csv_files = list(self.csv_dir.glob("*.csv"))
            json_files = list(self.json_dir.glob("*.json"))

            # 计算总文件大小
            total_size = 0
            for file_path in csv_files + json_files:
                total_size += file_path.stat().st_size

            return {
                'csv_files_count': len(csv_files),
                'json_files_count': len(json_files),
                'total_files': len(csv_files + json_files),
                'total_size_mb': total_size / (1024 * 1024),
                'output_directory': str(self.output_dir)
            }

        except Exception as e:
            self.logger.error(f"获取存储统计失败: {e}")
            return {}

# ================================
# 数据采集核心区域
# ================================

class TrafficDataCollector:
    """交通数据采集器"""

    def __init__(self):
        self.road_network: Optional[RoadNetwork] = None
        self.api_client: Optional[AmapAPIClient] = None
        self.data_storage: Optional[DataStorage] = None
        self.logger = logging.getLogger(__name__)

        # 采集控制
        self.is_running = False
        self.collection_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()

        # 统计信息
        self.total_collections = 0
        self.successful_collections = 0
        self.failed_collections = 0
        self.start_time: Optional[datetime] = None

        # 回调函数
        self.progress_callback: Optional[Callable] = None

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def initialize(self) -> bool:
        """初始化采集器"""
        try:
            self.logger.info("初始化数据采集器...")

            # 加载路网数据
            self.logger.info("加载路网数据...")
            self.road_network = RoadNetwork.load_from_file(Config.ROAD_NETWORK_FILE)
            self.logger.info(f"成功加载 {len(self.road_network)} 个路段")

            # 初始化API客户端
            self.logger.info("初始化高德地图API客户端...")
            self.api_client = AmapAPIClient()

            # 初始化数据存储
            self.logger.info("初始化数据存储...")
            self.data_storage = DataStorage()

            self.logger.info("数据采集器初始化完成")
            return True

        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            return False

    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"接收到信号 {signum}，正在停止采集...")
        self.stop()

    def collect_single_batch(self, segments: List[RoadSegment]) -> List[TrafficData]:
        """采集单批路段数据"""
        results = []

        # 使用线程池并发采集
        with ThreadPoolExecutor(max_workers=Config.BATCH_SIZE) as executor:
            # 提交任务
            future_to_segment = {
                executor.submit(self.api_client.get_segment_traffic_data, segment): segment
                for segment in segments
            }

            # 收集结果
            for future in as_completed(future_to_segment):
                segment = future_to_segment[future]
                try:
                    traffic_data = future.result(timeout=30)  # 30秒超时
                    results.append(traffic_data)

                    if traffic_data.error_message:
                        self.failed_collections += 1
                        self.logger.warning(f"路段 {segment.segment_id} 采集失败: {traffic_data.error_message}")
                    else:
                        self.successful_collections += 1
                        self.logger.debug(f"路段 {segment.segment_id} 采集成功")

                except Exception as e:
                    self.failed_collections += 1
                    self.logger.error(f"路段 {segment.segment_id} 采集异常: {e}")

                    # 创建错误记录
                    error_data = TrafficData(
                        segment_id=segment.segment_id,
                        collection_time=datetime.now(),
                        error_message=str(e)
                    )
                    results.append(error_data)

                self.total_collections += 1

                # 调用进度回调
                if self.progress_callback:
                    self.progress_callback(self.total_collections, len(self.road_network))

        return results

    def collect_all_segments(self) -> List[TrafficData]:
        """采集所有路段数据"""
        if not self.road_network:
            raise RuntimeError("路网数据未加载")

        all_results = []
        segments = self.road_network.segments
        batch_size = Config.BATCH_SIZE

        self.logger.info(f"开始采集 {len(segments)} 个路段的数据，批次大小: {batch_size}")

        # 分批处理
        for i in range(0, len(segments), batch_size):
            if self.stop_event.is_set():
                self.logger.info("收到停止信号，中断采集")
                break

            batch = segments[i:i + batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(segments) + batch_size - 1) // batch_size

            self.logger.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch)} 个路段")

            try:
                batch_results = self.collect_single_batch(batch)
                all_results.extend(batch_results)

                # 批次间延迟
                if i + batch_size < len(segments) and not self.stop_event.is_set():
                    time.sleep(Config.BATCH_DELAY)

            except Exception as e:
                self.logger.error(f"批次 {batch_num} 处理失败: {e}")
                continue

        return all_results

    def single_collection(self) -> bool:
        """执行一次完整的数据采集"""
        session_id = datetime.now().strftime("%Y%m%d_%H%M%S")

        try:
            self.logger.info(f"开始数据采集会话: {session_id}")

            # 重置计数器
            self.total_collections = 0
            self.successful_collections = 0
            self.failed_collections = 0

            # 采集数据
            traffic_data_list = self.collect_all_segments()

            # 保存数据
            if traffic_data_list:
                self.data_storage.save_traffic_data(traffic_data_list, session_id)
                self.logger.info(f"保存了 {len(traffic_data_list)} 条数据")

            # 记录统计信息
            success_rate = (self.successful_collections / len(self.road_network) * 100) if len(self.road_network) > 0 else 0

            self.logger.info(
                f"采集会话 {session_id} 完成: "
                f"成功率 {success_rate:.1f}% "
                f"({self.successful_collections}/{len(self.road_network)})"
            )

            # 记录API使用情况
            api_stats = self.api_client.get_api_usage_stats()
            self.logger.info(f"API使用情况: 今日已用 {api_stats['daily_requests']}/{api_stats['daily_limit']}")

            return True

        except Exception as e:
            self.logger.error(f"数据采集会话 {session_id} 失败: {e}")
            return False

    def start_continuous_collection(self, progress_callback: Optional[Callable] = None):
        """开始连续数据采集"""
        if self.is_running:
            self.logger.warning("数据采集已在运行中")
            return

        self.progress_callback = progress_callback
        self.is_running = True
        self.start_time = datetime.now()
        self.stop_event.clear()

        self.collection_thread = threading.Thread(
            target=self._collection_loop,
            name="DataCollectionThread"
        )
        self.collection_thread.start()

        self.logger.info("开始连续数据采集")

    def _collection_loop(self):
        """采集循环"""
        interval = Config.COLLECTION_INTERVAL

        while not self.stop_event.is_set():
            try:
                collection_start = datetime.now()

                # 执行一次采集
                success = self.single_collection()

                if not success:
                    self.logger.error("数据采集失败")

                # 计算下次采集时间
                collection_duration = (datetime.now() - collection_start).total_seconds()
                sleep_time = max(0, interval - collection_duration)

                if sleep_time > 0:
                    self.logger.info(f"等待 {sleep_time:.1f} 秒后进行下次采集")
                    if self.stop_event.wait(sleep_time):
                        break
                else:
                    self.logger.warning(f"采集耗时 {collection_duration:.1f}秒，超过间隔时间 {interval}秒")

            except Exception as e:
                self.logger.error(f"采集循环异常: {e}")
                # 发生异常时等待一段时间再继续
                if not self.stop_event.wait(60):  # 等待1分钟
                    continue
                else:
                    break

        self.logger.info("数据采集循环结束")

    def stop(self):
        """停止数据采集"""
        if not self.is_running:
            return

        self.logger.info("正在停止数据采集...")
        self.stop_event.set()

        if self.collection_thread and self.collection_thread.is_alive():
            self.collection_thread.join(timeout=30)
            if self.collection_thread.is_alive():
                self.logger.warning("采集线程未能正常停止")

        self.is_running = False
        self.logger.info("数据采集已停止")

    def get_status(self) -> dict:
        """获取采集器状态"""
        status = {
            'is_running': self.is_running,
            'total_collections': self.total_collections,
            'successful_collections': self.successful_collections,
            'failed_collections': self.failed_collections,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'road_network_loaded': self.road_network is not None,
            'total_segments': len(self.road_network) if self.road_network else 0
        }

        if self.api_client:
            status['api_usage'] = self.api_client.get_api_usage_stats()

        return status

    def cleanup(self):
        """清理资源"""
        self.stop()

        if self.api_client:
            self.api_client.close()

        self.logger.info("资源清理完成")

# ================================
# 主程序和用户界面区域
# ================================

def print_banner():
    """打印程序横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    路网数据采集系统                          ║
    ║                Traffic Data Collection System                ║
    ║                                                              ║
    ║  版本: 1.0.0 (单文件版本)                                   ║
    ║  功能: 基于高德地图API的实时路网通行时间采集                 ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def progress_callback(current: int, total: int):
    """进度回调函数"""
    percentage = (current / total) * 100
    bar_length = 50
    filled_length = int(bar_length * current // total)
    bar = '█' * filled_length + '-' * (bar_length - filled_length)

    print(f'\r进度: |{bar}| {current}/{total} ({percentage:.1f}%)', end='', flush=True)

    if current == total:
        print()  # 换行

def run_single_collection(collector: TrafficDataCollector) -> bool:
    """运行单次数据采集"""
    print("\n开始单次数据采集...")
    print(f"路网规模: {len(collector.road_network)} 个路段")
    print(f"预计耗时: {len(collector.road_network) * 2 / 60:.1f} 分钟")

    start_time = datetime.now()
    success = collector.single_collection()
    end_time = datetime.now()

    duration = (end_time - start_time).total_seconds()

    if success:
        print(f"\n✅ 单次采集完成！耗时: {duration:.1f} 秒")

        # 显示统计信息
        status = collector.get_status()
        print(f"成功采集: {status['successful_collections']} 个路段")
        print(f"失败采集: {status['failed_collections']} 个路段")

        if status['total_collections'] > 0:
            success_rate = status['successful_collections'] / status['total_collections'] * 100
            print(f"成功率: {success_rate:.1f}%")

        # 显示API使用情况
        if 'api_usage' in status:
            api_usage = status['api_usage']
            print(f"API使用: {api_usage['daily_requests']}/{api_usage['daily_limit']} (剩余: {api_usage['remaining_daily']})")
    else:
        print(f"\n❌ 单次采集失败！耗时: {duration:.1f} 秒")

    return success

def run_continuous_collection(collector: TrafficDataCollector, duration_hours: Optional[float] = None):
    """运行连续数据采集"""
    interval_minutes = Config.COLLECTION_INTERVAL / 60

    print(f"\n开始连续数据采集...")
    print(f"采集间隔: {interval_minutes:.1f} 分钟")
    print(f"路网规模: {len(collector.road_network)} 个路段")

    if duration_hours:
        end_time = datetime.now() + timedelta(hours=duration_hours)
        print(f"运行时长: {duration_hours} 小时 (至 {end_time.strftime('%Y-%m-%d %H:%M:%S')})")
    else:
        print("运行时长: 无限制 (按 Ctrl+C 停止)")

    print("\n按 Ctrl+C 可随时停止采集\n")

    # 启动连续采集
    collector.start_continuous_collection(progress_callback)

    try:
        if duration_hours:
            # 有时间限制的运行
            time.sleep(duration_hours * 3600)
            print(f"\n⏰ 达到预设运行时长 {duration_hours} 小时，正在停止...")
        else:
            # 无限制运行，等待用户中断
            while collector.is_running:
                time.sleep(1)

    except KeyboardInterrupt:
        print("\n\n🛑 接收到停止信号...")

    finally:
        collector.stop()
        print("✅ 数据采集已停止")

        # 显示最终统计
        status = collector.get_status()
        if status['start_time']:
            start_time = datetime.fromisoformat(status['start_time'])
            total_runtime = (datetime.now() - start_time).total_seconds() / 3600
            print(f"\n📊 运行统计:")
            print(f"总运行时间: {total_runtime:.2f} 小时")
            print(f"总采集次数: {status['total_collections']}")
            print(f"成功采集: {status['successful_collections']}")
            print(f"失败采集: {status['failed_collections']}")

            if status['total_collections'] > 0:
                success_rate = status['successful_collections'] / status['total_collections'] * 100
                print(f"总体成功率: {success_rate:.1f}%")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="路网数据采集系统 - 单文件版本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python traffic_collector_all_in_one.py --single                    # 执行单次采集
  python traffic_collector_all_in_one.py --continuous                # 连续采集（无限制）
  python traffic_collector_all_in_one.py --continuous --hours 2      # 连续采集2小时
  python traffic_collector_all_in_one.py --test-config               # 测试配置

配置说明:
  1. 设置API密钥: 修改代码中的 Config.API_KEY 或设置环境变量 AMAP_API_KEY
  2. 调整采集间隔: 修改 Config.COLLECTION_INTERVAL (秒)
  3. 调整批处理大小: 修改 Config.BATCH_SIZE
  4. 调整路网文件路径: 修改 Config.ROAD_NETWORK_FILE
        """
    )

    parser.add_argument('--single', action='store_true', help='执行单次数据采集')
    parser.add_argument('--continuous', action='store_true', help='连续数据采集')
    parser.add_argument('--hours', type=float, help='连续采集的小时数（仅与--continuous一起使用）')
    parser.add_argument('--test-config', action='store_true', help='测试配置并退出')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')

    args = parser.parse_args()

    # 打印横幅
    print_banner()

    # 设置日志级别
    if args.verbose:
        Config.LOG_LEVEL = "DEBUG"

    # 初始化日志系统
    setup_logging()

    # 验证配置
    print("🔧 检查系统配置...")
    if not Config.validate():
        print("❌ 配置验证失败，请检查配置")
        return 1

    # 创建必要目录
    Config.create_directories()
    print("✅ 配置验证通过")

    # 如果只是测试配置
    if args.test_config:
        print("\n📋 配置信息:")
        print(f"API密钥: {'已设置' if Config.get_api_key() != 'YOUR_AMAP_API_KEY_HERE' else '未设置'}")
        print(f"路网文件: {Config.ROAD_NETWORK_FILE}")
        print(f"输出目录: {Config.OUTPUT_DIR}")
        print(f"采集间隔: {Config.COLLECTION_INTERVAL} 秒")
        print(f"批处理大小: {Config.BATCH_SIZE}")
        print(f"日志目录: {Config.LOG_DIR}")
        print("\n✅ 配置测试完成")
        return 0

    # 检查运行模式
    if not args.single and not args.continuous:
        print("❌ 请指定运行模式: --single 或 --continuous")
        parser.print_help()
        return 1

    if args.single and args.continuous:
        print("❌ 不能同时指定 --single 和 --continuous")
        return 1

    if args.hours and not args.continuous:
        print("❌ --hours 参数只能与 --continuous 一起使用")
        return 1

    # 初始化数据采集器
    print("\n🚀 初始化数据采集器...")
    collector = TrafficDataCollector()

    if not collector.initialize():
        print("❌ 数据采集器初始化失败")
        return 1

    print("✅ 数据采集器初始化成功")

    try:
        if args.single:
            # 单次采集
            success = run_single_collection(collector)
            return 0 if success else 1

        elif args.continuous:
            # 连续采集
            run_continuous_collection(collector, args.hours)
            return 0

    except Exception as e:
        print(f"\n❌ 程序运行异常: {e}")
        return 1

    finally:
        # 清理资源
        print("\n🧹 清理系统资源...")
        collector.cleanup()
        print("✅ 资源清理完成")

if __name__ == "__main__":
    sys.exit(main())
