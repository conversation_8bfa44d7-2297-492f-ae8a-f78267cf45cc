import requests
import json
import pandas as pd
import time
import datetime
import os
import schedule
from pathlib import Path


class TrafficDataCollector:
    def __init__(self, key, roads_config):
        """
        初始化交通数据收集器

        参数:
            key: 高德地图API密钥
            roads_config: 道路配置列表，每个道路包含 name, adcode, level
        """
        self.key = key
        self.roads_config = roads_config
        self.data_dir = Path("traffic_data")
        self.data_dir.mkdir(exist_ok=True)

    def query_road_traffic(self, road_name, adcode, level):
        """
        查询指定道路的交通状况
        """
        url = "https://restapi.amap.com/v3/traffic/status/road"
        params = {
            "key": self.key,
            "name": road_name,
            "adcode": adcode,
            "level": level,
            "extensions": "all"  # 返回详细信息
        }

        try:
            response = requests.get(url, params=params)
            data = response.json()

            if data["status"] == "1":  # API调用成功
                return data
            else:
                print(f"API调用失败: {data['info']}")
                return None
        except Exception as e:
            print(f"请求异常: {e}")
            return None

    def calculate_travel_time(self, data):
        """
        从路况数据计算通行时间
        """
        if not data or "trafficinfo" not in data or "roads" not in data["trafficinfo"]:
            return None

        roads = data["trafficinfo"]["roads"]
        result = []

        for road in roads:
            road_name = road.get("name", "未知道路")
            status = road.get("status", "0")  # 路况状态: 0-未知, 1-畅通, 2-缓行, 3-拥堵
            speed = road.get("speed", 0)  # 平均速度 km/hr

            # 从polyline计算道路长度（这里简化处理，实际应该计算坐标点之间的距离）
            polyline = road.get("polyline", "")
            if polyline:
                # 这里只是估算长度，实际应该根据坐标点计算真实距离
                # 在实际应用中，应使用更准确的距离计算方法
                length_km = len(polyline.split(";")) * 0.1  # 简化估算，实际应计算真实距离
            else:
                length_km = 1  # 默认值

            # 计算通行时间（小时）
            if speed > 0:
                travel_time_hours = length_km / speed
                travel_time_minutes = travel_time_hours * 60
            else:
                travel_time_minutes = None

            result.append({
                "road_name": road_name,
                "status": status,
                "speed": speed,
                "length_km": length_km,
                "travel_time_minutes": travel_time_minutes,
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })

        return result

    def collect_data(self):
        """收集所有配置的道路数据"""
        print(f"开始数据采集: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        all_data = []

        for road in self.roads_config:
            print(f"查询道路: {road['name']}")
            traffic_data = self.query_road_traffic(road["name"], road["adcode"], road["level"])
            travel_times = self.calculate_travel_time(traffic_data)

            if travel_times:
                for item in travel_times:
                    item["query_road_name"] = road["name"]
                    all_data.append(item)

        # 将数据保存到当天的CSV文件
        if all_data:
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            file_path = self.data_dir / f"traffic_data_{today}.csv"

            # 检查文件是否存在，决定是否写入表头
            file_exists = os.path.isfile(file_path)

            df = pd.DataFrame(all_data)
            df.to_csv(file_path, mode='a', header=not file_exists, index=False)

            print(f"数据已保存到: {file_path}")
        else:
            print("没有收集到数据")

    def generate_daily_summary(self):
        """生成每日汇总数据，计算最大和最小通行时间"""
        yesterday = (datetime.datetime.now() - datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        file_path = self.data_dir / f"traffic_data_{yesterday}.csv"

        if not os.path.isfile(file_path):
            print(f"找不到昨日数据文件: {file_path}")
            return

        try:
            df = pd.read_csv(file_path)

            # 按道路名称分组计算统计值
            summary = df.groupby(["query_road_name", "road_name"]).agg({
                "travel_time_minutes": ["min", "max", "mean"],
                "speed": ["min", "max", "mean"]
            }).reset_index()

            # 保存汇总数据
            summary_path = self.data_dir / f"summary_{yesterday}.csv"
            summary.to_csv(summary_path, index=False)

            print(f"昨日数据汇总已保存到: {summary_path}")

        except Exception as e:
            print(f"汇总数据时出错: {e}")

    def run_scheduler(self):
        """运行定时调度器"""
        # 每30分钟采集一次数据
        schedule.every(30).minutes.do(self.collect_data)

        # 每天凌晨0:05生成前一天的汇总数据
        schedule.every().day.at("00:05").do(self.generate_daily_summary)

        # 立即执行一次数据采集
        self.collect_data()

        print("数据采集调度器已启动...")
        while True:
            schedule.run_pending()
            time.sleep(1)


# 使用示例
if __name__ == "__main__":
    # 替换为您的高德地图API密钥
    API_KEY = "你的高德地图API密钥"

    # 配置要监测的道路列表
    ROADS_CONFIG = [
        {"name": "北环大道", "adcode": "440300", "level": "5"},  # 深圳北环大道
        {"name": "南海大道", "adcode": "440300", "level": "5"},  # 深圳南海大道
        # 可以添加更多道路...
    ]

    collector = TrafficDataCollector(API_KEY, ROADS_CONFIG)
    collector.run_scheduler()