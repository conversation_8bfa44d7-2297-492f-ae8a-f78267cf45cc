"""
结合鲁棒成本和绝对后悔值的满意度
"""
import networkx as nx
from gurobipy import *
import matplotlib.pyplot as plt

alpha = 0.5  # 权重系数
Z_max = 50.4#53.7-3.3  # 最大鲁棒成本  计算方法：运行各种小模块-最大化鲁棒成本得到的上界阻抗总和减去各种小模块-寻找上下界最短路得到的下界阻抗总和
# 1, 2, 3, 4, 5, 9, 8, 7, 6, 11, 12, 13, 14, 15, 10, 16, 20, 19, 18, 17, 22, 23, 24, 21, 25, 26 - 1, 4, 8, 14, 19, 24, 26
S_min = 2.7  # 下界最短路  1, 5, 4, 8, 14, 19, 24, 26
S_max = 11.5 # 上界最短路  1, 3, 6, 13, 18, 22, 26
Smiddle_max = 28.3  # 中值最长路 1, 2, 11, 12, 17, 13, 8, 14, 9, 15, 10, 16, 20, 21, 25, 26
r = 1  # 起点
t = 26  # 终点
N = [i for i in range(1, 27)]
N_rt = [r, t]
N_center = N.copy()
for k in [r, t]:
    N_center.remove(k)
Links = [(1, 2, 0.2, 4), (1, 3, 1, 2), (1, 4, 0.9, 1.8), (1, 5, 0.2, 2.1),
    (2, 3, 0.9, 1.7), (2, 6, 0.8, 2.1),(2, 11, 1.5, 3.2),
    (3, 4, 1.1, 1.7), (3, 6, 1.5, 2.1), (3, 7, 0.8, 1.5),
    (4, 5, 0.1, 1.8), (4, 7, 1.1, 1.3),(4, 8, 0.5, 1.3), (4, 9, 1.1, 2.4),
    (5, 9, 0.9, 1.5), (5, 10, 0.5, 1.5),
    (6, 7, 0.7, 1.2), (6, 11, 0.7, 2.1),(6, 12, 1.4, 2.3), (6, 13, 0.9, 1),
    (7, 8, 0.2, 1.5), (7, 13, 0.4, 2.5),
    (8, 9, 0.6, 1.6), (8, 13, 1.1, 3.5),(8, 14, 0.6, 1.7),
    (9, 10, 0.7, 1.4), (9, 14, 0.7, 3), (9, 15, 1.2, 1.8),
    (10, 15, 1.1, 1.7), (10, 16, 1, 2.4),
    (11, 12, 0.7, 2.1),
    (12, 13, 0.3, 1.5), (12, 17, 0.4, 1.9),
    (13, 14, 1.2, 1.9), (13, 17, 1.5, 3.2),(13, 18, 0.9, 1.9),
    (14, 15, 0.5, 2.5), (14, 18, 0.9, 2.5), (14, 19, 0.6, 2.1),
    (15, 16, 1.1, 1.6), (15, 19, 1, 2.8),(15, 20, 1, 1.7),
    (16, 20, 1.2, 2.7), (16, 21, 0.8, 2.2),
    (17, 18, 1.2, 2.5), (17, 22, 1, 1.6),
    (18, 19, 0.5, 1.5),(18, 22, 0.5, 1.7),(18, 23, 0.6, 2.2),
    (19, 20, 0.3, 2.3), (19, 23, 0.9, 2.5), (19, 24, 0.2, 2.6),
    (20, 21, 1.1, 2.4),(20, 24, 0.5, 2.2),
    (21, 24, 1.2, 2.8), (21, 25, 1.1, 2.7),
    (22, 23, 0.7, 1.8), (22, 26, 0.9, 2.8),
    (23, 24, 2, 2.2), (23, 26, 0.8, 2.3),
    (24, 25, 0.5, 1.4), (24, 26, 0.5, 3.8),
    (25, 26, 2.4, 4.4),
    (2, 1, 0.2, 4), (3, 1, 1, 2), (4, 1, 0.9, 1.8), (5, 1, 0.2, 2.1),
    (3, 2, 0.9, 1.7), (6, 2, 0.8, 2.1), (11, 2, 1.5, 3.2),
    (4, 3, 1.1, 1.7), (6, 3, 1.5, 2.1), (7, 3, 0.8, 1.5),
    (5, 4, 0.1, 1.8), (7, 4, 1.1, 1.3), (8, 4, 0.5, 1.3), (9, 4, 1.1, 2.4),
    (9, 5, 0.9, 1.5), (10, 5, 0.5, 1.5),
    (7, 6, 0.7, 1.2), (11, 6, 0.7, 2.1), (12, 6, 1.4, 2.3), (13, 6, 0.9, 1),
    (8, 7, 0.2, 1.5), (13, 7, 0.4, 2.5),
    (9, 8, 0.6, 1.6), (13, 8, 1.1, 3.5), (14, 8, 0.6, 1.7),
    (10, 9, 0.7, 1.4), (14, 9, 0.7, 3), (15, 9, 1.2, 1.8),
    (15, 10, 1.1, 1.7), (16, 10, 1, 2.4),
    (12, 11, 0.7, 2.1),
    (13, 12, 0.3, 1.5), (17, 12, 0.4, 1.9),
    (14, 13, 1.2, 1.9), (17, 13, 1.5, 3.2), (18, 13, 0.9, 1.9),
    (15, 14, 0.5, 2.5), (18, 14, 0.9, 2.5), (19, 14, 0.6, 2.1),
    (16, 15, 1.1, 1.6), (19, 15, 1, 2.8), (20, 15, 1, 1.7),
    (20, 16, 1.2, 2.7), (21, 16, 0.8, 2.2),
    (18, 17, 1.2, 2.5), (22, 17, 1, 1.6),
    (19, 18, 0.5, 1.5), (22, 18, 0.5, 1.7), (23, 18, 0.6, 2.2),
    (20, 19, 0.3, 2.3), (23, 19, 0.9, 2.5), (24, 19, 0.2, 2.6),
    (21, 20, 1.1, 2.4), (24, 20, 0.5, 2.2),
    (24, 21, 1.2, 2.8), (25, 21, 1.1, 2.7),
    (23, 22, 0.7, 1.8), (26, 22, 0.9, 2.8),
    (24, 23, 2, 2.2), (26, 23, 0.8, 2.3),
    (25, 24, 0.5, 1.4), (26, 24, 0.5, 3.8),
    (26, 25, 2.4, 4.4)]
G = nx.DiGraph()
for k in range(len(Links)):
    G.add_edge(Links[k][0], Links[k][1], time=[Links[k][2], Links[k][3]],
               time_lb=Links[k][2], time_ub=Links[k][3], time_mid=(Links[k][2] + Links[k][3]) / 2)
time = nx.get_edge_attributes(G, 'time')
time_lb = nx.get_edge_attributes(G, 'time_lb')
time_ub = nx.get_edge_attributes(G, 'time_ub')
# 路网中的所有边
L = []
for item in time_lb.items():
    L.append(item[0])
Lt = tuplelist(L)


# print('路网所有的边：',L)

# 定义加割函数addBendersCuts
def addBendersCuts(d_obj, x):
    # print(x)
    if d.status == GRB.Status.UNBOUNDED:  # 模型无界
        ## 取极射线，向模型中添加可行割
        ray = d.UnbdRay
        # print(ray)
        ## 添加可行割
        m.addConstr(1 - (alpha * (1-((quicksum(time_ub[i, j] * p[i, j] for i, j in L) -
                             quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j])
                                      * ray[i, j] for i, j in L))/Z_max)) + (1 - alpha) *
                    (1-(quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L) - S_min - S_max)/
                     (2*Smiddle_max - S_min - S_max))) <= 0)
        print("*****加入可行割*****")
        ## 没有最优解，默认等于上次迭代的结果
        d_obj.append(d_obj[-1])
    elif d.status == GRB.Status.OPTIMAL:  # 发现最优解
        ## 添加最优割
        m.addConstr(1 - (alpha * (1 - ((quicksum(time_ub[i, j] * p[i, j] for i, j in L) -
                                   quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j])
                                        * w[i, j].x for i, j in L)) / Z_max)) + (1 - alpha) *
                    (1 - (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L) - S_min - S_max) /
                     (2*Smiddle_max - S_min - S_max))) <= z)
        print("*****加入最优割*****")
        ## 将最优解与当前主问题的f(y*)相加构成问题的上界加入到集合中
        d_obj.append(d.objVal)  # 获取最优解
    else:  # 其他状态，模型不可行
        print(d.status)


try:
    ## 初始化下界记录数组
    m_obj = []
    ## 创建限制主问题MP
    m = Model('masterproblem')  # Benders Master Problem
    ## 创建对偶子问题SP_Dual
    d = Model('dual_subproblem')  # dual of Benders SubProblem
    ## 注意，这里是加入限制主问题的变量，而不是初始子问题的变量
    ## 向MP中添加变量
    z = m.addVar(vtype=GRB.CONTINUOUS, name='z')
    p = m.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='p')


    ## 设置MP的目标函数
    m.setObjective(z, GRB.MINIMIZE)
    ## 向对偶子问题中添加变量
    w = d.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='w')

    # 添加约束
    ## 向主问题中加入约束
    ###起点约束
    m_con1 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select(r, '*')) == 1)
    m_con2 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', r)) == 0)
    ###终点约束
    m_con3 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', t)) == 1)
    m_con4 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select(t, '*')) == 0)
    ###中间点约束
    for j in N_center:
        m_con5 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', j))
                             == quicksum(p[j, k] for j, k in Lt.select(j, '*')))
    m_con6 = m.addConstr(quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L) >= 0)
    # 添加辅助变量 u[i]
    u = m.addVars(N, vtype=GRB.BINARY, name='u')

    # 将 u[i] 与 p[i, j] 关联
    for i in N:
        m.addConstr(quicksum(p[i, j] for j in N if (i, j) in Lt) <= u[i] * len(N))

    # 限制总的节点数
    m_con7 = m.addConstr(quicksum(u[i] for i in N) <= 9)
    ###有效路径约束
    # m_con6 = m.addConstr()
    ## 向对偶子问题中加入约束
    ###起点约束
    d_con1 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select(r, '*')) == 1)
    d_con2 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', r)) == 0)
    ###终点约束
    d_con3 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', t)) == 1)
    d_con4 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select(t, '*')) == 0)
    ###中间点约束
    for j in N_center:
        d_con5 = d.addConstr(quicksum(w[i, j] for i, j in Lt.select('*', j)) ==
                             quicksum(w[j, k] for j, k in Lt.select(j, '*')))

    d_con6 = d.addConstr(quicksum((time_lb[i, j] + time_ub[i, j]) * w[i, j] for i, j in L) >= 0)
    # 设置参数 InfUnbdInfo，此变量的解释 https://www.gurobi.com/documentation/9.5/refman/infunbdinfo.html
    # 这个参数是为了返回极射线
    d.Params.InfUnbdInfo = 1
    ## 设置迭代次数
    iteration = 0
    ## 初始化上界记录数组
    d_obj = [299]
    x = []  # x是用来接收变量取值的，与 .x方法两回事，要注意
    print("***第{}次求解主问题MP***".format(iteration + 1))
    ## 第一次求解MP
    m.optimize()
    print('第{}次主问题目标函数'.format(iteration + 1), m.objVal)
    for i, j in L:
        if p[i, j].x == 1:
            print('p[%d,%d] = %d' % (i, j, p[i, j].x))
    ## 将 MP 最优值放入 集合中
    m_obj.append(m.objval)
    ## z.x可以看作子问题最优解，可以理解为子问题的上界
    # while d_obj[-1] > z.x:  # 迭代主循环
    while iteration < 8:
        if iteration == 0:
            ## 第一次调用对偶子问题，需要构造它的目标函数
            d.setObjective(1 - (alpha * (1 - ((quicksum(time_ub[i, j] * p[i, j].x for i, j in L) -
                                   quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j].x)
                                            * w[i, j] for i, j in L)) / Z_max)) + (1 - alpha) *
                    (1 - (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j].x for i, j in L) - S_min - S_max) /
                     (2*Smiddle_max - S_min - S_max))), GRB.MAXIMIZE)
            print("***第{}次求解子问题对偶问题SP_dual***".format(iteration + 1))
            ## 求解对偶子问题
            d.optimize()
            print('第{}次对偶子问题目标函数'.format(iteration + 1), d.objVal)
            for i, j in L:
                if w[i, j].x == 1:
                    print('w[%d,%d] = %d' % (i, j, w[i, j].x))
            ## 调用加割函数
            addBendersCuts(d_obj, x)
            iteration = 1
        else:
            d.setObjective(1 - (alpha * (1 - ((quicksum(time_ub[i, j] * p[i, j].x for i, j in L) -
                                    quicksum((time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j].x)
                                            * w[i, j] for i, j in L)) / Z_max)) + (1 - alpha) *
                    (1 - (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j].x for i, j in L) - S_min - S_max) /
                      (2*Smiddle_max - S_min - S_max))), GRB.MAXIMIZE)
            print("***第{}次求解子问题对偶问题SP_dual***".format(iteration + 1))
            ## 求解对偶子问题
            d.optimize()
            print('第{}次对偶子问题目标函数'.format(iteration + 1), d.objVal)
            for i, j in L:
                if w[i, j].x == 1:
                    print('w[%d,%d] = %d' % (i, j, w[i, j].x))
            ## 调用加割函数
            addBendersCuts(d_obj, x)  # add Benders Cuts
            ## 迭代次数更新
            iteration = iteration + 1
        print("***第{}次求解主问题MP***".format(iteration + 1))
        ## 求解主问提（加割后）
        m.optimize()
        print('第{}次主问题目标函数'.format(iteration + 1), m.objVal)
        for i, j in L:
            if p[i, j].x == 1:
                print('p[%d,%d] = %d' % (i, j, p[i, j].x))
        ## 将当前fy*加入集合中
        m_obj.append(m.objval)
    ##打印路径决策变量为1的路径
    L_decision = []
    for i, j in L:
        if p[i, j].x == 1:
            L_decision.append((i, j))
    print('加权决策路径', L_decision)


except GurobiError as e:
    print('Error code ' + str(e.errno) + ": " + str(e))
except AttributeError:
    print('Encountered an attribute error')
print(m.objVal)