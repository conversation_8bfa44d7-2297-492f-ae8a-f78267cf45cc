import networkx as nx  # 导入 NetworkX 库用于处理图结构
import heapq  # 导入 heapq 作为优先队列（堆）实现 Dijkstra 算法
from collections import defaultdict  # 导入 defaultdict 以简化字典操作


from networkx import NetworkXNoPath

def yen_k_shortest_paths(graph, start, target, k=5):
    G = nx.DiGraph()
    for u in graph:
        for v, intervals in graph[u].items():
            min_t, _ = get_time_interval(intervals, 0)
            G.add_edge(u, v, weight=min_t)
    try:
        return list(nx.shortest_simple_paths(G, start, target, weight="weight"))[:k]
    except NetworkXNoPath:
        return []  # 返回空列表或提示无路径


def get_time_interval(edge_intervals, current_time):
    """
    获取当前时间段的阻抗范围（时间依赖）。
    :param edge_intervals: 该边的时间依赖阻抗列表，每个元素为 (起始时间, 结束时间, 最小阻抗, 最大阻抗)
    :param current_time: 当前时间
    :return: (最小阻抗, 最大阻抗)
    """
    for start, end, min_t, max_t in edge_intervals:
        if start <= current_time < end:  # 确保当前时间在时间区间内
            return min_t, max_t  # 返回该时间段的阻抗
    return float('inf'), float('inf')  # 若不在任何时间段内，则返回无穷大


def compute_upper_bound_cost(graph, path, start_time):
    """
    计算路径的最大阻抗成本（上界）。
    :param graph: 原始时间依赖图
    :param path: 需要计算的路径（节点序列）
    :param start_time: 出发时间
    :return: (路径最大成本, 时间记录)
    """
    current_time = start_time
    total_max = 0  # 记录最大总成本
    time_records = []  # 记录路径上的时间信息
    for i in range(len(path) - 1):
        u, v = path[i], path[i + 1]
        edge_intervals = graph[u].get(v, [])
        min_t, max_t = get_time_interval(edge_intervals, current_time)  # 获取当前时间的最大阻抗
        total_max += max_t
        time_records.append((u, v, current_time, max_t))  # 记录 (起点, 终点, 当前时间, 最大阻抗)
        current_time += max_t  # 更新当前时间
    return total_max, time_records


def build_modified_graph(original_graph, time_records, start_time):
    """
    生成修改后的图，将路径 p 中的所有边移除。
    :param original_graph: 原始时间依赖图
    :param time_records: 需要移除的路径时间记录
    :param start_time: 起始时间
    :return: 修改后的图
    """
    modified_graph = defaultdict(dict)
    removed_edges = {(record[0], record[1]) for record in time_records}  # 记录需要移除的边

    for u in original_graph:
        for v in original_graph[u]:
            if (u, v) not in removed_edges and (v, u) not in removed_edges:  # 仅保留未被移除的边
                min_t, _ = get_time_interval(original_graph[u][v], start_time)
                modified_graph[u][v] = min_t
                modified_graph[v][u] = min_t  # 假设双向通行
    return modified_graph


def time_dependent_dijkstra(graph, start, target, start_time):
    """
    运行时间依赖的 Dijkstra 算法计算最短路径。
    :param graph: 图（邻接表形式）
    :param start: 起点
    :param target: 终点
    :param start_time: 出发时间
    :return: (最短路径成本, 最短路径)
    """
    heap = [(0, start, start_time, [start])]  # (当前路径成本, 当前节点, 当前时间, 当前路径)
    visited = {}  # 记录访问过的最优时间

    while heap:
        cost, node, curr_time, path = heapq.heappop(heap)  # 取出当前最小成本的路径
        if node == target:  # 找到目标节点
            return cost, path
        if node in visited and curr_time >= visited[node]:  # 如果当前时间晚于已知最优时间，则跳过
            continue
        visited[node] = curr_time  # 记录当前节点的最优到达时间

        for neighbor in graph.get(node, {}):
            weight = graph[node][neighbor]
            new_time = curr_time + weight
            new_cost = cost + weight
            if neighbor not in visited or new_time < visited[neighbor]:  # 更新更优路径
                heapq.heappush(heap, (new_cost, neighbor, new_time, path + [neighbor]))

    return float('inf'), []  # 若找不到路径，则返回无穷大


def compute_robust_cost(graph, path, start, target, start_time):
    upper_cost, time_records = compute_upper_bound_cost(graph, path, start_time)
    modified_graph = build_modified_graph(graph, time_records, start_time)
    p_star_cost, p_star_path = time_dependent_dijkstra(modified_graph, start, target, start_time)
    if p_star_cost == float('inf'):
        return float('inf'), []
    return (upper_cost - p_star_cost), p_star_path


def find_min_robust_path(graph, start, target, start_time, k=5):  # 降低 k 值
    candidate_paths = yen_k_shortest_paths(graph, start, target, k)
    if not candidate_paths:
        raise ValueError("No valid paths between start and target")
    min_cost = float('inf')
    best_path = None
    best_p_star = None
    for path in candidate_paths:
        robust_cost, p_star_path = compute_robust_cost(graph, path, start, target, start_time)
        print(f"路径 {path} 鲁棒成本: {robust_cost}, p* 路径: {p_star_path}")
        if robust_cost < min_cost:
            min_cost = robust_cost
            best_path = path
            best_p_star = p_star_path
    return best_path, min_cost, best_p_star


# 示例（双向通行）
# 修改后的路网数据，每个时段的区间阻抗在2到20之间
graph = {
    'A': {
        'B': [
            [0, 480, 20, 30],
            [480, 600, 30, 50],
            [600, 1440, 20, 40]
        ],
        'C': [
            [0, 480, 11, 40],
            [480, 600, 20, 55],
            [600, 1440, 15, 35]
        ],
        'D': [
            [0, 480, 15, 35],
            [480, 600, 17, 44],
            [600, 1440, 18, 42]
        ]
    },
    'B': {
        'A': [
            [0, 480, 20, 30],
            [480, 600, 30, 50],
            [600, 1440, 20, 40]
        ],
        'C': [
            [0, 480, 8, 40],
            [480, 600, 20, 60],
            [600, 1440, 11, 35]
        ],
        'E': [
            [0, 480, 11, 35],
            [480, 600, 20, 44],
            [600, 1440, 21, 42]
        ]
    },
    'C': {
        'A': [
            [0, 480, 11, 40],
            [480, 600, 20, 55],
            [600, 1440, 15, 35]
        ],
        'B': [
            [0, 480, 8, 40],
            [480, 600, 20, 60],
            [600, 1440, 11, 35]
        ],
        'D': [
            [0, 480, 11, 44],
            [480, 600, 21, 50],
            [600, 1440, 21, 42]
        ],
        'E': [
            [0, 480, 11, 41],
            [480, 600, 12, 60],
            [600, 1440, 8, 35]
        ]
    },
    'D': {
        'A': [
            [0, 480, 15, 35],
            [480, 600, 17, 44],
            [600, 1440, 18, 42]
        ],
        'C': [
            [0, 480, 11, 44],
            [480, 600, 21, 50],
            [600, 1440, 21, 42]
        ],
        'E': [
            [0, 480, 20, 40],
            [480, 600, 15, 55],
            [600, 1440, 11, 35]
        ]
    }
}

best_path, min_cost, best_p_star = find_min_robust_path(graph, 'A', 'E', start_time=470)
print(f"最优路径: {best_path}, 最小鲁棒成本: {min_cost}, p* 路径: {best_p_star}")
