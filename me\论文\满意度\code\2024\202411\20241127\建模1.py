


# 失败




import gurobipy as grb
import networkx as nx
from gurobipy import *


# 路网数据 (i, j, l[i,j], u[i,j])
r = 1  # 起点
t = 29  # 终点
N = [i for i in range(1, 30)]
N_rt = [r, t]
N_center = N.copy()
for k in [r, t]:
    N_center.remove(k)
Links = [(1, 2, 6, 10), (1, 3, 6, 8), (2, 3, 6, 10), (1, 4, 5, 9), (3, 4, 6, 10), (1, 5, 3, 5), (4, 5, 1, 4),
         (2, 6, 6, 10), (3, 6, 6, 10), (3, 7, 6, 8), (6, 7, 6, 10), (3, 8, 6, 10), (4, 8, 6, 8), (7, 8, 6, 10),
         (4, 9, 3, 9), (8, 9, 6, 10), (4, 10, 6, 10), (5, 10, 3, 10), (9, 10, 6, 10), (6, 11, 6, 10), (7, 11, 6, 10),
         (7, 12, 6, 8), (11, 12, 6, 10), (7, 13, 6, 10), (8, 13, 6, 8), (12, 13, 6, 10), (8, 14, 6, 10), (9, 14, 6, 8),
         (13, 14, 6, 10), (9, 15, 3, 9), (10, 15, 6, 12), (14, 15, 6, 10), (10, 16, 3, 10), (15, 16, 6, 10),
         (11, 17, 6, 10),
         (12, 17, 6, 10), (12, 18, 6, 8), (13, 18, 6, 8), (17, 18, 6, 10), (13, 19, 6, 10), (14, 19, 6, 8),
         (18, 19, 6, 10),
         (14, 20, 6, 10), (15, 20, 4, 9), (19, 20, 6, 10), (15, 21, 6, 10), (16, 21, 3, 10), (20, 21, 6, 10),
         (17, 22, 6, 10),
         (18, 22, 6, 10), (18, 23, 6, 8), (19, 23, 6, 8), (22, 23, 6, 10), (19, 24, 6, 8), (20, 24, 5, 10),
         (23, 24, 6, 10),
         (20, 25, 6, 10), (21, 25, 3, 11), (24, 25, 6, 10), (22, 26, 6, 10), (23, 26, 6, 10), (23, 27, 6, 8),
         (24, 27, 4, 9),
         (26, 27, 2, 3), (24, 28, 6, 10), (25, 28, 3, 10), (27, 28, 6, 10), (26, 29, 5, 13), (27, 29, 2, 4),
         (28, 29, 3, 13),
         (2, 1, 6, 10), (3, 1, 6, 8), (3, 2, 6, 10), (4, 1, 5, 9), (4, 3, 6, 10), (5, 1, 3, 5), (5, 4, 1, 4),
         (6, 2, 6, 10), (6, 3, 6, 10), (7, 3, 6, 8), (7, 6, 6, 10), (8, 3, 6, 10), (8, 4, 6, 8), (8, 7, 6, 10),
         (9, 4, 3, 9), (9, 8, 6, 10), (10, 4, 6, 10), (10, 5, 3, 10), (10, 9, 6, 10), (11, 6, 6, 10), (11, 7, 6, 10),
         (12, 7, 6, 8), (12, 11, 6, 10), (13, 7, 6, 10), (13, 8, 6, 8), (13, 12, 6, 10), (14, 8, 6, 10), (14, 9, 6, 8),
         (14, 13, 6, 10), (15, 9, 3, 9), (15, 10, 6, 12), (15, 14, 6, 10), (16, 10, 3, 10), (16, 15, 6, 10),
         (17, 11, 6, 10),
         (17, 12, 6, 10), (18, 12, 6, 8), (18, 13, 6, 8), (18, 17, 6, 10), (19, 13, 6, 10), (19, 14, 6, 8),
         (19, 18, 6, 10),
         (20, 14, 6, 10), (20, 15, 4, 9), (20, 19, 6, 10), (21, 15, 6, 10), (21, 16, 3, 10), (21, 20, 6, 10),
         (22, 17, 6, 10),
         (22, 18, 6, 10), (23, 18, 6, 8), (23, 19, 6, 8), (23, 22, 6, 10), (24, 19, 6, 8), (24, 20, 5, 10),
         (24, 23, 6, 10),
         (25, 20, 6, 10), (25, 21, 3, 11), (25, 24, 6, 10), (26, 22, 6, 10), (26, 23, 6, 10), (27, 23, 6, 8),
         (27, 24, 4, 9),
         (27, 26, 2, 3), (28, 24, 6, 10), (28, 25, 3, 10), (28, 27, 6, 10), (29, 26, 5, 13), (29, 27, 2, 4),
         (29, 28, 3, 13)]
G = nx.DiGraph()
for k in range(len(Links)):
    G.add_edge(Links[k][0], Links[k][1], lu=[Links[k][2], Links[k][3]],
               l=Links[k][2], u=Links[k][3], mid=(Links[k][2] + Links[k][3]) / 2)
lu = nx.get_edge_attributes(G, 'lu')
l = nx.get_edge_attributes(G, 'l')
u = nx.get_edge_attributes(G, 'u')
# 路网中的所有边
L = []
for item in l.items():
    L.append(item[0])
Lt = tuplelist(L)

# 创建模型
m = grb.Model("RobustShortestPath")
p = m.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='p')

# 创建决策变量 p[i, j]，表示边 (i, j) 是否在路径中
p = {}
for i, j in L:
    p[i, j] = m.addVar(vtype=grb.GRB.BINARY, name=f"p_{i}_{j}")

# 创建特定情境变量 x[r, t]
x = {}
for i, j in L:
    x[i, j] = m.addVar(vtype=grb.GRB.CONTINUOUS, lb=0, name=f"x_{i}_{j}")

# 目标函数：最小化鲁棒成本 W
W = grb.quicksum(u[i, j] * p[i, j] for i, j in L) - grb.quicksum(x[r,t] for i, j in L)
m.setObjective(W, grb.GRB.MINIMIZE)

# 约束1: 最短路径约束
for (i, j, l, u) in Links:
    m_con1 = m.addConstr(x[r, r] == 0, name="StartNode")
    m_con2 = m.addConstr(x[r, j] <= x[r, i] + l[i, j] + (u[i, j] - l[i, j]) * p[i, j], name=f"ShortestPath_{i}_{j}")


# 约束2: 流量守恒约束
###起点约束
m_con3 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select(r, '*')) == 1)
m_con4 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', r)) == 0)
###终点约束
m_con5 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', t)) == 1)
m_con6 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select(t, '*')) == 0)
###中间点约束
for j in N_center:
    m_con7 = m.addConstr(quicksum(p[i, j] for i, j in Lt.select('*', j)) == quicksum(p[j, k] for j, k in Lt.select(j, '*')))

# 约束3: 决策变量约束
for (i, j, l, u) in Links:
    m_con8 = m.addConstr(p[i, j] for i, j in Lt >= 0)

# 求解模型
m.optimize()

# 输出结果
if m.status == grb.GRB.OPTIMAL:
    print("Optimal solution found:")
    for (i, j, l, u) in Links:
        if p[i, j].x > 0.5:  # 如果该边在最短路径中
            print(f"Edge ({i}, {j}) is in the path with decision variable p = {p[i, j].x}")
else:
    print("No optimal solution found.")
