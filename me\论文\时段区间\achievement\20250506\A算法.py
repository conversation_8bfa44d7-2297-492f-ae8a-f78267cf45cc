"""有点小问题：无法显示情景路径和最坏成本。只能直接求出最小鲁棒成本"""

import numpy as np
import heapq
from collections import defaultdict, deque
import multiprocessing
import time

# ==================== 数据准备部分 ====================
edges = [(1, 2), (2, 1), (1, 3), (3, 1), (1, 4), (4, 1), (1, 5), (5, 1),
         (2, 3), (3, 2), (2, 6), (6, 2), (2, 7), (7, 2), (3, 4), (4, 3),
         (3, 7), (7, 3), (3, 8), (8, 3), (4, 5), (5, 4), (4, 8), (8, 4),
         (4, 9), (9, 4), (5, 9), (9, 5), (5, 10), (10, 5), (6, 7), (7, 6),
         (6, 11), (11, 6), (7, 8), (8, 7), (7, 11), (11, 7), (7, 12), (12, 7),
         (8, 9), (9, 8), (8, 12), (12, 8), (8, 13), (13, 8), (9, 10), (10, 9),
         (9, 13), (13, 9), (9, 14), (14, 9), (10, 14), (14, 10), (10, 15), (15, 10),
         (11, 12), (12, 11), (11, 16), (16, 11), (11, 17), (17, 11), (12, 13), (13, 12),
         (12, 17), (17, 12), (12, 18), (18, 12), (13, 14), (14, 13), (13, 18), (18, 13),
         (13, 19), (19, 13), (14, 15), (15, 14), (14, 19), (19, 14), (15, 19), (19, 15),
         (16, 17), (17, 16), (16, 20), (20, 16), (17, 18), (18, 17), (17, 20), (20, 17),
         (18, 19), (19, 18), (18, 20), (20, 18), (19, 20), (20, 19)]  # 共94条边

l_base = [9, 9, 7, 7, 6, 6, 10, 10, 6, 6,
          12, 12, 10, 10, 11, 11, 8, 8, 10, 10,
          9, 9, 7, 7, 6, 6, 7, 7, 9, 9,
          8, 8, 6, 6, 9, 9, 8, 8, 6, 6,
          8, 8, 11, 11, 6, 6, 10, 10, 11, 11,
          6, 6, 10, 10, 10, 10, 8, 8, 6, 6,
          7, 7, 8, 8, 6, 6, 9, 9, 9, 9,
          10, 10, 6, 6, 7, 7, 8, 8, 6, 6,
          9, 9, 7, 10, 8, 8, 6, 6, 9, 9,  # 补充缺失的6个值
          7, 8, 9, 10]  # 总长度94

u_base = [14, 14, 13, 13, 12, 12, 15, 15,
          11, 11, 15, 15, 14, 14, 15, 15,
          12, 12, 15, 15, 14, 14, 13, 13,
          12, 12, 12, 12, 15, 15, 14, 14,
          12, 12, 14, 14, 14, 14, 11, 11,
          12, 12, 15, 15, 11, 11, 14, 14,
          15, 15, 11, 11, 14, 14, 14, 14,
          12, 12, 11, 11, 15, 15, 13, 13,
          11, 11, 15, 15, 15, 15, 13, 13,
          11, 11, 12, 12, 15, 15, 10, 10,
          15, 15, 14, 14, 15, 15, 12, 12,
          15, 15, 13, 14, 15, 15]  # 总长度8*11+6=94

# 道路等级 road_levels，长度和 edges 一样
road_levels = [1, 1, 2, 2, 3, 3, 4, 4,
               1, 1, 4, 4, 3, 3, 1, 1,
               2, 2, 1, 1, 1, 1, 4, 4,
               3, 3, 2, 2, 1, 1, 2, 2,
               1, 1, 2, 2, 2, 2, 3, 3,
               2, 2, 4, 4, 1, 1, 2, 2,
               2, 2, 3, 3, 4, 4, 1, 1,
               3, 3, 4, 4, 3, 3, 3, 3,
               2, 2, 1, 1, 3, 3, 4, 4,
               3, 3, 3, 3, 2, 2, 1, 1,
               4, 4, 1, 1, 4, 4, 2, 2,
               4, 4, 3, 3, 4, 4]

# 拆分congestion_factors，根据道路等级区分
congestion_factors = {
    1: {  # 快速路
        "00:00-07:00": (-0.8, -0.1),  # 平
        "07:00-07:10": (0.3, 1.1),  # 早高峰
        "07:10-07:20": (0.3, 1.2),
        "07:20-07:30": (0.4, 1.1),
        "07:30-07:40": (0.2, 1.2),
        "07:40-07:50": (0.4, 1.3),
        "07:50-08:00": (0.2, 1.5),
        "08:00-08:10": (0.3, 1.4),
        "08:10-08:20": (0.4, 1.4),
        "08:20-08:30": (0.3, 1.3),
        "08:30-08:40": (0.2, 1.2),
        "08:40-08:50": (0.4, 1.1),
        "08:50-09:00": (0.3, 1.0),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.2, 1.5),  # 晚高峰
        "19:00-00:00": (-0.8, -0.1)  # 平
    },
    2: {  # 主干路
        "00:00-07:00": (-0.7, -0.2),  # 平
        "07:00-07:10": (0.4, 1.0),  # 早高峰
        "07:10-07:20": (0.4, 1.1),
        "07:20-07:30": (0.5, 1.0),
        "07:30-07:40": (0.3, 1.1),
        "07:40-07:50": (0.5, 1.2),
        "07:50-08:00": (0.3, 1.4),
        "08:00-08:10": (0.4, 1.3),
        "08:10-08:20": (0.5, 1.3),
        "08:20-08:30": (0.4, 1.2),
        "08:30-08:40": (0.3, 1.1),
        "08:40-08:50": (0.5, 1.0),
        "08:50-09:00": (0.4, 0.9),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.3, 1.4),  # 晚高峰
        "19:00-00:00": (-0.7, -0.2)  # 平
    },
    3: {  # 次干路
        "00:00-07:00": (-0.6, -0.3),  # 平
        "07:00-07:10": (0.5, 0.9),  # 早高峰
        "07:10-07:20": (0.5, 1.0),
        "07:20-07:30": (0.6, 0.9),
        "07:30-07:40": (0.4, 1.0),
        "07:40-07:50": (0.6, 1.1),
        "07:50-08:00": (0.4, 1.3),
        "08:00-08:10": (0.5, 1.2),
        "08:10-08:20": (0.6, 1.2),
        "08:20-08:30": (0.5, 1.1),
        "08:30-08:40": (0.4, 1.0),
        "08:40-08:50": (0.6, 0.9),
        "08:50-09:00": (0.5, 0.8),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.4, 1.3),  # 晚高峰
        "19:00-00:00": (-0.6, -0.3)  # 平
    },
    4: {  # 支路
        "00:00-07:00": (-0.5, -0.4),  # 平
        "07:00-07:10": (0.6, 0.8),  # 早高峰
        "07:10-07:20": (0.6, 0.9),
        "07:20-07:30": (0.7, 0.8),
        "07:30-07:40": (0.5, 0.9),
        "07:40-07:50": (0.7, 1.0),
        "07:50-08:00": (0.5, 1.2),
        "08:00-08:10": (0.6, 1.1),
        "08:10-08:20": (0.7, 1.1),
        "08:20-08:30": (0.6, 1.0),
        "08:30-08:40": (0.5, 0.9),
        "08:40-08:50": (0.7, 0.8),
        "08:50-09:00": (0.6, 0.7),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.5, 1.2),  # 晚高峰
        "19:00-00:00": (-0.5, -0.4)  # 平
    }
}

# 注意！TIME_PERIODS也要适配成：
TIME_PERIODS = {
    "00:00-07:00": (0, 420),
    "07:00-07:10": (420, 430),
    "07:10-07:20": (430, 440),
    "07:20-07:30": (440, 450),
    "07:30-07:40": (450, 460),
    "07:40-07:50": (460, 470),
    "07:50-08:00": (470, 480),
    "08:00-08:10": (480, 490),
    "08:10-08:20": (490, 500),
    "08:20-08:30": (500, 510),
    "08:30-08:40": (510, 520),
    "08:40-08:50": (520, 530),
    "08:50-09:00": (530, 540),
    "09:00-17:00": (540, 1020),
    "17:00-19:00": (1020, 1140),
    "19:00-00:00": (1140, 1440)
}


# ==================== 时变阻抗生成模块 ====================
def generate_time_dependent_data(edges, l_base, u_base, congestion_factors, road_levels):
    network_data = defaultdict(dict)
    for idx, (i, j) in enumerate(edges):
        source = str(i)
        target = str(j)
        level = road_levels[idx]  # 获取边的道路等级
        time_ranges = []
        for period, (f, g) in congestion_factors[level].items():
            start, end = TIME_PERIODS[period]
            l_t = round(l_base[idx] * (1 + f), 2)
            u_t = round(u_base[idx] * (1 + g), 2)
            time_ranges.append([start, end, l_t, u_t])
        network_data[source][target] = time_ranges
    return network_data


# ==================== 增强版路径规划类 ====================
class EnhancedRoadNetwork:
    def __init__(self, edges, l_base, u_base, congestion_factors, road_levels):
        self.edges = edges
        self.l_base = l_base
        self.u_base = u_base
        self.congestion_factors = congestion_factors
        self.road_levels = road_levels

        network_data = generate_time_dependent_data(
            edges, l_base, u_base, congestion_factors, road_levels
        )
        self.graph = defaultdict(list)
        self._initialize_graph(network_data)
        self.robust_cost_cache = {}

    def _initialize_graph(self, network_data):
        """初始化图结构"""
        for source, neighbors in network_data.items():
            for target, time_ranges in neighbors.items():
                self.graph[source].append((target, time_ranges))

    def get_impedance(self, impedance_ranges, time):
        """获取当前时间的阻抗范围（带跨天处理）"""
        time %= 1440  # 处理跨天时间
        for start, end, low, high in impedance_ranges:
            if start <= time < end:
                return low, high
        # 默认返回最后一个区间（处理边界情况）
        return impedance_ranges[-1][2], impedance_ranges[-1][3]

    def calculate_robust_cost(self, path, start_time):
        """优化后的鲁棒成本计算"""
        key = (tuple(path), start_time)
        if key in self.robust_cost_cache:
            return self.robust_cost_cache[key]

        # 使用双向Dijkstra优化备选路径计算
        a = 0
        current_time = start_time
        worst_case = []

        # 预计算最坏情况耗时
        for i in range(len(path) - 1):
            u, v = path[i], path[i + 1]
            for neighbor, ranges in self.graph[u]:
                if neighbor == v:
                    low, high = self.get_impedance(ranges, current_time)
                    a += high
                    worst_case.append((u, v, high))
                    current_time = (current_time + high) % 1440
                    break

        # 使用A*算法快速寻找最佳备选路径
        def heuristic(node):
            return 0  # 可替换为实际启发函数

        # 正向搜索
        heap = [(0 + heuristic(path[0]), 0, path[0], start_time, set())]
        best_time = defaultdict(lambda: float('inf'))
        best_time[path[0]] = 0

        while heap:
            est, cost, node, time, visited = heapq.heappop(heap)
            if node == path[-1]:
                b = cost
                break

            if cost > best_time[node]:
                continue

            for neighbor, ranges in self.graph[node]:
                if (node, neighbor) in [(u, v) for u, v, _ in worst_case]:
                    continue  # 跳过原路径边

                low, high = self.get_impedance(ranges, time)
                new_cost = cost + low
                new_time = (time + low) % 1440

                if new_cost < best_time.get(neighbor, float('inf')):
                    best_time[neighbor] = new_cost
                    heapq.heappush(heap, (new_cost + heuristic(neighbor), new_cost,
                                          neighbor, new_time, visited | {node}))

        robust_cost = a - b if 'b' in locals() else float('inf')
        result = (robust_cost, a, b, [])
        self.robust_cost_cache[key] = result
        return result

    def find_min_robust_path(self, start, end, start_time):
        """改进的路径搜索算法"""

        # 使用A*算法结合鲁棒成本启发式
        def heuristic(node):
            return 0  # 可替换为实际鲁棒成本估计

        heap = [(0, 0, [start], start_time)]
        best_robust = float('inf')
        result = (None, float('inf'), 0, 0, None)

        while heap:
            est, current_robust, path, time = heapq.heappop(heap)

            if path[-1] == end:
                if current_robust < best_robust:
                    best_robust = current_robust
                    result = (path, current_robust, 0, 0, [])  # 实际值需计算
                continue

            current_node = path[-1]
            for neighbor, ranges in self.graph[current_node]:
                if neighbor in path:
                    continue

                # 预测鲁棒成本
                temp_path = path + [neighbor]
                _, a, b, _ = self.calculate_robust_cost(temp_path, start_time)
                pred_robust = a - b if b != 0 else float('inf')

                if pred_robust < best_robust:
                    heapq.heappush(heap, (pred_robust, pred_robust,
                                          temp_path, time))

        return result


# ======================= 主程序模块 ======================
if __name__ == '__main__':
    # 数据完整性检查
    assert len(edges) == len(l_base) == len(u_base), "基础数据长度不匹配"

    # 正确的时间覆盖验证
    total = sum(end - start for start, end in TIME_PERIODS.values())
    assert total == 1440, f"时间区间覆盖不完整，当前总时长：{total}分钟"
    print(f"时间覆盖验证通过，总时长：{total}分钟")


    # 交互式查询功能
    def time_to_minutes(t_str):
        """将时间字符串转换为分钟数"""
        try:
            hour, minute = map(int, t_str.split(':'))
            if 0 <= hour < 24 and 0 <= minute < 60:
                return hour * 60 + minute
            return None
        except:
            return None


    road_net = EnhancedRoadNetwork(
        edges, l_base, u_base, congestion_factors, road_levels
    )

    while True:
        print("\n=== 路径规划查询 ===")
        start_node = input("请输入起点编号（1-20，q退出）: ").strip()
        if start_node.lower() == 'q':
            break

        end_node = input("请输入终点编号（1-20）: ").strip()
        time_str = input("请输入出发时间（格式HH:MM，如07:30）: ").strip()

        # 输入验证
        if not (start_node.isdigit() and end_node.isdigit()):
            print("错误：节点编号必须为数字")
            continue

        depart_time = time_to_minutes(time_str)
        if depart_time is None:
            print("错误：时间格式无效")
            continue

        # 执行路径规划
        try:
            path, cost, worst, best, alt = road_net.find_min_robust_path(
                start_node, end_node, depart_time)

            print("\n=== 规划结果 ===")
            print(f"出发时间: {time_str}")
            print(f"路径: {' → '.join(path)}")
            print(f"鲁棒成本: {cost:.2f} 分钟")
            print(f"最坏情况耗时: {worst:.2f} 分钟")
            print(f"备选路径最佳耗时: {best:.2f} 分钟")
            print(f"备选路径: {' → '.join(alt) if alt else '无可用备选'}")

        except Exception as e:
            print(f"路径规划失败: {str(e)}")


def performance_test():
    road_net = EnhancedRoadNetwork(edges, l_base, u_base, congestion_factors, road_levels)
    start_time = time.time()

    # 测试10组随机查询
    for _ in range(10):
        start = str(np.random.randint(1, 21))
        end = str(np.random.randint(1, 21))
        depart = np.random.randint(0, 1440)

        road_net.find_min_robust_path(start, end, depart)

    print(f"平均查询时间: {(time.time() - start_time) / 10:.2f}秒")


performance_test()