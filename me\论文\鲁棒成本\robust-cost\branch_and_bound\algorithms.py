# 算法模块
import numpy as np
import heapq


# 计算鲁棒成本
def compute_robust_cost(path, edges, l, u):
    max_cost = 0  # 初始化当前路径的最大成本

    # 累加当前路径的上界阻抗
    for i in range(len(path) - 1):
        # 查找路径中相邻节点之间的边的索引
        edge_index = find_edge(path[i], path[i + 1], edges)
        if edge_index is not None:  # 如果找到了该边
            max_cost += u[edge_index]  # 将该边的上界阻抗累加到最大成本中

    # 找到未经过的路段
    visited_edges = set()  # 用于存储已经访问的边的索引
    for i in range(len(path) - 1):
        # 将当前路径中所有边的索引添加到已访问的集合中
        visited_edges.add(find_edge(path[i], path[i + 1], edges))

    # 创建未经过的边的列表
    # unused_edges 将包含所有未在当前路径中使用的边的索引
    unused_edges = [i for i in range(len(edges)) if i not in visited_edges]

    # 使用 Dijkstra 算法找出未经过的路段组成的最小下界阻抗路径
    min_cost = dijkstra_min_cost(unused_edges, edges, l)

    # 返回当前路径的鲁棒成本
    # 鲁棒成本的计算为：当前路径的上界阻抗总和 - 未经过边的最小下界阻抗路径成本
    return max_cost - min_cost  # 返回鲁棒成本

# dijkstra算法找情景最短路径
def dijkstra_min_cost(unused_edges, edges, l, start_node=1, end_node=26):
    # 创建图的邻接列表
    graph = {}  # 使用字典来存储图的邻接关系
    for edge in unused_edges:
        # 获取未使用边的两个节点
        node1, node2 = edges[edge]
        # 如果节点1不在图中，初始化一个空列表
        if node1 not in graph:
            graph[node1] = []
        # 如果节点2不在图中，初始化一个空列表
        if node2 not in graph:
            graph[node2] = []
        # 将相邻节点和对应的下界阻抗添加到图中（双向边）
        graph[node1].append((node2, l[edge]))  # (相邻节点, 下界阻抗)
        graph[node2].append((node1, l[edge]))  # 双向

    # Dijkstra 算法初始化
    min_heap = [(0, start_node)]  # 使用堆来保存待处理的节点，初始时只有起始节点，成本为0
    min_cost = {node: float('inf') for node in graph}  # 初始化所有节点的最小成本为无穷大
    min_cost[start_node] = 0  # 起始节点的成本为0

    while min_heap:
        # 从堆中取出当前成本最小的节点
        current_cost, current_node = heapq.heappop(min_heap)

        # 如果到达终点，返回当前成本
        if current_node == end_node:
            return current_cost  # 返回从起始节点到终点的最小下界阻抗

        # 遍历当前节点的所有相邻节点
        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost  # 计算通过当前节点到达邻居的总成本
            # 如果新计算的成本小于已知的最小成本，则更新
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost  # 更新最小成本
                # 将新成本和邻居节点添加到堆中
                heapq.heappush(min_heap, (new_cost, neighbor))

    # 如果无法到达终点，返回无限大
    return float('inf')  # 表示从起始节点到终点没有可达路径

# 获取与指定节点相邻的所有节点，用于查找可以从当前节点移动到哪些节点
def get_neighbors(node, edges):
    return np.unique(edges[edges[:, 0] == node][:, 1])

# 查找图中连接两个节点的边的索引，在计算路径的鲁棒成本时，需要找到路径上每条边的索引，以便获取对应的下界和上界阻抗
def find_edge(node1, node2, edges):
    index = np.where((edges[:, 0] == node1) & (edges[:, 1] == node2))
    return index[0][0] if index[0].size > 0 else None


# 分支定界法
def branch_and_bound(nodes, edges, l, u, start, end):
    optimal_path = []  # 初始化最优路径为空列表
    R_ort = float('inf')  # 初始化鲁棒成本为正无穷大

    def search(current_node, current_path):
        nonlocal optimal_path, R_ort  # 引入外部变量以便修改

        # 检查是否到达终点
        if current_node == end:
            # 计算当前路径的鲁棒成本
            R_j = compute_robust_cost(current_path, edges, l, u)
            # 如果当前鲁棒成本小于已知的最优鲁棒成本
            if R_j < R_ort:
                R_ort = R_j  # 更新最优鲁棒成本
                optimal_path = current_path  # 更新最优路径
            return  # 返回，结束当前路径的搜索

        # 遍历相邻节点
        neighbors = get_neighbors(current_node, edges)  # 获取当前节点的相邻节点
        for next_node in neighbors:
            # 计算包括下一个节点的路径的鲁棒成本
            R_ri = compute_robust_cost(current_path + [next_node], edges, l, u)
            # 如果新路径的鲁棒成本小于或等于当前最优鲁棒成本
            if R_ri <= R_ort:
                search(next_node, current_path + [next_node])  # 继续搜索下一个节点

    # 启动搜索，从起点开始
    search(start, [start])
    return optimal_path, R_ort  # 返回找到的最优路径及其鲁棒成本
# 上面四个def都是为了branch_and_bound服务的