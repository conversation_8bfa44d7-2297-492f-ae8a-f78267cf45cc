import math
import heapq

# 路网坐标
coordinates = {
    1: (0.46035, 5.0452), 2: (0.73589, 7.177), 3: (1.4617, 6.0917), 4: (1.6095, 4.4897), 5: (1.0316, 3.1072),
    6: (1.5827, 8.4173), 7: (2.2749, 7.1641), 8: (2.7251, 5.3165), 9: (2.6781, 3.624), 10: (2.127, 1.7765),
    11: (2.7722, 9.1279), 12: (3.4778, 7.7842), 13: (3.8071, 6.2726), 14: (3.8542, 4.2313), 15: (3.6929, 2.5517),
    16: (3.2359, 0.67829), 17: (4.5128, 9.0504), 18: (5.0302, 7.5), 19: (5.2587, 5.3036), 20: (5.2386, 3.2235),
    21: (4.8353, 1.1305), 22: (6.0921, 8.6886), 23: (6.4751, 6.7119), 24: (6.5558, 4.593), 25: (6.334, 2.0995),
    26: (7.5034, 8.1202), 27: (7.8058, 5.8075), 28: (7.584, 3.1589), 29: (8.9751, 5.7041)
}

# 每个时段的速度（km/h）
speeds = [5,6,7,8,9,6,7,2,1,6,8,7,6,9,11,8,16,3,2,10,5,7,8,6]

# 路网
roads = [
    (1, 2), (1, 3), (2, 3), (1, 4), (3, 4), (1, 5), (4, 5), (2, 6), (3, 6), (3, 7), (6, 7),
    (3, 8), (4, 8), (7, 8), (4, 9), (8, 9), (4, 10), (5, 10), (9, 10), (6, 11), (7, 11),
    (7, 12), (11, 12), (7, 13), (8, 13), (12, 13), (8, 14), (9, 14), (13, 14), (9, 15),
    (10, 15), (14, 15), (10, 16), (15, 16), (11, 17), (12, 17), (12, 18), (13, 18), (17, 18),
    (13, 19), (14, 19), (18, 19), (14, 20), (15, 20), (19, 20), (15, 21), (16, 21), (20, 21),
    (17, 22), (18, 22), (18, 23), (19, 23), (22, 23), (19, 24), (20, 24), (23, 24), (20, 25),
    (21, 25), (24, 25), (22, 26), (23, 26), (23, 27), (24, 27), (26, 27), (24, 28), (25, 28),
    (27, 28), (26, 29), (27, 29), (28, 29), (2, 1), (3, 1), (3, 2), (4, 1), (4, 3), (5, 1),
    (5, 4), (6, 2), (6, 3), (7, 3), (7, 6), (8, 3), (8, 4), (8, 7), (9, 4), (9, 8), (10, 4),
    (10, 5), (10, 9), (11, 6), (11, 7), (12, 7), (12, 11), (13, 7), (13, 8), (13, 12), (14, 8),
    (14, 9), (14, 13), (15, 9), (15, 10), (15, 14), (16, 10), (16, 15), (17, 11), (17, 12),
    (18, 12), (18, 13), (18, 17), (19, 13), (19, 14), (19, 18), (20, 14), (20, 15), (20, 19),
    (21, 15), (21, 16), (21, 20), (22, 17), (22, 18), (23, 18), (23, 19), (23, 22), (24, 19),
    (24, 20), (24, 23), (25, 20), (25, 21), (25, 24), (26, 22), (26, 23), (27, 23), (27, 24),
    (27, 26), (28, 24), (28, 25), (28, 27), (29, 26), (29, 27), (29, 28)
]


# 计算欧几里得距离
def euclidean_distance(coord1, coord2):
    x1, y1 = coord1
    x2, y2 = coord2
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)


# 构建图
graph = {}
for road in roads:
    start, end = road
    distance = euclidean_distance(coordinates[start], coordinates[end])
    if start not in graph:
        graph[start] = {}
    graph[start][end] = distance


# 计算通行时间
def calculate_travel_time(distance, start_time):
    total_time = 0
    remaining_distance = distance
    current_time = start_time
    while remaining_distance > 0:
        interval = int(current_time // 60) % 24
        speed = speeds[interval]
        time_available = 60 - (current_time % 60)
        distance_covered = speed * (time_available / 60)
        if distance_covered >= remaining_distance:
            total_time += remaining_distance / speed * 60
            break
        else:
            total_time += time_available
            remaining_distance -= distance_covered
            current_time += time_available
    return total_time


# Dijkstra算法变种
def dijkstra(graph, start, end, start_time):
    priority_queue = [(0, start, [start], 0, 0)]
    visited = set()
    while priority_queue:
        cost, current_node, path, total_distance, total_time = heapq.heappop(priority_queue)
        if current_node in visited:
            continue
        visited.add(current_node)
        if current_node == end:
            return path, total_distance, total_time
        for neighbor, distance in graph[current_node].items():
            travel_time = calculate_travel_time(distance, start_time + total_time)
            new_total_distance = total_distance + distance
            new_total_time = total_time + travel_time
            new_cost = 3 * new_total_distance + 5 * (new_total_time / 60)
            new_path = path + [neighbor]
            heapq.heappush(priority_queue, (new_cost, neighbor, new_path, new_total_distance, new_total_time))
    return None


# 起点和终点
start_node = 1
end_node = 29
start_time = 420

# 寻找路径
result = dijkstra(graph, start_node, end_node, start_time)
if result:
    path, total_distance, total_time = result
    print(f"最优路径: {path}")
    print(f"总距离: {total_distance:.2f} km")
    print(f"总时间: {total_time / 60:.2f} 小时")
else:
    print("未找到路径")
