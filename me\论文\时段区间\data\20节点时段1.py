
{
  "1": {
    "2": [[0, 480, 3, 7]],
    "3": [[0, 480, 5, 9]],
    "4": [[0, 480, 2, 8]],
    "5": [[0, 480, 4, 10]]
  },
  "2": {
    "1": [[0, 480, 3, 7]],
    "3": [[0, 480, 3, 6]],
    "6": [[0, 480, 6, 9]],
    "7": [[0, 480, 5, 10]]
  },
  "3": {
    "1": [[0, 480, 5, 9]],
    "2": [[0, 480, 3, 6]],
    "4": [[0, 480, 5, 10]],
    "7": [[0, 480, 6, 9]],
    "8": [[0, 480, 3, 8]]
  },
  "4": {
    "1": [[0, 480, 2, 8]],
    "3": [[0, 480, 5, 10]],
    "5": [[0, 480, 3, 9]],
    "8": [[0, 480, 6, 10]],
    "9": [[0, 480, 4, 7]]
  },
  "5": {
    "1": [[0, 480, 4, 10]],
    "4": [[0, 480, 3, 9]],
    "9": [[0, 480, 6, 10]],
    "10": [[0, 480, 3, 7]]
  },
  "6": {
    "2": [[0, 480, 6, 9]],
    "7": [[0, 480, 5, 9]],
    "11": [[0, 480, 2, 6]]
  },
  "7": {
    "2": [[0, 480, 5, 10]],
    "3": [[0, 480, 6, 9]],
    "6": [[0, 480, 5, 9]],
    "8": [[0, 480, 5, 9]],
    "11": [[0, 480, 4, 8]],
    "12": [[0, 480, 3, 6]]
  },
  "8": {
    "3": [[0, 480, 3, 8]],
    "4": [[0, 480, 6, 10]],
    "7": [[0, 480, 5, 9]],
    "9": [[0, 480, 4, 8]],
    "12": [[0, 480, 5, 9]],
    "13": [[0, 480, 2, 6]]
  },
  "9": {
    "4": [[0, 480, 4, 7]],
    "5": [[0, 480, 6, 10]],
    "8": [[0, 480, 4, 8]],
    "10": [[0, 480, 6, 9]],
    "13": [[0, 480, 4, 8]],
    "14": [[0, 480, 3, 7]]
  },
  "10": {
    "5": [[0, 480, 3, 7]],
    "9": [[0, 480, 6, 9]],
    "14": [[0, 480, 6, 10]],
    "15": [[0, 480, 3, 7]]
  },
  "11": {
    "6": [[0, 480, 2, 6]],
    "7": [[0, 480, 4, 8]],
    "12": [[0, 480, 5, 9]],
    "16": [[0, 480, 3, 7]],
    "17": [[0, 480, 6, 10]]
  },
  "12": {
    "7": [[0, 480, 3, 6]],
    "8": [[0, 480, 5, 9]],
    "11": [[0, 480, 5, 9]],
    "13": [[0, 480, 6, 10]],
    "17": [[0, 480, 3, 7]],
    "18": [[0, 480, 2, 6]]
  },
  "13": {
    "8": [[0, 480, 2, 6]],
    "9": [[0, 480, 4, 8]],
    "12": [[0, 480, 6, 10]],
    "14": [[0, 480, 6, 10]],
    "18": [[0, 480, 3, 7]],
    "19": [[0, 480, 5, 9]]
  },
  "14": {
    "9": [[0, 480, 3, 7]],
    "10": [[0, 480, 6, 10]],
    "13": [[0, 480, 6, 10]],
    "15": [[0, 480, 6, 10]],
    "19": [[0, 480, 3, 7]]
  },
  "15": {
    "10": [[0, 480, 3, 7]],
    "14": [[0, 480, 6, 10]],
    "19": [[0, 480, 4, 8]]
  },
  "16": {
    "11": [[0, 480, 3, 7]],
    "17": [[0, 480, 5, 10]],
    "20": [[0, 480, 2, 6]]
  },
  "17": {
    "11": [[0, 480, 6, 10]],
    "12": [[0, 480, 3, 7]],
    "16": [[0, 480, 5, 10]],
    "18": [[0, 480, 5, 10]],
    "20": [[0, 480, 3, 7]]
  },
  "18": {
    "12": [[0, 480, 2, 6]],
    "13": [[0, 480, 3, 7]],
    "17": [[0, 480, 5, 10]],
    "19": [[0, 480, 6, 10]],
    "20": [[0, 480, 3, 7]]
  },
  "19": {
    "13": [[0, 480, 5, 9]],
    "14": [[0, 480, 3, 7]],
    "15": [[0, 480, 4, 8]],
    "18": [[0, 480, 6, 10]],
    "20": [[0, 480, 3, 7]]
  },
  "20": {
    "16": [[0, 480, 2, 6]],
    "17": [[0, 480, 3, 7]],
    "18": [[0, 480, 3, 7]],
    "19": [[0, 480, 3, 7]]
  }
}

"""
(1,2,3,7),(2,1,3,7),(1,3,5,9),(3,1,5,9),(1,4,2,8),(4,1,2,8),(1,5,4,10),(5,1,4,10),
(2,3,3,6),(3,2,3,6),(2,6,6,9),(6,2,6,9),(2,7,5,10),(7,2,5,10),(3,4,5,10),(4,3,5,10),
(3,7,6,9),(7,3,6,9),(3,8,3,8),(8,3,3,8),(4,5,3,9),(5,4,3,9),(4,8,6,10),(8,4,6,10),
(4,9,4,7),(9,4,4,7),(5,9,6,10),(9,5,6,10),(5,10,3,7),(10,5,3,7),(6,7,5,9),(7,6,5,9),
(6,11,2,6),(11,6,2,6),(7,8,5,9),(8,7,5,9),(7,11,4,8),(11,7,4,8),(7,12,3,6),(12,7,3,6),
(8,9,4,8),(9,8,4,8),(8,12,5,9),(12,8,5,9),(8,13,2,6),(13,8,2,6),(9,10,6,9),(10,9,6,9),
(9,13,4,8),(13,9,4,8),(9,14,3,7),(14,9,3,7),(10,14,6,10),(14,10,6,10),(10,15,3,7),(15,10,3,7),
(11,12,5,9),(12,11,5,9),(11,16,3,7),(16,11,3,7),(11,17,6,10),(17,11,6,10),(12,13,6,10),(13,12,6,10),
(12,17,3,7),(17,12,3,7),(12,18,2,6),(18,12,2,6),(13,14,6,10),(14,13,6,10),(13,18,3,7),(18,13,3,7),
(13,19,5,9),(19,13,5,9),(14,15,6,10),(15,14,6,10),(14,19,3,7),(19,14,3,7),(15,19,4,8),(19,15,4,8),
(16,17,5,10),(17,16,5,10),(16,20,2,6),(20,16,2,6),(17,20,3,7),(20,17,3,7),(18,19,6,10),(19,18,6,10),
(18,20,3,7),(20,18,3,7),(19,20,3,7),(20,19,3,7)

下界最短路径: [1, 4, 9, 14, 19, 20]，路径的下界阻抗总和: 15
上界最短路径: [1, 2, 6, 11, 16, 20]，路径的上界阻抗总和: 35
鲁棒成本最短路: [1, 4, 9, 14, 19, 20]，上界阻抗: 36     情景最短路：[1, 3 ，8， 13， 18， 20]，下界阻抗: 16    鲁棒成本: 20
"""

{
  "1": {
    "2": [[300, 420, 2, 4]],
    "3": [[300, 420, 1, 5]],
    "4": [[300, 420, 3, 5]],
    "5": [[300, 420, 2, 4]]
  },
  "2": {
    "1": [[300, 420, 2, 4]],
    "3": [[300, 420, 2, 5]],
    "6": [[300, 420, 4, 5]],
    "7": [[300, 420, 3, 5]]
  },
  "3": {
    "1": [[300, 420, 1, 5]],
    "2": [[300, 420, 2, 5]],
    "4": [[300, 420, 3, 5]],
    "7": [[300, 420, 4, 5]],
    "8": [[300, 420, 2, 4]]
  },
  "4": {
    "1": [[300, 420, 3, 5]],
    "3": [[300, 420, 3, 5]],
    "5": [[300, 420, 3, 5]],
    "8": [[300, 420, 1, 4]],
    "9": [[300, 420, 2, 5]]
  },
  "5": {
    "1": [[300, 420, 2, 4]],
    "4": [[300, 420, 3, 5]],
    "9": [[300, 420, 1, 5]],
    "10": [[300, 420, 2, 4]]
  },
  "6": {
    "2": [[300, 420, 4, 5]],
    "7": [[300, 420, 3, 5]],
    "11": [[300, 420, 1, 3]]
  },
  "7": {
    "2": [[300, 420, 3, 5]],
    "3": [[300, 420, 4, 5]],
    "6": [[300, 420, 3, 5]],
    "8": [[300, 420, 1, 4]],
    "11": [[300, 420, 2, 4]],
    "12": [[300, 420, 1, 5]]
  },
  "8": {
    "3": [[300, 420, 2, 4]],
    "4": [[300, 420, 1, 4]],
    "7": [[300, 420, 1, 4]],
    "9": [[300, 420, 2, 5]],
    "12": [[300, 420, 3, 5]],
    "13": [[300, 420, 1, 3]]
  },
  "9": {
    "4": [[300, 420, 2, 5]],
    "5": [[300, 420, 1, 5]],
    "8": [[300, 420, 2, 5]],
    "10": [[300, 420, 2, 4]],
    "13": [[300, 420, 3, 5]],
    "14": [[300, 420, 1, 3]]
  },
  "10": {
    "5": [[300, 420, 2, 4]],
    "9": [[300, 420, 2, 4]],
    "14": [[300, 420, 1, 4]],
    "15": [[300, 420, 2, 5]]
  },
  "11": {
    "6": [[300, 420, 1, 3]],
    "7": [[300, 420, 2, 4]],
    "12": [[300, 420, 3, 5]],
    "16": [[300, 420, 1, 4]],
    "17": [[300, 420, 2, 5]]
  },
  "12": {
    "7": [[300, 420, 1, 5]],
    "8": [[300, 420, 3, 5]],
    "11": [[300, 420, 3, 5]],
    "13": [[300, 420, 3, 5]],
    "17": [[300, 420, 1, 3]],
    "18": [[300, 420, 2, 4]]
  },
  "13": {
    "8": [[300, 420, 1, 3]],
    "9": [[300, 420, 3, 5]],
    "12": [[300, 420, 3, 5]],
    "14": [[300, 420, 1, 4]],
    "18": [[300, 420, 2, 4]],
    "19": [[300, 420, 3, 5]]
  },
  "14": {
    "9": [[300, 420, 1, 3]],
    "10": [[300, 420, 1, 4]],
    "13": [[300, 420, 1, 4]],
    "15": [[300, 420, 2, 5]],
    "19": [[300, 420, 3, 5]]
  },
  "15": {
    "10": [[300, 420, 2, 5]],
    "14": [[300, 420, 2, 5]],
    "19": [[300, 420, 3, 5]]
  },
  "16": {
    "11": [[300, 420, 1, 4]],
    "17": [[300, 420, 3, 5]],
    "20": [[300, 420, 1, 3]]
  },
  "17": {
    "11": [[300, 420, 2, 5]],
    "12": [[300, 420, 1, 3]],
    "16": [[300, 420, 3, 5]],
    "18": [[300, 420, 2, 4]],
    "20": [[300, 420, 1, 4]]
  },
  "18": {
    "12": [[300, 420, 2, 4]],
    "13": [[300, 420, 2, 4]],
    "17": [[300, 420, 2, 4]],
    "19": [[300, 420, 2, 5]],
    "20": [[300, 420, 2, 5]]
  },
  "19": {
    "13": [[300, 420, 3, 5]],
    "14": [[300, 420, 3, 5]],
    "15": [[300, 420, 3, 5]],
    "18": [[300, 420, 2, 5]],
    "20": [[300, 420, 1, 5]]
  },
  "20": {
    "16": [[300, 420, 1, 3]],
    "17": [[300, 420, 1, 4]],
    "18": [[300, 420, 2, 5]],
    "19": [[300, 420, 1, 5]]
  }
}

"""
(1,2,2,4),(2,1,2,4),(1,3,1,5),(3,1,1,5),(1,4,3,5),(4,1,3,5),(1,5,2,4),(5,1,2,4),
(2,3,2,5),(3,2,2,5),(2,6,4,5),(6,2,4,5),(2,7,3,5),(7,2,3,5),(3,4,3,5),(4,3,3,5),
(3,7,4,5),(7,3,4,5),(3,8,2,4),(8,3,2,4),(4,5,3,5),(5,4,3,5),(4,8,1,4),(8,4,1,4),
(4,9,2,5),(9,4,2,5),(5,9,1,5),(9,5,1,5),(5,10,2,4),(10,5,2,4),(6,7,3,5),(7,6,3,5),
(6,11,1,3),(11,6,1,3),(7,8,1,4),(8,7,1,4),(7,11,2,4),(11,7,2,4),(7,12,1,5),(12,7,1,5),
(8,9,2,5),(9,8,2,5),(8,12,3,5),(12,8,3,5),(8,13,1,3),(13,8,1,3),(9,10,2,4),(10,9,2,4),
(9,13,3,5),(13,9,3,5),(9,14,1,3),(14,9,1,3),(10,14,1,4),(14,10,1,4),(10,15,2,5),(15,10,2,5),
(11,12,3,5),(12,11,3,5),(11,16,1,4),(16,11,1,4),(11,17,2,5),(17,11,2,5),(12,13,3,5),(13,12,3,5),
(12,17,1,3),(17,12,1,3),(12,18,2,4),(18,12,2,4),(13,14,1,4),(14,13,1,4),(13,18,2,4),(18,13,2,4),
(13,19,3,5),(19,13,3,5),(14,15,2,5),(15,14,2,5),(14,19,3,5),(19,14,3,5),(15,19,3,5),(19,15,3,5),
(16,17,3,5),(17,16,3,5),(16,20,1,3),(20,16,1,3),(17,18,2,4),(18,17,2,4),(17,20,1,4),(20,17,1,4),
(18,19,2,5),(19,18,2,5),(18,20,2,5),(20,18,2,5),(19,20,1,5),(20,19,1,5)
2,2,1,1,3,3,2,2,2,2,
4,4,3,3,3,3,4,4,2,2,
3,3,1,1,2,2,1,1,2,2,
3,3,1,1,1,1,2,2,1,1,
2,2,3,3,3,3,4,4,3,3,
1,1,1,1,2,2,3,3,1,1,
2,2,3,3,1,1,2,2,1,1,
2,2,3,3,2,2,3,3,3,3,
3,3,1,1,2,2,1,1,2,2,
2,2,1,1

4,4,5,5,5,5,4,4,5,5,
5,5,5,5,5,5,5,5,4,4,
5,5,4,4,5,5,5,5,4,4,
5,5,3,3,4,4,4,4,5,5,
5,5,5,5,3,3,4,4,5,5,
3,3,4,4,5,5,5,5,4,4,
5,5,5,5,3,3,4,4,4,4,
4,4,5,5,5,5,5,5,5,5,
5,5,3,3,4,4,4,4,5,5,
5,5,5,5
下界最短路径: [1，3，8，7，12，17，20]，路径的下界阻抗总和: 7
上界最短路径: [1，2，6，11，16，20]，   路径的上界阻抗总和: 19
鲁棒成本最短路: [1，2，6，11，16，20]，上界阻抗:19     情景最短路：[1，3，8，7，12，17，20]，下界阻抗:7     鲁棒成本:12
"""