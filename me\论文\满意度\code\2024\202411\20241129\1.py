

# 失败





import numpy as np
import networkx as nx
import matplotlib.pyplot as plt
from gurobipy import Model, GRB

# 生成测试路网（简化示例，实际需根据具体情况生成）
def generate_test_network():
    G = nx.Graph()
    for i in range(1, 30):
        G.add_node(i)

    # 添加双向路段（示例，实际路段数据需根据具体路网生成）
    edges = [(1, 2), (1, 3), (2, 3), (2, 4), (3, 4), (3, 5), (4, 5), (4, 6), (5, 6),
             (5, 7), (6, 7), (6, 8), (7, 8), (7, 9), (8, 9), (8, 10), (9, 10), (9, 11),
             (10, 11), (10, 12), (11, 12), (11, 13), (12, 13), (12, 14), (13, 14), (13, 15),
             (14, 15), (14, 16), (15, 16), (15, 17), (16, 17), (16, 18), (17, 18), (17, 19),
             (18, 19), (18, 20), (19, 20), (19, 21), (20, 21), (20, 22), (21, 22), (21, 23),
             (22, 23), (22, 24), (23, 24), (23, 25), (24, 25), (24, 26), (25, 26), (25, 27),
             (26, 27), (26, 28), (27, 28), (27, 29), (28, 29)]

    for edge in edges:
        G.add_edge(edge[0], edge[1])

    return G

# 计算路段阻抗区间（示例，实际需根据具体路网数据生成）
def calculate_impedance_interval(G):
    impedance_interval = {}
    for edge in G.edges():
        impedance_interval[edge] = (np.random.randint(1, 6), np.random.randint(6, 11))
    return impedance_interval

# 定义参数
N = 29  # 节点集合
L = list(G.edges())  # 路段集合
r = 1  # 起点
t = 29  # 终点
lambda_ = 0.5  # 模型系数

# 生成测试路网和路段阻抗区间
G = generate_test_network()
impedance_interval = calculate_impedance_interval(G)
l = np.array([impedance_interval[edge][0] for edge in L])  # 路段下界阻抗数组
u = np.array([impedance_interval[edge][1] for edge in L])  # 路段上界阻抗数组

# 计算下界最短路径阻抗S_min、上界最短路径阻抗S_max（这里假设已经通过其他方法计算得出）
S_min = 23
S_max = 52

# 定义变量
p = {}
for i, j in L:
    p[i, j] = model.addVar(vtype=GRB.BINARY, name=f'p_{i}_{j}')

mu = {}
for j in N:
    mu[j] = model.addVar(lb=0, name=f'mu_{j}')

z = model.addVar(lb=-GRB.INFINITY, name='z')

# 目标函数
model.setObjective(lambda_ * (sum(u[i, j] * p[i, j] - mu[t] for i, j in L)) +
                   (1 - lambda_) * (sum((u[i, j] + l[i, j]) * p[i, j] - S_min - S_max for i, j in L)),
                   GRB.MINIMIZE)

# 约束条件
# 流量守恒约束
for j in N:
    if j == r:
        model.addConstr(sum(p[i, j] for i, j in L if (i, j) in L) - sum(p[j, k] for j, k in L if (j, k) in L) == -1)
    elif j == t:
        model.addConstr(sum(p[i, j] for i, j in L if (i, j) in L) - sum(p[j, k] for j, k in L if (j, k) in L) == 1)
    else:
        model.addConstr(sum(p[i, j] for i, j in L if (i, j) in L) - sum(p[j, k] for j, k in L if (j, k) in L) == 0)

# 最短路径约束
for i, j in L:
    model.addConstr(mu[j] <= mu[r] + l[i, j] + (u[i, j] - l[i, j]) * p[i, j])
model.addConstr(mu[r] == 0)
for j in N:
    model.addConstr(mu[j] >= 0)

# 决策变量约束
for i, j in L:
    model.addConstr(p[i, j] >= 0)
    model.addConstr(p[i, j] <= 1)

# 有效路径约束（根据论文中的定义添加）
# 这里假设已经计算出中值最短路径的鲁棒成本为R_p_mid
model.addConstr(sum(mu[i, j] * p[i, j] - (l[i, j] + (u[i, j] - l[i, j]) * p[i, j]) * w[i, j] for i, j in L) <= R_p_mid)

# 求解模型
model.optimize()

# 提取最短路径
shortest_path = []
for i, j in L:
    if p[i, j].x > 0.5:
        shortest_path.append((i, j))

# 绘制路网和最短路径
pos = nx.spring_layout(G)
nx.draw(G, pos, with_labels=True, node_size=500, font_size=12)
nx.draw_networkx_edges(G, pos, edgelist=shortest_path, edge_color='r', width=2)
plt.title('Shortest Path')
plt.show()