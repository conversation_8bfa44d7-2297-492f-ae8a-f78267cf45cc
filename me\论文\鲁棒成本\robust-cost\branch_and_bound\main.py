# 主程序
import time
from data import get_data
from algorithms import branch_and_bound

# 获取数据
nodes, edges, l, u = get_data()

# 起点和终点
start_node = 1
end_node = 26

# 记录整个程序的运行时间
start_time = time.time()

# 调用算法
optimal_path, robust_cost = branch_and_bound(nodes, edges, l, u, start_node, end_node)

end_time = time.time()



# 输出结果
print("最优路径:", [int(node) for node in optimal_path])
print("鲁棒成本:", robust_cost)
print("运行时间:", end_time - start_time, "秒")

"""
运行结果：最优路径: [1, 4, 8, 14, 19, 23, 26]
        鲁棒成本: 8.2
        运行时间: 0.2876744270324707 秒
"""