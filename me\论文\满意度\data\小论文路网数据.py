import numpy as np

# 数据定义，26个节点63条路段
nodes = np.arange(1, 27)  # 26个节点
edges = np.array([
    [1, 2], [1, 3], [1, 4], [1, 5],
    [2, 3], [2, 6], [2, 11],
    [3, 4], [3, 6], [3, 7],
    [4, 5], [4, 7], [4, 8], [4, 9],
    [5, 9], [5, 10],
    [6, 7], [6, 11], [6, 12], [6, 13],
    [7, 8], [7, 13],
    [8, 9], [8, 13], [8, 14],
    [9, 10], [9, 14], [9, 15],
    [10, 15], [10, 16],
    [11, 12],
    [12, 13], [12, 17],
    [13, 14], [13, 17], [13, 18],
    [14, 15], [14, 18], [14, 19],
    [15, 16], [15, 19], [15, 20],
    [16, 20], [16, 21],
    [17, 18], [17, 22],
    [18, 19], [18, 22], [18, 23],
    [19, 20], [19, 23], [19, 24],
    [20, 21], [20, 24],
    [21, 24], [21, 25],
    [22, 23], [22, 26],
    [23, 24], [23, 26],
    [24, 25], [24, 26],
    [25, 26],
    [2, 1], [3, 1], [4, 1], [5, 1],
    [3, 2], [6, 2], [11, 2],
    [4, 3], [6, 3], [7, 3],
    [5, 4], [7, 4], [8, 4], [9, 4],
    [9, 5], [10, 5],
    [7, 6], [11, 6], [12, 6], [13, 6],
    [8, 7], [13, 7],
    [9, 8], [13, 8], [14, 8],
    [10, 9], [14, 9], [15, 9],
    [15, 10], [16, 10],
    [12, 11],
    [13, 12], [17, 12],
    [14, 13], [17, 13], [18, 13],
    [15, 14], [18, 14], [19, 14],
    [16, 15], [19, 15], [20, 15],
    [20, 16], [21, 16],
    [18, 17], [22, 17],
    [19, 18], [22, 18], [23, 18],
    [20, 19], [23, 19], [24, 19],
    [21, 20], [24, 20],
    [24, 21], [25, 21],
    [23, 22], [26, 22],
    [24, 23], [26, 23],
    [25, 24], [26, 24],
    [26, 25]
])

# 边的下界和上界阻抗（需要与边的数量一致）
l = np.array([2, 5, 3, 2, 4, 6, 5, 3, 5, 2, 3, 4, 2, 6, 6, 6, 5, 5, 5, 7, 7, 3, 3, 3, 4, 4, 4, 4, 4, 5, 6,
              5, 4, 4, 4, 3, 3, 4, 5, 5, 5, 3, 9, 5, 2, 5, 4, 5, 6, 3, 5, 4, 7, 5, 4, 5, 4, 5, 2, 4, 5, 5, 4,
              2, 5, 3, 2, 4, 6, 5, 3, 5, 2, 3, 4, 2, 6, 6, 6, 5, 5, 5, 7, 7, 3, 3, 3, 4, 4, 4, 4, 4, 5, 6,
              5, 4, 4, 4, 3, 3, 4, 5, 5, 5, 3, 9, 5, 2, 5, 4, 5, 6, 3, 5, 4, 7, 5, 4, 5, 4, 5, 2, 4, 5, 5, 4])
u = np.array([4, 10, 8, 10, 7, 10, 8, 7, 10, 9, 8, 7, 8, 11, 11, 11, 10, 10, 10, 10, 10, 9, 9, 9, 8, 8, 8, 10,
              10, 10, 10, 8, 9, 9, 10, 11, 8, 7, 8, 11, 11, 10, 13, 15, 12, 11, 15, 9, 12, 11, 8, 9, 10, 12, 8,
              7, 8, 8, 11, 11, 14, 8, 14,
              4, 10, 8, 10, 7, 10, 8, 7, 10, 9, 8, 7, 8, 11, 11, 11, 10, 10, 10, 10, 10, 9, 9, 9, 8, 8, 8, 10,
              10, 10, 10, 8, 9, 9, 10, 11, 8, 7, 8, 11, 11, 10, 13, 15, 12, 11, 15, 9, 12, 11, 8, 9, 10, 12, 8,
              7, 8, 8, 11, 11, 14, 8, 14])

# 起点和终点
start_node = 1
end_node = 26

"""
    (1, 2, 2, 4), (1, 3, 5, 10), (1, 4, 3, 8), (1, 5, 2, 10),
    (2, 3, 4, 7), (2, 6, 6, 10),(2, 11, 5, 8),
    (3, 4, 3, 7), (3, 6, 5, 10), (3, 7, 2, 9),
    (4, 5, 3, 8), (4, 7, 4, 7),(4, 8, 2, 8), (4, 9, 6, 11),
    (5, 9, 6, 11), (5, 10, 6, 11),
    (6, 7, 5, 10), (6, 11, 5, 10),(6, 12, 5, 10), (6, 13, 7, 10),
    (7, 8, 7, 10), (7, 13, 3, 9),
    (8, 9, 3, 9), (8, 13, 3, 9),(8, 14, 4, 8),
    (9, 10, 4, 8), (9, 14, 4, 8), (9, 15, 4, 10),
    (10, 15, 4, 10), (10, 16, 5, 10),
    (11, 12, 6, 10),
    (12, 13, 5, 8), (12, 17, 4, 9),
    (13, 14, 4, 9), (13, 17, 4, 10),(13, 18, 3, 11),
    (14, 15, 3, 8), (14, 18, 4, 7), (14, 19, 5, 8),
    (15, 16, 5, 11), (15, 19, 5, 11),(15, 20, 3, 10),
    (16, 20, 9, 13), (16, 21, 5, 15),
    (17, 18, 2, 12), (17, 22, 5, 11),
    (18, 19, 4, 15),(18, 22, 5, 9),(18, 23, 6, 12),
    (19, 20, 3, 11), (19, 23, 5, 8), (19, 24, 4, 9),
    (20, 21, 7, 10),(20, 24, 5, 12),
    (21, 24, 4, 8), (21, 25, 5, 7),
    (22, 23, 4, 8), (22, 26, 5, 8),
    (23, 24, 2, 11), (23, 26, 4, 11),
    (24, 25, 5, 14), (24, 26, 5, 8),
    (25, 26, 4, 14),
    (2, 1, 2, 4), (3, 1, 5, 10), (4, 1, 3, 8), (5, 1, 2, 10),
    (3, 2, 4, 7), (6, 2, 6, 10), (11, 2, 5, 8),
    (4, 3, 3, 7), (6, 3, 5, 10), (7, 3, 2, 9),
    (5, 4, 3, 8), (7, 4, 4, 7), (8, 4, 2, 8), (9, 4, 6, 11),
    (9, 5, 6, 11), (10, 5, 6, 11),
    (7, 6, 5, 10), (11, 6, 5, 10), (12, 6, 5, 10), (13, 6, 7, 10),
    (8, 7, 7, 10), (13, 7, 3, 9),
    (9, 8, 3, 9), (13, 8, 3, 9), (14, 8, 4, 8),
    (10, 9, 4, 8), (14, 9, 4, 8), (15, 9, 4, 10),
    (15, 10, 4, 10), (16, 10, 5, 10),
    (12, 11, 6, 10),
    (13, 12, 5, 8), (17, 12, 4, 9),
    (14, 13, 4, 9), (17, 13, 4, 10), (18, 13, 3, 11),
    (15, 14, 3, 8), (18, 14, 4, 7), (19, 14, 5, 8),
    (16, 15, 5, 11), (19, 15, 5, 11), (20, 15, 3, 10),
    (20, 16, 9, 13), (21, 16, 5, 15),
    (18, 17, 2, 12), (22, 17, 5, 11),
    (19, 18, 4, 15), (22, 18, 5, 9), (23, 18, 6, 12),
    (20, 19, 3, 11), (23, 19, 5, 8), (24, 19, 4, 9),
    (21, 20, 7, 10), (24, 20, 5, 12),
    (24, 21, 4, 8), (25, 21, 5, 7),
    (23, 22, 4, 8), (26, 22, 5, 8),
    (24, 23, 2, 11), (26, 23, 4, 11),
    (25, 24, 5, 14), (26, 24, 5, 8),
    (26, 25, 4, 14)
"""

"""
(1, 2, 6), (1, 3, 15), (1, 4, 11), (1, 5, 12), 
(2, 3, 11), (2, 6, 16), (2, 11, 13), 
(3, 4, 10), (3, 6, 15), (3, 7, 11), 
(4, 5, 11), (4, 7, 11), (4, 8, 10), (4, 9, 17), 
(5, 9, 17), (5, 10, 17), 
(6, 7, 15), (6, 11, 15), (6, 12, 15), (6, 13, 17), 
(7, 8, 17), (7, 13, 12), 
(8, 9, 12), (8, 13, 12), (8, 14, 12), 
(9, 10, 12), (9, 14, 12), (9, 15, 14), 
(10, 15, 14), (10, 16, 15), 
(11, 12, 16), 
(12, 13, 13), (12, 17, 13), 
(13, 14, 13), (13, 17, 14), (13, 18, 14), 
(14, 15, 11), (14, 18, 11), (14, 19, 13), 
(15, 16, 16), (15, 19, 16), (15, 20, 13), 
(16, 20, 22), (16, 21, 20), 
(17, 18, 14), (17, 22, 16), 
(18, 19, 19), (18, 22, 14), (18, 23, 18), 
(19, 20, 14), (19, 23, 13), (19, 24, 13), 
(20, 21, 17), (20, 24, 17), 
(21, 24, 12), (21, 25, 12), 
(22, 23, 12), (22, 26, 13), 
(23, 24, 13), (23, 26, 15), 
(24, 25, 19), (24, 26, 13), 
(25, 26, 18), 
(2, 1, 6), (3, 1, 15), (4, 1, 11), (5, 1, 12), 
(3, 2, 11), (6, 2, 16), (11, 2, 13), 
(4, 3, 10), (6, 3, 15), (7, 3, 11), 
(5, 4, 11), (7, 4, 11), (8, 4, 10), (9, 4, 17), 
(9, 5, 17), (10, 5, 17), 
(7, 6, 15), (11, 6, 15), (12, 6, 15), (13, 6, 17), 
(8, 7, 17), (13, 7, 12), 
(9, 8, 12), (13, 8, 12), (14, 8, 12), 
(10, 9, 12), (14, 9, 12), (15, 9, 14), 
(15, 10, 14), (16, 10, 15), 
(12, 11, 16), 
(13, 12, 13), (17, 12, 13), 
(14, 13, 13), (17, 13, 14), (18, 13, 14), 
(15, 14, 11), (18, 14, 11), (19, 14, 13), 
(16, 15, 16), (19, 15, 16), (20, 15, 13), 
(20, 16, 22), (21, 16, 20), 
(18, 17, 14), (22, 17, 16), 
(19, 18, 19), (22, 18, 14), (23, 18, 18), 
(20, 19, 14), (23, 19, 13), (24, 19, 13), 
(21, 20, 17), (24, 20, 17), 
(24, 21, 12), (25, 21, 12), 
(23, 22, 12), (26, 22, 13), 
(24, 23, 13), (26, 23, 15), 
(25, 24, 19), (26, 24, 13), 
(26, 25, 18)
"""