"""-----------------找到的路径是闭合的---------------"""

from gurobipy import GRB
import numpy as np
import math
import random
import time

# ---------- 节点与边定义 ----------
edges = [
    (1, 8), (8, 1), (1, 20), (20, 1), (1, 29), (29, 1), (1, 45), (45, 1), (1, 50), (50, 1), (1, 51), (51, 1), (1, 53),
    (53, 1),
    (2, 22), (22, 2), (2, 23), (23, 2), (2, 33), (33, 2), (2, 48), (48, 2), (2, 49), (49, 2),
    (3, 22), (22, 3), (3, 23), (23, 3), (3, 25), (25, 3), (3, 27), (27, 3), (3, 41), (41, 3), (3, 42), (42, 3),
    (4, 5), (5, 4), (4, 30), (30, 4), (4, 36), (36, 4), (4, 37), (37, 4), (4, 38), (38, 4), (4, 39), (39, 4), (4, 40),
    (40, 4), (4, 43), (43, 4),
    (5, 37), (37, 5), (5, 38), (38, 5),
    (6, 17), (17, 6), (6, 18), (18, 6), (6, 20), (20, 6), (6, 46), (46, 6), (6, 47), (47, 6), (6, 48), (48, 6),
    (7, 17), (17, 7), (7, 20), (20, 7), (7, 53), (53, 7),
    (8, 50), (50, 8), (8, 51), (51, 8),
    (9, 31), (31, 9), (9, 32), (32, 9), (9, 47), (47, 9), (9, 48), (48, 9),
    (10, 18), (18, 10), (10, 28), (28, 10), (10, 34), (34, 10), (10, 37), (37, 10), (10, 45), (45, 10), (10, 47),
    (47, 10),
    (11, 29), (29, 11), (11, 30), (30, 11), (11, 51), (51, 11),
    (12, 19), (19, 12), (12, 31), (31, 12), (12, 33), (33, 12), (12, 49), (49, 12),
    (14, 27), (27, 14), (14, 40), (40, 14), (14, 41), (41, 14),
    (15, 21), (21, 15), (15, 22), (22, 15), (15, 26), (26, 15), (15, 42), (42, 15),
    (16, 25), (25, 16), (16, 26), (26, 16), (16, 42), (42, 16),
    (17, 48), (48, 17),
    (18, 46), (46, 18), (18, 47), (47, 18),
    (19, 31), (31, 19), (19, 32), (32, 19), (19, 33), (33, 19), (19, 34), (34, 19),
    (20, 45), (45, 20), (20, 53), (53, 20),
    (21, 22), (22, 21), (21, 49), (49, 21),
    (22, 23), (23, 22), (22, 42), (42, 22),
    (23, 24), (24, 23), (23, 35), (35, 23), (23, 41), (41, 23), (23, 48), (48, 23),
    (24, 35), (35, 24), (24, 39), (39, 24), (24, 40), (40, 24), (24, 41), (41, 24),
    (25, 27), (27, 25), (25, 42), (42, 25), (25, 52), (52, 25),
    (26, 42), (42, 26),
    (27, 41), (41, 27), (27, 52), (52, 27),
    (28, 43), (43, 28), (28, 44), (44, 28), (28, 45), (45, 28),
    (29, 44), (44, 29), (29, 45), (45, 29), (29, 51), (51, 29),
    (30, 36), (36, 30), (30, 43), (43, 30), (30, 44), (44, 30),
    (31, 32), (32, 31),
    (32, 34), (34, 32), (32, 47), (47, 32),
    (33, 34), (34, 33), (33, 48), (48, 33), (33, 49), (49, 33),
    (34, 35), (35, 34), (34, 37), (37, 34), (34, 38), (38, 34), (34, 47), (47, 34), (34, 48), (48, 34),
    (35, 38), (38, 35), (35, 48), (48, 35),
    (36, 40), (40, 36),
    (37, 43), (43, 37),
    (38, 39), (39, 38),
    (40, 41), (41, 40),
    (43, 44), (44, 43),
    (45, 46), (46, 45),
    (47, 48), (48, 47),
    (50, 53), (53, 50)
]

# ---------- 时段定义 ----------
TIME_PERIODS = {
    0: "19:00-07:00",  # 晚上到早晨
    1: "07:00-09:00",  # 早高峰
    2: "09:00-17:00",  # 日间
    3: "17:00-19:00"  # 晚高峰
}

# 每个时段的开始时间（分钟）
PERIOD_START_MINUTES = {
    0: 19 * 60,  # 19:00-07:00
    1: 7 * 60,  # 07:00-09:00
    2: 9 * 60,  # 09:00-17:00
    3: 17 * 60  # 17:00-19:00
}

# 每个时段的结束时间（分钟）
PERIOD_END_MINUTES = {
    0: 7 * 60,  # 19:00-07:00
    1: 9 * 60,  # 07:00-09:00
    2: 17 * 60,  # 09:00-17:00
    3: 19 * 60  # 17:00-19:00
}

# 默认起始时间（以分钟为单位）
DEFAULT_START_TIME = 8 * 60  # 默认从早上8点开始配送

N = sorted(set(i for e in edges for i in e))
S = [1, 2, 3]  # 起点集合
M = [i for i in range(17, 47)]
K = [0, 1, 2, 3, 4, 5]  # 车辆集合，固定6辆车

# 受灾点需求
p = {
    17: 192, 18: 197, 19: 107, 20: 183, 21: 197, 22: 232, 23: 153, 24: 217, 25: 193, 26: 216,
    27: 196, 28: 243, 29: 218, 30: 277, 31: 186, 32: 184, 33: 107, 34: 203, 35: 178, 36: 247,
    37: 194, 38: 179, 39: 183, 40: 184, 41: 206, 42: 229, 43: 246, 44: 223, 45: 220, 46: 200
}  # 受灾点的实际需求量(kg)

Q = 1500  # 车辆容量
Tmax = 100  # 最大行程时间（分钟）

# 路段行程时间区间：tl下界，tu上界（具体数据，精确到小数点后一位）
# 将原来的tl和tu字典转换为多时段的字典
# 为简化代码，这里先创建基础数据，后面再基于这些数据生成不同时段的值

base_tl = {
    (1, 8): 1.5, (8, 1): 1.7, (1, 20): 2.0, (20, 1): 1.8, (1, 29): 2.2, (29, 1): 1.9,
    (1, 45): 2.4, (45, 1): 2.0, (1, 50): 1.3, (50, 1): 1.1, (1, 51): 1.6, (51, 1): 1.7,
    (1, 53): 2.1, (53, 1): 2.3, (2, 22): 1.7, (22, 2): 1.8, (2, 23): 2.1, (23, 2): 2.2,
    (2, 33): 1.5, (33, 2): 1.6, (2, 48): 2.5, (48, 2): 2.7, (2, 49): 1.8, (49, 2): 1.7,
    (3, 22): 1.1, (22, 3): 1.2, (3, 23): 1.6, (23, 3): 1.5, (3, 25): 2.0, (25, 3): 2.2,
    (3, 27): 1.9, (27, 3): 1.8, (3, 41): 2.4, (41, 3): 2.3, (3, 42): 1.4, (42, 3): 1.5,
    (4, 5): 1.2, (5, 4): 1.3, (4, 30): 2.3, (30, 4): 2.1, (4, 36): 2.5, (36, 4): 2.6,
    (4, 37): 1.9, (37, 4): 1.7, (4, 38): 1.5, (38, 4): 1.6, (4, 39): 2.2, (39, 4): 2.3,
    (4, 40): 1.7, (40, 4): 1.8, (4, 43): 2.1, (43, 4): 2.2, (5, 37): 1.3, (37, 5): 1.4,
    (5, 38): 1.9, (38, 5): 1.8, (6, 17): 2.0, (17, 6): 2.1, (6, 18): 1.4, (18, 6): 1.5,
    (6, 20): 1.7, (20, 6): 1.6, (6, 46): 2.3, (46, 6): 2.2, (6, 47): 1.2, (47, 6): 1.3,
    (6, 48): 2.5, (48, 6): 2.7, (7, 17): 1.6, (17, 7): 1.5, (7, 20): 2.0, (20, 7): 2.1,
    (7, 53): 1.8, (53, 7): 1.7, (8, 50): 2.2, (50, 8): 2.4, (8, 51): 1.9, (51, 8): 1.8,
    (9, 31): 1.5, (31, 9): 1.4, (9, 32): 2.1, (32, 9): 2.3, (9, 47): 1.7, (47, 9): 1.8,
    (9, 48): 2.5, (48, 9): 2.6, (10, 18): 1.3, (18, 10): 1.2, (10, 28): 2.0, (28, 10): 2.1,
    (10, 34): 2.4, (34, 10): 2.3, (10, 37): 1.6, (37, 10): 1.7, (10, 45): 1.9, (45, 10): 1.8,
    (10, 47): 2.2, (47, 10): 2.3, (11, 29): 1.5, (29, 11): 1.4, (11, 30): 2.0, (30, 11): 2.2,
    (11, 51): 1.8, (51, 11): 1.9, (12, 19): 2.3, (19, 12): 2.2, (12, 31): 1.6, (31, 12): 1.5,
    (12, 33): 1.3, (33, 12): 1.4, (12, 49): 2.1, (49, 12): 2.0, (14, 27): 1.7, (27, 14): 1.8,
    (14, 40): 2.4, (40, 14): 2.3, (14, 41): 1.2, (41, 14): 1.3, (15, 21): 2.2, (21, 15): 2.0,
    (15, 22): 1.5, (22, 15): 1.6, (15, 26): 1.9, (26, 15): 1.8, (15, 42): 2.3, (42, 15): 2.4,
    (16, 25): 1.4, (25, 16): 1.3, (16, 26): 2.1, (26, 16): 2.2, (16, 42): 1.8, (42, 16): 1.7,
    (17, 48): 2.5, (48, 17): 2.6, (18, 46): 1.3, (46, 18): 1.2, (18, 47): 2.0, (47, 18): 2.1,
    (19, 31): 1.6, (31, 19): 1.5, (19, 32): 1.9, (32, 19): 1.8, (19, 33): 2.2, (33, 19): 2.3,
    (19, 34): 2.7, (34, 19): 2.6, (20, 45): 1.4, (45, 20): 1.3, (20, 53): 2.0, (53, 20): 2.1,
    (21, 22): 1.7, (22, 21): 1.8, (21, 49): 2.4, (49, 21): 2.2, (22, 23): 1.5, (23, 22): 1.6,
    (22, 42): 1.9, (42, 22): 1.7, (23, 24): 2.3, (24, 23): 2.2, (23, 35): 1.2, (35, 23): 1.3,
    (23, 41): 2.0, (41, 23): 2.1, (23, 48): 1.8, (48, 23): 1.7, (24, 35): 1.5, (35, 24): 1.6,
    (24, 39): 2.4, (39, 24): 2.3, (24, 40): 1.9, (40, 24): 1.8, (24, 41): 1.3, (41, 24): 1.4,
    (25, 27): 2.2, (27, 25): 2.0, (25, 42): 1.7, (42, 25): 1.6, (25, 52): 2.5, (52, 25): 2.3,
    (26, 42): 1.4, (42, 26): 1.3, (27, 41): 2.1, (41, 27): 2.0, (27, 52): 1.8, (52, 27): 1.9,
    (28, 43): 1.6, (43, 28): 1.5, (28, 44): 2.3, (44, 28): 2.2, (28, 45): 1.2, (45, 28): 1.3,
    (29, 44): 2.0, (44, 29): 2.1, (29, 45): 2.5, (45, 29): 2.4, (29, 51): 1.7, (51, 29): 1.8,
    (30, 36): 1.3, (36, 30): 1.4, (30, 43): 2.2, (43, 30): 2.3, (30, 44): 1.9, (44, 30): 1.8,
    (31, 32): 1.5, (32, 31): 1.6, (32, 34): 2.1, (34, 32): 2.2, (32, 47): 1.4, (47, 32): 1.3,
    (33, 34): 2.3, (34, 33): 2.4, (33, 48): 1.8, (48, 33): 1.7, (33, 49): 1.1, (49, 33): 1.2,
    (34, 35): 2.0, (35, 34): 2.1, (34, 37): 1.6, (37, 34): 1.5, (34, 38): 2.4, (38, 34): 2.3,
    (34, 47): 1.9, (47, 34): 1.8, (34, 48): 1.3, (48, 34): 1.2, (35, 38): 2.2, (38, 35): 2.0,
    (35, 48): 1.5, (48, 35): 1.7, (36, 40): 2.1, (40, 36): 2.3, (37, 43): 1.8, (43, 37): 1.6,
    (38, 39): 1.2, (39, 38): 1.4, (40, 41): 2.5, (41, 40): 2.3, (43, 44): 1.7, (44, 43): 1.9,
    (45, 46): 1.3, (46, 45): 1.1, (47, 48): 2.0, (48, 47): 2.2, (50, 53): 1.5, (53, 50): 1.6
}

base_tu = {
    (1, 8): 4.2, (8, 1): 5.1, (1, 20): 7.3, (20, 1): 6.2, (1, 29): 8.5, (29, 1): 7.2,
    (1, 45): 9.1, (45, 1): 8.4, (1, 50): 3.9, (50, 1): 4.5, (1, 51): 6.7, (51, 1): 5.8,
    (1, 53): 7.4, (53, 1): 8.1, (2, 22): 5.2, (22, 2): 4.3, (2, 23): 7.6, (23, 2): 6.5,
    (2, 33): 3.8, (33, 2): 4.7, (2, 48): 9.3, (48, 2): 8.2, (2, 49): 5.5, (49, 2): 4.6,
    (3, 22): 3.6, (22, 3): 4.8, (3, 23): 6.3, (23, 3): 7.2, (3, 25): 8.7, (25, 3): 9.4,
    (3, 27): 5.9, (27, 3): 4.4, (3, 41): 7.5, (41, 3): 6.7, (3, 42): 4.1, (42, 3): 3.7,
    (4, 5): 5.3, (5, 4): 6.4, (4, 30): 9.2, (30, 4): 8.3, (4, 36): 7.1, (36, 4): 6.9,
    (4, 37): 4.8, (37, 4): 5.6, (4, 38): 3.5, (38, 4): 4.9, (4, 39): 8.6, (39, 4): 7.7,
    (4, 40): 5.4, (40, 4): 6.1, (4, 43): 9.5, (43, 4): 8.8, (5, 37): 3.7, (37, 5): 4.2,
    (5, 38): 7.8, (38, 5): 6.6, (6, 17): 9.7, (17, 6): 8.5, (6, 18): 4.5, (18, 6): 3.9,
    (6, 20): 6.8, (20, 6): 5.7, (6, 46): 9.4, (46, 6): 8.1, (6, 47): 4.3, (47, 6): 5.2,
    (6, 48): 7.4, (48, 6): 6.3, (7, 17): 5.8, (17, 7): 4.7, (7, 20): 8.3, (20, 7): 7.2,
    (7, 53): 4.1, (53, 7): 3.6, (8, 50): 9.8, (50, 8): 8.7, (8, 51): 5.6, (51, 8): 4.4,
    (9, 31): 3.8, (31, 9): 4.9, (9, 32): 7.5, (32, 9): 6.2, (9, 47): 5.3, (47, 9): 4.6,
    (9, 48): 9.1, (48, 9): 8.4, (10, 18): 3.7, (18, 10): 4.5, (10, 28): 7.9, (28, 10): 6.8,
    (10, 34): 9.6, (34, 10): 8.3, (10, 37): 4.2, (37, 10): 5.5, (10, 45): 6.7, (45, 10): 7.4,
    (10, 47): 8.9, (47, 10): 9.2, (11, 29): 4.7, (29, 11): 3.8, (11, 30): 7.3, (30, 11): 6.5,
    (11, 51): 5.9, (51, 11): 4.8, (12, 19): 9.3, (19, 12): 8.6, (12, 31): 4.5, (31, 12): 3.7,
    (12, 33): 6.4, (33, 12): 5.3, (12, 49): 8.2, (49, 12): 7.1, (14, 27): 5.7, (27, 14): 4.9,
    (14, 40): 9.4, (40, 14): 8.5, (14, 41): 3.6, (41, 14): 4.4, (15, 21): 7.8, (21, 15): 6.6,
    (15, 22): 4.3, (22, 15): 5.1, (15, 26): 6.9, (26, 15): 5.8, (15, 42): 9.5, (42, 15): 8.7,
    (16, 25): 3.9, (25, 16): 4.8, (16, 26): 7.2, (26, 16): 6.3, (16, 42): 5.4, (42, 16): 4.5,
    (17, 48): 8.8, (48, 17): 9.6, (18, 46): 3.5, (46, 18): 4.7, (18, 47): 7.6, (47, 18): 6.9,
    (19, 31): 5.2, (31, 19): 4.3, (19, 32): 8.4, (32, 19): 7.5, (19, 33): 9.1, (33, 19): 8.2,
    (19, 34): 6.7, (34, 19): 5.8, (20, 45): 3.4, (45, 20): 4.6, (20, 53): 7.9, (53, 20): 6.8,
    (21, 22): 5.5, (22, 21): 4.2, (21, 49): 9.7, (49, 21): 8.3, (22, 23): 3.8, (23, 22): 4.9,
    (22, 42): 6.6, (42, 22): 5.7, (23, 24): 9.2, (24, 23): 8.4, (23, 35): 4.5, (35, 23): 3.6,
    (23, 41): 7.7, (41, 23): 6.8, (23, 48): 5.1, (48, 23): 4.3, (24, 35): 8.5, (35, 24): 9.3,
    (24, 39): 6.4, (39, 24): 5.5, (24, 40): 3.9, (40, 24): 4.8, (24, 41): 7.1, (41, 24): 6.2,
    (25, 27): 5.6, (27, 25): 4.7, (25, 42): 9.4, (42, 25): 8.5, (25, 52): 7.3, (52, 25): 6.4,
    (26, 42): 3.7, (42, 26): 4.9, (27, 41): 8.6, (41, 27): 7.7, (27, 52): 5.2, (52, 27): 4.3,
    (28, 43): 6.8, (43, 28): 5.9, (28, 44): 9.8, (44, 28): 8.9, (28, 45): 4.6, (45, 28): 3.5,
    (29, 44): 7.4, (44, 29): 6.5, (29, 45): 9.9, (45, 29): 8.8, (29, 51): 5.3, (51, 29): 4.4,
    (30, 36): 3.8, (36, 30): 4.7, (30, 43): 8.2, (43, 30): 7.3, (30, 44): 6.1, (44, 30): 5.2,
    (31, 32): 4.8, (32, 31): 3.9, (32, 34): 9.5, (34, 32): 8.6, (32, 47): 5.7, (47, 32): 4.8,
    (33, 34): 7.9, (34, 33): 6.8, (33, 48): 3.6, (48, 33): 4.5, (33, 49): 8.4, (49, 33): 7.5,
    (34, 35): 5.3, (35, 34): 4.2, (34, 37): 9.6, (37, 34): 8.7, (34, 38): 7.1, (38, 34): 6.3,
    (34, 47): 3.8, (47, 34): 4.9, (34, 48): 6.7, (48, 34): 5.8, (35, 38): 9.2, (38, 35): 8.3,
    (35, 48): 4.4, (48, 35): 3.5, (36, 40): 7.6, (40, 36): 6.7, (37, 43): 5.9, (43, 37): 4.8,
    (38, 39): 8.7, (39, 38): 9.5, (40, 41): 6.2, (41, 40): 5.3, (43, 44): 4.1, (44, 43): 3.6,
    (45, 46): 7.8, (46, 45): 6.9, (47, 48): 5.5, (48, 47): 4.7, (50, 53): 9.3, (53, 50): 8.4
}

# 创建不同时段的时间区间
# 为每个时段创建不同的tl和tu值
# 时段0（19:00-07:00）：夜间，道路通畅，时间短
# 时段1（07:00-09:00）：早高峰，拥堵，时间长
# 时段2（09:00-17:00）：日间，正常流量
# 时段3（17:00-19:00）：晚高峰，拥堵，时间长

# 初始化多时段的tl和tu字典
tl = {period: {} for period in TIME_PERIODS}
tu = {period: {} for period in TIME_PERIODS}

# 为每个时段生成对应的时间值
for edge in base_tl:
    # 时段0（夜间）：路况好，时间短
    tl[0][edge] = base_tl[edge] * 0.8
    tu[0][edge] = base_tu[edge] * 0.7

    # 时段1（早高峰）：拥堵严重，时间长
    tl[1][edge] = base_tl[edge] * 1.5
    tu[1][edge] = base_tu[edge] * 1.8

    # 时段2（日间）：正常流量
    tl[2][edge] = base_tl[edge]
    tu[2][edge] = base_tu[edge]

    # 时段3（晚高峰）：拥堵严重，时间长
    tl[3][edge] = base_tl[edge] * 1.4
    tu[3][edge] = base_tu[edge] * 1.6


# ---------- 构建邻接矩阵 ----------
def build_adjacency_matrix(time_period=2, time_scenario='worst'):
    """构建邻接矩阵，用于Dijkstra算法

    参数:
        time_period: 时段索引，0=19:00-07:00, 1=07:00-09:00, 2=09:00-17:00, 3=17:00-19:00
        time_scenario: 'worst'使用最坏情况(tu)，'best'使用最好情况(tl)
    """
    n = max(N) + 1  # 最大节点编号+1
    adj_matrix = np.full((n, n), float('inf'))

    # 设置邻接矩阵
    for i, j in edges:
        # 根据情景和时段选择时间
        if time_scenario == 'worst':
            adj_matrix[i, j] = tu[time_period][i, j]  # 使用特定时段的最大行程时间作为权重
        else:
            adj_matrix[i, j] = tl[time_period][i, j]  # 使用特定时段的最小行程时间作为权重

    # 对角线设为0
    for i in range(n):
        adj_matrix[i, i] = 0

    return adj_matrix


# ---------- 优化的Dijkstra算法计算最短路径 ----------
def dijkstra(adj_matrix, start):
    """优化的Dijkstra算法计算从起点到所有其他点的最短路径

    使用优先队列和稀疏图优化以提高性能
    """
    import heapq  # 导入堆队列实现优先队列

    n = len(adj_matrix)
    dist = [float('inf')] * n
    prev = [-1] * n

    # 使用优先队列优化
    dist[start] = 0
    pq = [(0, start)]  # (距离, 节点)

    # 提前建立邻接表，避免在内循环中检查所有节点
    # 仅考虑有连接的节点，大幅减少循环次数
    adj_list = [[] for _ in range(n)]
    for i in range(n):
        for j in range(n):
            if i != j and adj_matrix[i, j] != float('inf'):  # 排除自己到自己的路径
                adj_list[i].append((j, adj_matrix[i, j]))  # (目标节点, 距离)

    while pq:
        # 取出当前最短距离的节点
        curr_dist, curr_node = heapq.heappop(pq)

        # 如果已经找到更短的路径，跳过
        if curr_dist > dist[curr_node]:
            continue

        # 遍历邻接点（仅考虑有连接的节点）
        for next_node, edge_weight in adj_list[curr_node]:
            new_dist = dist[curr_node] + edge_weight

            # 如果找到更短路径，更新距离和前驱
            if new_dist < dist[next_node]:
                dist[next_node] = new_dist
                prev[next_node] = curr_node
                heapq.heappush(pq, (new_dist, next_node))

    # 构建路径，与原算法相同
    paths = {}
    for i in range(n):
        if i != start and dist[i] != float('inf'):
            path = []
            current = i
            while current != -1:
                path.append(current)
                current = prev[current]
            path.reverse()
            paths[i] = (path, dist[i])

    return paths


# ---------- 根据时间确定时段 ----------
def get_time_period(current_time_minutes):
    """根据当前时间确定所处的时段

    参数:
        current_time_minutes: 当前时间（以分钟为单位，24小时制）

    返回:
        时段索引
    """
    # 处理跨天的情况
    minutes_in_day = 24 * 60
    minutes = current_time_minutes % minutes_in_day

    # 转换为小时便于比较
    hour = minutes // 60

    if 19 <= hour or hour < 7:
        return 0  # 19:00-07:00
    elif 7 <= hour < 9:
        return 1  # 07:00-09:00
    elif 9 <= hour < 17:
        return 2  # 09:00-17:00
    else:  # 17 <= hour < 19
        return 3  # 17:00-19:00


# ---------- 检查是否在时段边界附近 ----------
def is_near_period_boundary(time_minutes, threshold_minutes=15):
    """检查时间是否接近时段边界

    参数:
        time_minutes: 当前时间（以分钟为单位）
        threshold_minutes: 边界阈值（分钟），默认15分钟

    返回:
        相邻时段的列表，如果不在边界附近则返回空列表
    """
    # 处理跨天的情况
    minutes_in_day = 24 * 60
    minutes = time_minutes % minutes_in_day

    # 获取当前小时和分钟
    hour = minutes // 60
    minute = minutes % 60

    adjacent_periods = []

    # 特殊处理接近凌晨7点的情况（如6:55）
    if hour == 6 and minute >= 45:
        # 当前在19:00-07:00时段，但接近07:00-09:00时段
        adjacent_periods.append(1)  # 添加07:00-09:00时段
        return adjacent_periods

    # 检查是否接近时段边界
    # 时段边界：7:00, 9:00, 17:00, 19:00
    boundary_hours = [7, 9, 17, 19]

    for boundary_hour in boundary_hours:
        boundary_minutes = boundary_hour * 60

        # 计算与边界的时间差（分钟）
        time_diff = abs(minutes - boundary_minutes)

        # 考虑跨天的情况
        if boundary_hour == 19 and hour < 7:
            time_diff = min(time_diff, abs(minutes + (24 * 60 - boundary_minutes)))
        elif boundary_hour == 7 and hour >= 19:
            time_diff = min(time_diff, abs(minutes - (24 * 60 + boundary_minutes)))

        # 如果在阈值内，添加相邻时段
        if time_diff <= threshold_minutes:
            # 计算边界两侧的时段
            if boundary_hour == 7:
                adjacent_periods.extend([0, 1])  # 19:00-07:00 和 07:00-09:00
            elif boundary_hour == 9:
                adjacent_periods.extend([1, 2])  # 07:00-09:00 和 09:00-17:00
            elif boundary_hour == 17:
                adjacent_periods.extend([2, 3])  # 09:00-17:00 和 17:00-19:00
            elif boundary_hour == 19:
                adjacent_periods.extend([3, 0])  # 17:00-19:00 和 19:00-07:00

    # 移除重复的时段并排序
    adjacent_periods = sorted(set(adjacent_periods))

    # 如果当前时段已经在列表中，则不需要额外添加
    current_period = get_time_period(time_minutes)
    if current_period in adjacent_periods:
        adjacent_periods.remove(current_period)

    return adjacent_periods


# ---------- 评估方案在跨时段情况下的行程时间 ----------
def evaluate_solution_cross_period(routes, depots, start_time=DEFAULT_START_TIME, time_scenario='worst'):
    """评估给定方案在考虑跨时段情况下的总行程时间

    参数:
        routes: 每辆车的路径字典
        depots: 每辆车的起点字典
        start_time: 起始时间（以分钟为单位）
        time_scenario: 'worst'使用最坏情况(tu)，'best'使用最好情况(tl)

    返回:
        总行程时间
    """
    total_time = 0
    arrival_times = {}  # 记录到达每个节点的时间

    for k in routes:
        route = routes[k]
        if not route:
            continue

        # 重建连续的路径
        full_path = [depots[k]]  # 起点
        full_path.extend(route)  # 添加路径中的所有点

        # 初始化当前时间为起始时间
        current_time = start_time
        arrival_times[k] = {depots[k]: current_time}

        # 计算相邻节点之间的时间，考虑时段变化
        for i in range(len(full_path) - 1):
            from_node = full_path[i]
            to_node = full_path[i + 1]

            # 跳过从节点到自身的连接
            if from_node == to_node:
                continue

            # 获取当前时段
            current_period = get_time_period(current_time)

            # 根据当前时段和情景选择时间
            if (from_node, to_node) in (tu[current_period] if time_scenario == 'worst' else tl[current_period]):
                edge_time = tu[current_period][(from_node, to_node)] if time_scenario == 'worst' else \
                tl[current_period][(from_node, to_node)]

                # 更新当前时间（edge_time已经是分钟）
                current_time += edge_time

                # 记录到达时间
                arrival_times[k][to_node] = current_time

                # 累加行程时间
                total_time += edge_time
            else:
                # 如果没有直接连接，可能是路径有问题
                print(f"警告: 节点{from_node}到{to_node}没有直接连接")

    return total_time, arrival_times


# ---------- 改进的鲁棒优化跨时段VRP算法 ----------
def cross_period_min_max_regret_vrp(start_time=None):
    """使用改进的鲁棒优化跨时段算法求解VRP问题

    参数:
        start_time: 指定的出发时间（分钟，24小时制），如果为None则使用每个时段的开始时间
    """
    print("使用改进的鲁棒优化跨时段算法求解VRP问题...")
    algorithm_start_time = time.time()

    # 固定分配起点，按照路网特性优化车辆分布
    depots = {
        0: 1,  # 0号车从节点1出发（覆盖东北区域）
        1: 1,  # 1号车从节点1出发（覆盖东南区域）
        2: 2,  # 2号车从节点2出发（覆盖西北区域）
        3: 2,  # 3号车从节点2出发（覆盖中部区域）
        4: 3,  # 4号车从节点3出发（覆盖西南区域）
        5: 3  # 5号车从节点3出发（覆盖中南区域）
    }

    # 减少候选方案数量以提高性能
    num_solutions = 10
    candidate_solutions = []

    # 预计算并缓存时段邻接矩阵以加速路径计算
    cached_adj_matrices = {}
    for period in range(len(TIME_PERIODS)):
        cached_adj_matrices[period] = {
            'best': build_adjacency_matrix(period, 'best'),
            'worst': build_adjacency_matrix(period, 'worst')
        }

    # 对需求点进行聚类分析，按照地理位置和需求量进行预分组
    demand_clusters = {}
    for depot in set(depots.values()):
        # 为每个起点创建两个簇
        demand_clusters[depot] = [[], []]

    if start_time is not None:
        # 如果指定了出发时间，先检查主时段
        start_period = get_time_period(start_time)
        # 检查是否在时段边界附近，获取相邻时段
        adjacent_periods = is_near_period_boundary(start_time)

        # 组合主时段和相邻时段
        start_periods = [start_period] + adjacent_periods
        initial_times = [start_time] * len(start_periods)

        # 打印时段信息
        if adjacent_periods:
            print(
                f"检测到时间 {int(start_time // 60)}:{int(start_time % 60):02d} 接近时段边界，将同时考虑时段 {[TIME_PERIODS[p] for p in adjacent_periods]}")
    else:
        # 否则，为每个时段生成不同的候选方案
        start_periods = range(len(TIME_PERIODS))
        initial_times = [PERIOD_START_MINUTES[period] for period in start_periods]

    # 为每个起始时段生成候选方案
    for idx, start_period in enumerate(start_periods):
        initial_time = initial_times[idx]
        initial_hour = initial_time // 60
        initial_minute = initial_time % 60
        print(f"\n为起始时段 {TIME_PERIODS[start_period]}（{initial_hour}:{initial_minute:02d}）生成候选方案...")

        solutions_per_period = num_solutions // (len(start_periods) if start_time is None else 1)
        for solution_idx in range(solutions_per_period):
            print(f"生成起始时段 {TIME_PERIODS[start_period]} 的第{solution_idx + 1}个候选方案...")

            # 使用预计算的邻接矩阵
            worst_adj_matrix = cached_adj_matrices[start_period]['worst']

            # 判断是否需要重新计算聚类（仅在第一个解决方案时计算）
            if solution_idx == 0 and not hasattr(cross_period_min_max_regret_vrp, 'clusters_computed'):
                # 预处理：计算每个起点到每个需求点的最短路径
                paths_from_depots = {}
                for s in S:
                    paths_from_depots[s] = dijkstra(worst_adj_matrix, s)

                    # 根据距离将需求点分配到最近的起点集群
                    for m in M:
                        if m in paths_from_depots[s]:
                            path, cost = paths_from_depots[s][m]
                            # 根据需求量和距离计算优先级分数
                            priority_score = p[m] / (cost + 1)  # 避免除以零
                            # 记录需求点、起点、分数和路径信息
                            if len(demand_clusters[s][0]) < len(M) // 6:  # 每个簇平均分配约1/6的需求点
                                demand_clusters[s][0].append((m, priority_score, cost))
                            else:
                                demand_clusters[s][1].append((m, priority_score, cost))

                # 对每个簇内的需求点按优先级排序
                for s in demand_clusters:
                    for i in range(len(demand_clusters[s])):
                        demand_clusters[s][i].sort(key=lambda x: x[1], reverse=True)  # 按优先级降序排序

                # 预计算并缓存需求点之间的最短路径，减少重复计算
                if not hasattr(cross_period_min_max_regret_vrp, 'paths_cache'):
                    cross_period_min_max_regret_vrp.paths_cache = {}
                
                cache_key = f"period_{start_period}"
                if cache_key not in cross_period_min_max_regret_vrp.paths_cache:
                    paths_between_demands = {}
                    for m in M:
                        paths_between_demands[m] = dijkstra(worst_adj_matrix, m)
                    cross_period_min_max_regret_vrp.paths_cache[cache_key] = paths_between_demands
                
                # 创建全局路径缓存
                if not hasattr(cross_period_min_max_regret_vrp, 'detailed_paths_cache'):
                    cross_period_min_max_regret_vrp.detailed_paths_cache = {}

                # 标记聚类已计算
                cross_period_min_max_regret_vrp.clusters_computed = True
            else:
                # 复用已计算的聚类结果
                paths_from_depots = {}
                for s in S:
                    paths_from_depots[s] = dijkstra(worst_adj_matrix, s)

            # 使用共享缓存优化计算需求点之间的最短路径
            if not hasattr(cross_period_min_max_regret_vrp, 'paths_cache'):
                cross_period_min_max_regret_vrp.paths_cache = {}

            # 检查是否已经为此时段计算了路径
            cache_key = f"period_{start_period}"
            if cache_key not in cross_period_min_max_regret_vrp.paths_cache:
                # 首次计算并缓存
                paths_between_demands = {}
                for m in M:
                    paths_between_demands[m] = dijkstra(worst_adj_matrix, m)
                cross_period_min_max_regret_vrp.paths_cache[cache_key] = paths_between_demands
            else:
                # 使用缓存的路径
                paths_between_demands = cross_period_min_max_regret_vrp.paths_cache[cache_key]

            # 初始化解
            unserved = set(M)  # 未服务的需求点
            routes = {k: [] for k in K}  # 每辆车的路径
            loads = {k: Q for k in K}  # 每辆车的剩余负载
            times = {k: 0 for k in K}  # 每辆车的行程时间
            served_by_vehicle = {k: set() for k in K}  # 每辆车服务的需求点
            vehicle_arrival_times = {k: {depots[k]: initial_time} for k in K}  # 车辆到达各节点的时间

            # 优化车辆初始任务分配
            # 为每辆车分配初始服务区域
            vehicle_clusters = {
                0: demand_clusters[1][0],  # 0号车负责1号起点的第一个簇
                1: demand_clusters[1][1],  # 1号车负责1号起点的第二个簇
                2: demand_clusters[2][0],  # 2号车负责2号起点的第一个簇
                3: demand_clusters[2][1],  # 3号车负责2号起点的第二个簇
                4: demand_clusters[3][0],  # 4号车负责3号起点的第一个簇
                5: demand_clusters[3][1]  # 5号车负责3号起点的第二个簇
            }

            # 准备初始需求点列表（按车辆分配）
            initial_demands = {}
            for k in K:
                if vehicle_clusters[k]:
                    # 取每个车辆对应簇中的前5个高优先级需求点
                    cluster_size = min(5, len(vehicle_clusters[k]))
                    # 修改为随机选择，而不是总是选前几个
                    if solution_idx > 0:  # 第一个方案保持原样，后续方案增加随机性
                        # 从簇中随机选择点
                        selected_indices = random.sample(range(len(vehicle_clusters[k])), cluster_size)
                        initial_demands[k] = [vehicle_clusters[k][i][0] for i in selected_indices]
                    else:
                        initial_demands[k] = [vehicle_clusters[k][i][0] for i in range(cluster_size)]
                else:
                    initial_demands[k] = []

            # 确保剩余的需求点也被考虑
            remaining_demands = list(set(M) - set([d for demands in initial_demands.values() for d in demands]))
            random.shuffle(remaining_demands)  # 打乱剩余需求点顺序，引入随机性

            # 优先为每辆车分配其预定区域内的高优先级需求点
            for k in K:
                if not initial_demands[k]:
                    continue  # 跳过没有预分配需求点的车辆

                # 尝试分配该车辆对应簇中的第一个需求点
                m = initial_demands[k][0]
                if m in unserved and p[m] <= loads[k]:  # 确保需求点未被服务且车辆有足够负载
                    path, cost = paths_from_depots[depots[k]].get(m, ([], float('inf')))
                    if path:  # 如果存在路径
                        # 模拟跨时段的行程时间，考虑时段变化对路径规划的影响
                        current_time = initial_time
                        real_cost = 0
                        node_travel_times = {}  # 记录到达每个节点的时间，用于后续优化

                        # 计算实际行程时间，精确考虑时段变化
                        for j in range(len(path) - 1):
                            from_node = path[j]
                            to_node = path[j + 1]
                            # 根据当前时刻确定时段
                            current_period = get_time_period(current_time)
                            edge_time = tu[current_period][(from_node, to_node)]

                            # 记录每一步的时间，便于后续分析
                            node_travel_times[to_node] = current_time + edge_time

                            # 累加实际成本
                            real_cost += edge_time
                            current_time += edge_time

                            # 记录到达时间
                            vehicle_arrival_times[k][to_node] = current_time

                        # 更新路径、负载和时间
                        routes[k].extend(path)
                        loads[k] -= p[m]
                        times[k] += real_cost
                        served_by_vehicle[k].add(m)
                        unserved.remove(m)
                        initial_demands[k].pop(0)  # 移除已分配的需求点

            # 处理剩余的初始预分配需求点
            for k in K:
                # 继续尝试分配该车辆的其他预分配需求点，直到负载不足或无路径
                while initial_demands[k] and loads[k] > 0:
                    m = initial_demands[k][0]
                    if m not in unserved:  # 如果需求点已被服务，跳过
                        initial_demands[k].pop(0)
                        continue

                    if p[m] <= loads[k]:  # 确保车辆有足够负载
                        # 如果车辆已有路径，从最后一个节点出发
                        if routes[k]:
                            last_node = routes[k][-1]
                            current_time = vehicle_arrival_times[k][last_node]

                            # 获取从最后访问节点到目标需求点的路径
                            path, base_cost = paths_between_demands[last_node].get(m, ([], float('inf')))

                            if path:  # 如果存在路径
                                # 计算实际行程时间
                                temp_time = current_time
                                real_cost = 0

                                for j in range(len(path) - 1):
                                    from_node = path[j]
                                    to_node = path[j + 1]
                                    current_period = get_time_period(temp_time)
                                    edge_time = tu[current_period][(from_node, to_node)]
                                    real_cost += edge_time
                                    temp_time += edge_time
                                    vehicle_arrival_times[k][to_node] = temp_time

                                # 更新路径
                                routes[k].extend(path[1:])  # 排除起点，因为已经在路径中
                                loads[k] -= p[m]
                                times[k] += real_cost
                                served_by_vehicle[k].add(m)
                                unserved.remove(m)
                                initial_demands[k].pop(0)
                            else:
                                # 无路径，跳过此需求点
                                initial_demands[k].pop(0)
                        else:
                            # 从起点出发
                            path, cost = paths_from_depots[depots[k]].get(m, ([], float('inf')))
                            if path:
                                # 计算实际行程时间
                                current_time = initial_time
                                real_cost = 0

                                for j in range(len(path) - 1):
                                    from_node = path[j]
                                    to_node = path[j + 1]
                                    current_period = get_time_period(current_time)
                                    edge_time = tu[current_period][(from_node, to_node)]
                                    real_cost += edge_time
                                    current_time += edge_time
                                    vehicle_arrival_times[k][to_node] = current_time

                                routes[k].extend(path)
                                loads[k] -= p[m]
                                times[k] += real_cost
                                served_by_vehicle[k].add(m)
                                unserved.remove(m)
                                initial_demands[k].pop(0)
                            else:
                                initial_demands[k].pop(0)
                    else:
                        # 负载不足，跳过此需求点
                        initial_demands[k].pop(0)

            # 使用改进的鲁棒优化算法分配剩余需求点
            # 先添加剩余的预分配需求点
            remaining_clusters = []
            for s in demand_clusters:
                for cluster in demand_clusters[s]:
                    for item in cluster:
                        m = item[0]
                        if m in unserved:
                            remaining_clusters.append(m)

            # 添加完全未分配的需求点
            for m in remaining_demands:
                if m in unserved and m not in remaining_clusters:
                    remaining_clusters.append(m)
                    
            # 增加随机性：随机打乱剩余需求点的顺序
            if solution_idx > 0:  # 第一个方案保持原样，后续方案增加随机性
                random.shuffle(remaining_clusters)

            # 使用改进的鲁棒优化方法处理剩余需求点
            unserved_count = len(unserved)
            progress_step = max(1, unserved_count // 5)  # 每处理20%的需求点显示一次进度
            progress_counter = 0
            
            while unserved:
                current_progress = unserved_count - len(unserved)
                if current_progress >= progress_counter * progress_step:
                    progress_percent = int(current_progress / unserved_count * 100)
                    print(f"    处理剩余需求点进度: {progress_percent}%")
                    progress_counter += 1

                best_regret = float('inf')  # 初始化为正无穷
                best_k = None
                best_m = None
                best_arrival_time = None
                best_insertion_cost = float('inf')

                # 考虑所有未服务的需求点，但优先考虑簇内点
                candidate_demands = remaining_clusters[:10] if remaining_clusters else list(unserved)
                if len(candidate_demands) > 10:
                    # 增加随机性：每次随机选择不同的候选需求点
                    if solution_idx > 0:
                        # 使用不同的随机种子生成不同的随机样本
                        random.seed(42 + solution_idx * 100 + len(unserved))
                        candidate_demands = random.sample(candidate_demands, 10)
                    else:
                        candidate_demands = candidate_demands[:10]

                # 增加随机性：随机打乱车辆顺序
                vehicle_candidates = list(K)
                if solution_idx > 0:  # 第一个方案保持原样，后续方案增加随机性
                    random.shuffle(vehicle_candidates)

                for k in vehicle_candidates:  # 使用随机顺序的车辆列表而不是固定顺序
                    # 跳过容量不足的车辆
                    if loads[k] < min([p[m] for m in candidate_demands], default=0):
                        continue

                    # 如果车辆未分配路径，使用起点作为参考点
                    if not routes[k]:
                        start_node = depots[k]
                        start_time = initial_time
                    else:
                        # 使用当前路径的最后一个节点
                        start_node = routes[k][-1]
                        start_time = vehicle_arrival_times[k][start_node]

                    # 计算每个候选需求点的插入成本和后悔值
                    for m in candidate_demands:
                        if m == start_node:  # 跳过起点自己到自己的路径查找
                            continue
                            
                        if p[m] <= loads[k]:  # 确保负载足够
                            # 计算在不同时段下的成本差异
                            best_scenario_cost = float('inf')
                            worst_scenario_cost = 0
                            insertion_costs = []
                            arrival_times_list = []

                            # 考虑当前时段和相邻时段
                            current_period = get_time_period(start_time)
                            periods_to_check = [current_period]

                            # 添加相邻时段
                            adjacent_periods = is_near_period_boundary(start_time)
                            if adjacent_periods:
                                periods_to_check.extend(adjacent_periods)

                            for period in periods_to_check:
                                # 使用缓存的邻接矩阵
                                adj_matrix = cached_adj_matrices[period]['worst']

                                # 生成缓存键
                                path_cache_key = f"{start_node}_{m}_{period}"

                                # 检查路径缓存
                                if not hasattr(cross_period_min_max_regret_vrp, 'detailed_paths_cache'):
                                    cross_period_min_max_regret_vrp.detailed_paths_cache = {}

                                if path_cache_key in cross_period_min_max_regret_vrp.detailed_paths_cache:
                                    # 使用缓存的路径结果
                                    cached_result = cross_period_min_max_regret_vrp.detailed_paths_cache[path_cache_key]
                                    path = cached_result['path']
                                    base_cost = cached_result['cost']
                                    real_cost = cached_result['real_cost']
                                    temp_time = start_time + real_cost
                                    node_arrival_times = {node: start_time + time_offset for node, time_offset in
                                                          cached_result['arrival_offsets'].items()}
                                else:
                                    # 计算新路径
                                    paths = dijkstra(adj_matrix, start_node)

                                    if m in paths:
                                        path, base_cost = paths[m]

                                        # 模拟在当前时段下的实际行程时间
                                        temp_time = start_time
                                        real_cost = 0
                                        node_arrival_times = {}
                                        arrival_offsets = {}  # 用于缓存

                                        # 计算实际行程时间（考虑时段变化，但减少get_time_period调用）
                                        last_period = get_time_period(temp_time)
                                        for j in range(len(path) - 1):
                                            from_node = path[j]
                                            to_node = path[j + 1]

                                            # 只在时间增加较多时重新计算时段
                                            if real_cost > 0 and real_cost % 30 < 5:  # 每30分钟检查一次时段变化
                                                current_period = get_time_period(temp_time)
                                                if current_period != last_period:
                                                    last_period = current_period

                                            edge_time = tu[last_period][(from_node, to_node)]
                                            real_cost += edge_time
                                            temp_time += edge_time
                                            node_arrival_times[to_node] = temp_time
                                            arrival_offsets[to_node] = real_cost  # 存储相对偏移量

                                        # 缓存计算结果
                                        cross_period_min_max_regret_vrp.detailed_paths_cache[path_cache_key] = {
                                            'path': path,
                                            'cost': base_cost,
                                            'real_cost': real_cost,
                                            'arrival_offsets': arrival_offsets
                                        }

                                    # 计算返回集散中心的时间 - 使用缓存
                                    return_period = get_time_period(temp_time)
                                    return_adj_matrix = cached_adj_matrices[return_period]['worst']

                                    # 检查返回路径缓存
                                    return_cache_key = f"{m}_{depots[k]}_{return_period}"
                                    if return_cache_key in cross_period_min_max_regret_vrp.detailed_paths_cache:
                                        return_paths = {depots[k]: cross_period_min_max_regret_vrp.detailed_paths_cache[
                                            return_cache_key]['path']}
                                    else:
                                        return_paths = dijkstra(return_adj_matrix, m)

                                    if depots[k] in return_paths:
                                        return_path, return_base_cost = return_paths[depots[k]]

                                        # 计算返回路径的实际时间
                                        return_temp_time = temp_time
                                        return_real_cost = 0

                                        for j in range(len(return_path) - 1):
                                            from_node = return_path[j]
                                            to_node = return_path[j + 1]
                                            segment_period = get_time_period(return_temp_time)
                                            edge_time = tu[segment_period][(from_node, to_node)]
                                            return_real_cost += edge_time
                                            return_temp_time += edge_time

                                        # 更新最佳和最坏情景成本
                                        total_cost = real_cost + return_real_cost
                                        insertion_costs.append(total_cost)
                                        arrival_times_list.append(node_arrival_times)

                                        if total_cost < best_scenario_cost:
                                            best_scenario_cost = total_cost
                                        if total_cost > worst_scenario_cost:
                                            worst_scenario_cost = total_cost

                            # 计算后悔值和鲁棒性指标
                            if insertion_costs:
                                # 后悔值 = 最坏情景下的成本 - 最佳情景下的成本
                                regret = worst_scenario_cost - best_scenario_cost

                                # 鲁棒性指标 = 后悔值/最佳情景成本，值越小越鲁棒
                                robustness = regret / best_scenario_cost if best_scenario_cost > 0 else float('inf')

                                # 计算总行程时间是否满足限制
                                if times[k] + min(insertion_costs) <= Tmax:
                                    # 更新最佳插入方案
                                    if robustness < best_regret:
                                        best_regret = robustness
                                        best_k = k
                                        best_m = m
                                        best_insertion_cost = min(insertion_costs)
                                        best_arrival_time = arrival_times_list[
                                            insertion_costs.index(min(insertion_costs))]

                # 如果无法找到最优插入位置
                if best_k is None:
                    # 使用改进的备选分配策略，更灵活地考虑时段和距离因素

                    # 计算每个车辆的当前负载率
                    vehicle_load_ratios = {k: (Q - loads[k]) / Q for k in K}

                    # 计算每个车辆的当前路径长度
                    vehicle_path_lengths = {k: len(routes[k]) for k in K}

                    # 计算每个未服务需求点到所有车辆当前位置的最小距离
                    demand_vehicle_distances = {}
                    for m in unserved:
                        demand_vehicle_distances[m] = {}
                        for k in K:
                            if p[m] <= loads[k]:  # 只考虑负载足够的车辆
                                if routes[k]:
                                    current_node = routes[k][-1]
                                    current_time = vehicle_arrival_times[k][current_node]
                                    current_period = get_time_period(current_time)

                                    # 构建当前时段的邻接矩阵
                                    adj_matrix = build_adjacency_matrix(current_period, 'best')
                                    paths = dijkstra(adj_matrix, current_node)

                                    if m in paths:
                                        _, cost = paths[m]
                                        demand_vehicle_distances[m][k] = cost
                                    else:
                                        demand_vehicle_distances[m][k] = float('inf')
                                else:
                                    # 从起点到需求点的距离
                                    _, cost = paths_from_depots[depots[k]].get(m, ([], float('inf')))
                                    demand_vehicle_distances[m][k] = cost

                    # 优先选择负载低、路径短且距离近的车辆-需求点对
                    best_score = float('inf')
                    best_k = None
                    best_m = None

                    for m in unserved:
                        for k in K:
                            if p[m] <= loads[k] and k in demand_vehicle_distances[m]:
                                # 计算综合评分（越小越好）
                                # 考虑：距离、负载率、路径长度
                                distance_factor = demand_vehicle_distances[m][k]
                                load_factor = vehicle_load_ratios[k]
                                path_factor = vehicle_path_lengths[k] / 10  # 归一化路径长度

                                # 综合评分
                                score = distance_factor * 0.5 + load_factor * 0.3 + path_factor * 0.2

                                if score < best_score:
                                    # 检查时间约束
                                    if routes[k]:
                                        current_node = routes[k][-1]
                                        current_time = vehicle_arrival_times[k][current_node]
                                    else:
                                        current_node = depots[k]
                                        current_time = initial_time

                                    # 计算行程时间
                                    temp_time = current_time
                                    total_cost = 0

                                    # 检查路径可行性
                                    path_found = False
                                    node_arrival_times = {}

                                    # 尝试多个时段的路径
                                    for period in range(len(TIME_PERIODS)):
                                        adj_matrix = build_adjacency_matrix(period, 'worst')
                                        paths = dijkstra(adj_matrix, current_node)

                                        if m in paths:
                                            path, _ = paths[m]

                                            # 模拟实际行程时间
                                            temp_time = current_time
                                            real_cost = 0
                                            temp_arrivals = {}

                                            for j in range(len(path) - 1):
                                                from_node = path[j]
                                                to_node = path[j + 1]
                                                segment_period = get_time_period(temp_time)
                                                edge_time = tu[segment_period][(from_node, to_node)]
                                                real_cost += edge_time
                                                temp_time += edge_time
                                                temp_arrivals[to_node] = temp_time

                                            # 检查返回集散中心的可行性
                                            return_adj_matrix = build_adjacency_matrix(get_time_period(temp_time),
                                                                                       'worst')
                                            return_paths = dijkstra(return_adj_matrix, m)

                                            if depots[k] in return_paths:
                                                return_path, _ = return_paths[depots[k]]
                                                return_cost = 0

                                                for j in range(len(return_path) - 1):
                                                    from_node = return_path[j]
                                                    to_node = return_path[j + 1]
                                                    segment_period = get_time_period(temp_time)
                                                    edge_time = tu[segment_period][(from_node, to_node)]
                                                    return_cost += edge_time
                                                    temp_time += edge_time

                                                total_cost = real_cost + return_cost

                                                # 检查时间约束
                                                if times[k] + total_cost <= Tmax:
                                                    path_found = True
                                                    node_arrival_times = temp_arrivals
                                                    break

                                    if path_found:
                                        best_score = score
                                        best_k = k
                                        best_m = m
                                        best_arrival_time = node_arrival_times

                    # 如果仍然无法分配
                    if best_k is None:
                        # 最后尝试，宽松时间约束
                        for k in K:
                            for m in list(unserved):  # 使用列表副本，避免在迭代中修改
                                if p[m] <= loads[k]:
                                    adj_matrix = build_adjacency_matrix(0, 'best')  # 使用夜间时段（速度最快）
                                    paths = dijkstra(adj_matrix, depots[k] if not routes[k] else routes[k][-1])

                                    if m in paths:
                                        best_k = k
                                        best_m = m
                                        best_arrival_time = {}  # 简化处理
                                        break
                            if best_k is not None:
                                break

                        # 如果真的无法分配，放弃该需求点
                        if best_k is None:
                            if unserved:
                                # 移除一个需求量最小的点
                                min_demand_point = min(unserved, key=lambda x: p[x])
                                unserved.remove(min_demand_point)
                                print(f"无法找到合适的路径分配需求点{min_demand_point}，已移除")
                                continue
                            else:
                                break

                # 智能路径更新策略

                # 1. 确定起点和当前时间
                if not routes[best_k]:
                    # 从集散中心出发
                    start_node = depots[best_k]
                    current_time = initial_time
                else:
                    # 从当前路径的最后一个节点出发
                    start_node = routes[best_k][-1]
                    current_time = vehicle_arrival_times[best_k][start_node]

                # 2. 获取当前时段和对应的最优路径
                current_period = get_time_period(current_time)
                adj_matrix = build_adjacency_matrix(current_period, 'best')
                paths = dijkstra(adj_matrix, start_node)

                if best_m in paths:
                    # 获取最优路径
                    optimal_path, _ = paths[best_m]

                    # 记录完整路径信息，以便显示所有中间节点
                    path_details = []
                    path_times = {}

                    # 初始化时间计算
                    sim_time = current_time

                    # 3. 计算实际的时间和成本，考虑时段变化
                    for j in range(len(optimal_path) - 1):
                        from_node = optimal_path[j]
                        to_node = optimal_path[j + 1]

                        # 根据当前时刻确定时段
                        travel_period = get_time_period(sim_time)

                        # 获取对应时段的行程时间
                        edge_time = tu[travel_period][(from_node, to_node)]

                        # 更新时间和记录节点信息
                        sim_time += edge_time
                        path_times[to_node] = sim_time

                        # 记录路径详情用于显示
                        path_details.append((from_node, to_node, travel_period, edge_time))

                    # 4. 更新路径
                    if not routes[best_k]:
                        # 如果是新路径，添加完整路径
                        routes[best_k].extend(optimal_path)
                    else:
                        # 如果路径已存在，排除起点
                        routes[best_k].extend(optimal_path[1:])

                    # 5. 更新车辆到达时间
                    for node, time_val in path_times.items():
                        vehicle_arrival_times[best_k][node] = time_val

                    # 6. 记录详细路径数据到车辆信息中，以便后续显示完整路径
                    if 'path_details' not in served_by_vehicle:
                        served_by_vehicle['path_details'] = {}
                    if best_k not in served_by_vehicle['path_details']:
                        served_by_vehicle['path_details'][best_k] = []

                    # 添加本次路径的详情
                    served_by_vehicle['path_details'][best_k].extend(path_details)

                    # 7. 更新负载、时间和服务点
                    loads[best_k] -= p[best_m]

                    # 计算实际成本：路径成本
                    real_cost = sim_time - current_time
                    times[best_k] += real_cost

                    # 记录服务点
                    served_by_vehicle[best_k].add(best_m)

                    # 8. 将需求点标记为已服务
                    if best_m in unserved:
                        unserved.remove(best_m)
                    else:
                        print(f"警告: 需求点{best_m}不在未服务列表中，可能是重复服务点")

                    # 从剩余簇中移除该需求点
                    if best_m in remaining_clusters:
                        remaining_clusters.remove(best_m)
                else:
                    # 处理找不到路径的异常情况
                    print(f"错误：无法找到从节点{start_node}到需求点{best_m}的路径")
                    if best_m in unserved:
                        unserved.remove(best_m)  # 防止无限循环
                        
                    # 增加延时，防止过多的错误输出
                    time.sleep(0.01)  # 短暂延时，减少输出频率

            # 如果有未服务的需求点，这个方案不完整，跳过
            if unserved:
                print(f"方案不完整，有{len(unserved)}个未服务的需求点")
                continue

            # 保存当前方案，包括生成它的时段信息和到达时间
            candidate_solutions.append({
                'routes': {k: routes[k][:] for k in K},  # 深拷贝路径
                'served_by_vehicle': {k: served_by_vehicle[k].copy() for k in K},  # 深拷贝服务点
                'depots': depots.copy(),  # 深拷贝起点
                'start_period': start_period,  # 记录生成方案的起始时段
                'start_time': initial_time,  # 记录起始时间
                'arrival_times': {k: vehicle_arrival_times[k].copy() for k in K}  # 记录到达时间
            })

            print(f"方案完成，服务了所有{len(M)}个需求点")

    # 评估每个方案在跨时段情况下的表现
    print("\n评估候选方案在跨时段下的表现...")

    # 计算每个方案在跨时段下的行程时间
    solution_performances = []

    for i, solution in enumerate(candidate_solutions):
        # 确保每辆车返回集散中心
        for k in solution['routes']:
            if solution['routes'][k]:  # 如果路径不为空
                last_node = solution['routes'][k][-1]
                depot = solution['depots'][k]

                # 只有当最后一个节点不是集散中心时，才需要添加返回路径
                if last_node != depot:
                    # 使用对应时段的Dijkstra算法查找返回路径
                    current_time = solution['arrival_times'][k][last_node]
                    current_period = get_time_period(current_time)
                    worst_adj_matrix = build_adjacency_matrix(current_period, 'worst')
                    paths = dijkstra(worst_adj_matrix, last_node)

                    if depot in paths:
                        return_path, _ = paths[depot]
                        # 添加返回路径(除了起点，因为已经在路径中)
                        solution['routes'][k].extend(return_path[1:])

                        # 更新到达时间
                        temp_time = current_time
                        for j in range(len(return_path) - 1):
                            from_node = return_path[j]
                            to_node = return_path[j + 1]
                            temp_period = get_time_period(temp_time)
                            edge_time = tu[temp_period][(from_node, to_node)]
                            temp_time += edge_time  # edge_time已经是分钟
                            solution['arrival_times'][k][to_node] = temp_time
                    else:
                        print(f"警告: 无法找到从节点{last_node}到集散中心{depot}的路径")

        # 评估跨时段的行程时间
        worst_time, _ = evaluate_solution_cross_period(solution['routes'], solution['depots'], solution['start_time'],
                                                       'worst')
        best_time, _ = evaluate_solution_cross_period(solution['routes'], solution['depots'], solution['start_time'],
                                                      'best')
        regret = worst_time - best_time

        # 格式化起始时间显示
        start_hour = int(solution['start_time'] // 60)
        start_minute = int(solution['start_time'] % 60)

        solution_performances.append({
            'solution_idx': i,
            'worst_time': worst_time,
            'best_time': best_time,
            'regret': regret
        })

        print(
            f"方案{i + 1}（起始时间:{start_hour}:{start_minute:02d}, 时段:{TIME_PERIODS[solution['start_period']]}）: 最坏={worst_time:.2f}, 最佳={best_time:.2f}, 后悔值={regret:.2f}")

    # 找到最小最大后悔值的方案
    if not solution_performances:
        print("\n没有找到可行的方案，请尝试其他出发时间。")
        return

    min_regret_solution = min(solution_performances, key=lambda x: x['regret'])
    min_worst_time_solution = min(solution_performances, key=lambda x: x['worst_time'])

    print(
        f"\n最小后悔值的方案是方案{min_regret_solution['solution_idx'] + 1}，后悔值为: {min_regret_solution['regret']:.2f}")
    print(
        f"最小最坏时间的方案是方案{min_worst_time_solution['solution_idx'] + 1}，最坏时间为: {min_worst_time_solution['worst_time']:.2f}")

    # 输出最佳方案详情
    best_solution = candidate_solutions[min_regret_solution['solution_idx']]
    start_hour = int(best_solution['start_time'] // 60)
    start_minute = int(best_solution['start_time'] % 60)

    print("\n最佳方案详情:")
    print(f"该方案是在起始时段 {TIME_PERIODS[best_solution['start_period']]}（{start_hour}:{start_minute:02d}）下生成的")

    routes = best_solution['routes']
    depots = best_solution['depots']
    served_by_vehicle = best_solution['served_by_vehicle']
    arrival_times = best_solution['arrival_times']

    for k in K:
        if routes[k]:
            served_points = served_by_vehicle[k]
            remaining_load = Q
            demands_served = []

            # 计算实际消耗的物资
            for node in served_points:
                remaining_load -= p[node]
                demands_served.append((node, p[node]))

            # 生成路径字符串，包含到达时间和时段信息
            path_output = []
            path_output.append(f"  起点({depots[k]})[物资:{Q}kg, 出发时间:{start_hour}:{start_minute:02d}]")

            # 直接使用算法生成过程中记录的详细路径信息
            if 'path_details' in served_by_vehicle and best_k in served_by_vehicle['path_details']:
                # 从记录的路径细节中重建完整路径
                path_details = served_by_vehicle['path_details'][best_k]

                # 初始化路径时间
                path_times = {depots[k]: initial_time}

                # 从路径细节中提取所有节点和时间
                for from_node, to_node, period, edge_time in path_details:
                    if from_node not in path_times:
                        # 如果起点不在时间记录中，可能是中间连接节点
                        # 使用前一个节点的时间加上估计值
                        prev_time = max(path_times.values()) if path_times else initial_time
                        path_times[from_node] = prev_time

                    # 计算到达时间
                    arrival_time = path_times[from_node] + edge_time
                    path_times[to_node] = arrival_time

                # 处理异常情况：确保所有节点都有时间记录
                all_nodes_in_route = set(routes[k])
                for node in all_nodes_in_route:
                    if node not in path_times:
                        # 为缺失的节点估算时间
                        path_times[node] = arrival_times[k].get(node, max(path_times.values()))
            else:
                # 使用原有的重建逻辑作为备选
                full_path = [depots[k]]  # 从起点开始
                current_node = depots[k]
                path_times = {current_node: initial_time}

                # 收集所有服务点，按照访问顺序排序
                service_points = []
                for node in routes[k][1:]:  # 跳过起点
                    if node in M and node in served_points:
                        service_points.append((node, arrival_times[k].get(node, float('inf'))))

                # 按到达时间排序
                sorted_service_points = [node for node, _ in sorted(service_points, key=lambda x: x[1])]

                # 重建详细路径，显示所有中间节点
                for i, target in enumerate(sorted_service_points):
                    # 使用最佳时段选择算法
                    best_path_time = float('inf')
                    best_detailed_path = None
                    best_time_records = {}

                    # 尝试多个时段的路径规划
                    for period in range(len(TIME_PERIODS)):
                        current_time = path_times[current_node]
                        adj_matrix = build_adjacency_matrix(period, 'best')
                        paths = dijkstra(adj_matrix, current_node)

                        if target in paths:
                            # 获取路径和距离
                            detailed_path, _ = paths[target]

                            # 模拟实际行程时间
                            sim_time = current_time
                            time_records = {}

                            # 计算实际路径时间
                            for j in range(1, len(detailed_path)):
                                from_node = detailed_path[j - 1]
                                to_node = detailed_path[j]
                                travel_period = get_time_period(sim_time)
                                if (from_node, to_node) in tu[travel_period]:
                                    edge_time = tu[travel_period][(from_node, to_node)]
                                    sim_time += edge_time
                                    time_records[to_node] = sim_time

                            # 如果找到更短的路径
                            if sim_time < best_path_time:
                                best_path_time = sim_time
                                best_detailed_path = detailed_path
                                best_time_records = time_records

                    if best_detailed_path:
                        # 更新路径和时间
                        for j in range(1, len(best_detailed_path)):
                            to_node = best_detailed_path[j]
                            if to_node in best_time_records:
                                path_times[to_node] = best_time_records[to_node]

                        # 更新当前节点
                        current_node = target
                    else:
                        print(f"警告: 无法找到从节点{current_node}到服务点{target}的路径")

                # 添加返回起点的路径
                if current_node != depots[k]:
                    # 使用夜间时段（最快）寻找返回路径
                    current_time = path_times[current_node]
                    adj_matrix = build_adjacency_matrix(0, 'best')
                    paths = dijkstra(adj_matrix, current_node)

                    if depots[k] in paths:
                        return_path, _ = paths[depots[k]]

                        # 更新返回路径和时间
                        for j in range(1, len(return_path)):
                            from_node = return_path[j - 1]
                            to_node = return_path[j]
                            travel_period = get_time_period(current_time)
                            if (from_node, to_node) in tu[travel_period]:
                                edge_time = tu[travel_period][(from_node, to_node)]
                                current_time += edge_time
                                path_times[to_node] = current_time

            # 生成完整路径输出
            all_nodes = routes[k]  # 使用实际路径顺序
            # 确保所有节点都有时间记录
            for node in all_nodes:
                if node not in path_times:
                    path_times[node] = arrival_times[k].get(node, initial_time)
            # 按时间排序，确保正确显示
            all_nodes = sorted(all_nodes, key=lambda x: path_times.get(x, float('inf')))

            # 打印路径信息
            current_load = Q
            for node in all_nodes:
                arrival_minutes = path_times[node]
                arrival_period = get_time_period(arrival_minutes)
                arrival_hour = int(arrival_minutes // 60)
                arrival_minute = int(arrival_minutes % 60)

                if node in M:
                    if node in served_points:  # 如果是服务点
                        current_load -= p[node]
                        path_output.append(
                            f"需求点({node})*[需求:{p[node]}kg,剩余:{current_load}kg, 到达:{arrival_hour}:{arrival_minute:02d}, 时段:{TIME_PERIODS[arrival_period]}]")
                    else:
                        path_output.append(
                            f"需求点({node})[剩余:{current_load}kg, 到达:{arrival_hour}:{arrival_minute:02d}, 时段:{TIME_PERIODS[arrival_period]}]")
                else:
                    if node == depots[k]:  # 如果是起点或返回起点
                        if node == all_nodes[0]:  # 如果是起点
                            path_output[
                                0] = f"  起点({depots[k]})[物资:{Q}kg, 出发时间:{start_hour}:{start_minute:02d}]"  # 替换之前的起点信息
                        else:  # 如果是返回起点
                            path_output.append(
                                f"返回起点({node})[剩余:{current_load}kg, 到达:{arrival_hour}:{arrival_minute:02d}, 时段:{TIME_PERIODS[arrival_period]}]")
                    else:  # 中间节点
                        path_output.append(
                            f"中间节点({node})[剩余:{current_load}kg, 到达:{arrival_hour}:{arrival_minute:02d}, 时段:{TIME_PERIODS[arrival_period]}]")

            # 打印路径
            print(f"\n车辆 {k} 的路径:")
            print(" -> ".join(path_output))

            # 打印服务详情
            print(f"  服务的受灾点({len(served_points)}个): {sorted(served_points)}")
            print(f"  总配送物资: {sum(p[j] for j in served_points)}kg")
            print(f"  剩余物资: {remaining_load}kg")
            print(f"  配送详情:")

            # 按照到达时间排序服务点
            served_with_time = [(node, arrival_times[k].get(node, float('inf'))) for node in served_points]
            sorted_served = [node for node, _ in sorted(served_with_time, key=lambda x: x[1])]

            for node in sorted_served:
                demand = p[node]
                arrival_minutes = arrival_times[k].get(node, 0)
                arrival_hour = int(arrival_minutes // 60)
                arrival_minute = int(arrival_minutes % 60)
                arrival_period = get_time_period(arrival_minutes)
                print(
                    f"    需求点 {node}: {demand}kg, 到达时间: {arrival_hour}:{arrival_minute:02d} ({TIME_PERIODS[arrival_period]})")

    # 输出最佳方案的总行程时间
    worst_time, _ = evaluate_solution_cross_period(routes, depots, best_solution['start_time'], 'worst')
    best_time, _ = evaluate_solution_cross_period(routes, depots, best_solution['start_time'], 'best')

    print("\n最佳方案总结:")
    print(f"最坏情景下的总行程时间: {worst_time:.2f}分钟")
    print(f"最佳情景下的总行程时间: {best_time:.2f}分钟")
    print(f"后悔值: {min_regret_solution['regret']:.2f}分钟")

    print(f"\n考虑跨时段的最小最大后悔值算法求解时间: {time.time() - algorithm_start_time:.2f}秒")


if __name__ == "__main__":
    # 设置随机种子，使结果可重现
    random.seed(int(time.time()))  # 使用当前时间作为随机种子

    # 让用户指定出发时间
    user_input = input("请输入出发时间（格式为'小时:分钟'，例如'8:30'表示早上8点30分，或直接按回车使用默认设置）: ")

    if user_input.strip():
        try:
            # 尝试解析用户输入的时间格式
            if ":" in user_input:
                hour, minute = map(int, user_input.split(":"))
                specified_start_time = hour * 60 + minute  # 转换为分钟
            else:
                # 如果用户只输入小时，假设分钟为0
                hour = int(user_input)
                specified_start_time = hour * 60

            print(f"使用指定的出发时间: {hour}:{minute if 'minute' in locals() else 0:02d}")
            # 运行考虑跨时段的最小最大后悔值算法，使用指定的出发时间
            cross_period_min_max_regret_vrp(specified_start_time)
        except ValueError as e:
            print(f"输入的时间格式不正确: {e}")
            print("使用默认设置")
            # 运行考虑跨时段的最小最大后悔值算法，使用默认设置
            cross_period_min_max_regret_vrp()
    else:
        print("使用默认设置（考虑所有时段）")
        # 运行考虑跨时段的最小最大后悔值算法，使用默认设置
        cross_period_min_max_regret_vrp() 