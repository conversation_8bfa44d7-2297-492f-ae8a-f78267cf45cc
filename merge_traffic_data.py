# -*- coding: utf-8 -*-
"""
交通数据合并脚本
将data_output/csv/目录下所有traffic_data_*.csv文件合并为一个统一文件
"""

import os
import csv
import pandas as pd
from datetime import datetime
from pathlib import Path
import re

def natural_sort_key(text):
    """自然排序函数，用于正确排序路段ID（如1-2, 1-9, 2-3等）"""
    def convert(text):
        return int(text) if text.isdigit() else text.lower()
    
    def alphanum_key(key):
        return [convert(c) for c in re.split('([0-9]+)', key)]
    
    return alphanum_key(text)

def merge_traffic_data():
    """合并所有交通数据CSV文件"""
    
    # 设置路径
    csv_dir = Path("data_output/csv")
    output_file = "consolidated_traffic7.28测试1小时.csv"
    
    print("🚀 开始合并交通数据文件...")
    print(f"📁 源目录: {csv_dir}")
    print(f"📄 输出文件: {output_file}")
    
    # 检查目录是否存在
    if not csv_dir.exists():
        print(f"❌ 错误: 目录 {csv_dir} 不存在")
        return False
    
    # 获取所有traffic_data_*.csv文件
    csv_files = list(csv_dir.glob("traffic_data_*.csv"))
    
    if not csv_files:
        print("❌ 错误: 未找到任何traffic_data_*.csv文件")
        return False
    
    print(f"📊 找到 {len(csv_files)} 个CSV文件")
    
    # 存储所有数据
    all_data = []
    
    # 逐个读取CSV文件
    for i, csv_file in enumerate(csv_files, 1):
        try:
            print(f"📖 正在读取文件 {i}/{len(csv_files)}: {csv_file.name}")
            
            # 读取CSV文件
            with open(csv_file, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                
                # 读取数据行
                for row in reader:
                    # 跳过空行或无效行
                    if not row.get('路段ID') or not row.get('采集时间'):
                        continue
                    
                    # 添加到总数据中
                    all_data.append({
                        '路段ID': row.get('路段ID', '').strip(),
                        '采集时间': row.get('采集时间', '').strip(),
                        '通行时间(秒)': row.get('通行时间(秒)', '').strip(),
                        '距离(米)': row.get('距离(米)', '').strip(),
                        '交通状况': row.get('交通状况', '').strip(),
                        '红绿灯数量': row.get('红绿灯数量', '').strip(),
                        '收费站数量': row.get('收费站数量', '').strip(),
                        '限行状态': row.get('限行状态', '').strip(),
                        '错误信息': row.get('错误信息', '').strip()
                    })
                    
        except Exception as e:
            print(f"⚠️ 警告: 读取文件 {csv_file.name} 时出错: {e}")
            continue
    
    if not all_data:
        print("❌ 错误: 未读取到任何有效数据")
        return False
    
    print(f"📊 总共读取到 {len(all_data)} 条数据记录")
    
    # 数据去重
    print("🔄 正在去除重复记录...")
    
    # 创建唯一标识符用于去重（路段ID + 采集时间）
    seen = set()
    unique_data = []
    
    for row in all_data:
        identifier = f"{row['路段ID']}_{row['采集时间']}"
        if identifier not in seen:
            seen.add(identifier)
            unique_data.append(row)
    
    removed_duplicates = len(all_data) - len(unique_data)
    if removed_duplicates > 0:
        print(f"🗑️ 去除了 {removed_duplicates} 条重复记录")
    
    print(f"✅ 去重后剩余 {len(unique_data)} 条记录")
    
    # 数据排序
    print("📊 正在排序数据...")
    
    def sort_key(row):
        # 第一优先级：路段ID（自然排序）
        segment_id = row['路段ID']
        
        # 第二优先级：采集时间
        try:
            collection_time = datetime.strptime(row['采集时间'], '%Y-%m-%d %H:%M:%S')
        except:
            collection_time = datetime.min
        
        return (natural_sort_key(segment_id), collection_time)
    
    unique_data.sort(key=sort_key)
    print("✅ 数据排序完成")
    
    # 保存合并后的数据
    print(f"💾 正在保存到文件: {output_file}")
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
            fieldnames = [
                '路段ID', '采集时间', '通行时间(秒)', '距离(米)', '交通状况',
                '红绿灯数量', '收费站数量', '限行状态', '错误信息'
            ]
            
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            
            # 写入表头
            writer.writeheader()
            
            # 写入数据
            writer.writerows(unique_data)
        
        print(f"✅ 文件保存成功: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存文件时出错: {e}")
        return False
    
    # 数据验证和统计
    print("\n📊 数据统计信息:")
    
    # 统计路段数量
    segments = set(row['路段ID'] for row in unique_data)
    print(f"   📍 路段数量: {len(segments)}")
    
    # 统计时间范围
    times = []
    for row in unique_data:
        try:
            time = datetime.strptime(row['采集时间'], '%Y-%m-%d %H:%M:%S')
            times.append(time)
        except:
            continue
    
    if times:
        min_time = min(times)
        max_time = max(times)
        duration = max_time - min_time
        print(f"   ⏰ 时间范围: {min_time.strftime('%Y-%m-%d %H:%M:%S')} 至 {max_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   ⏱️ 总时长: {duration}")
    
    # 统计采集次数
    collection_times = set(row['采集时间'] for row in unique_data)
    print(f"   🔄 采集次数: {len(collection_times)}")
    
    # 统计成功率
    successful_records = sum(1 for row in unique_data if not row['错误信息'])
    success_rate = (successful_records / len(unique_data)) * 100 if unique_data else 0
    print(f"   ✅ 成功记录: {successful_records}/{len(unique_data)} ({success_rate:.1f}%)")
    
    # 统计交通状况分布
    traffic_status_count = {}
    for row in unique_data:
        status = row['交通状况'] or '未知'
        traffic_status_count[status] = traffic_status_count.get(status, 0) + 1
    
    print(f"   🚦 交通状况分布:")
    for status, count in sorted(traffic_status_count.items()):
        percentage = (count / len(unique_data)) * 100
        print(f"      {status}: {count} ({percentage:.1f}%)")
    
    print(f"\n🎉 数据合并完成！")
    print(f"📄 输出文件: {output_file}")
    print(f"📊 总记录数: {len(unique_data)}")
    
    return True

def main():
    """主函数"""
    print("="*60)
    print("           交通数据合并工具")
    print("="*60)
    
    try:
        success = merge_traffic_data()
        
        if success:
            print("\n✅ 合并任务完成！")
            print("\n💡 使用建议:")
            print("   1. 可以用Excel打开consolidated_traffic7.28测试1小时.csv查看数据")
            print("   2. 数据已按路段ID和时间排序，便于分析")
            print("   3. 可以进行时间序列分析和路段对比分析")
        else:
            print("\n❌ 合并任务失败！")
            
    except Exception as e:
        print(f"\n❌ 程序运行异常: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
