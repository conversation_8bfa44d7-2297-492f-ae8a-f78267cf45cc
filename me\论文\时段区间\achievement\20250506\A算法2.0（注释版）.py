import numpy as np
import heapq
from collections import defaultdict, deque
import multiprocessing
import time

# ============== 数据准备部分 ==============
# 边集合，每个元组表示两个节点之间的双向连接
edges = [(1, 2), (2, 1), (1, 3), (3, 1), (1, 4), (4, 1), (1, 5), (5, 1),
         (2, 3), (3, 2), (2, 6), (6, 2), (2, 7), (7, 2), (3, 4), (4, 3),
         (3, 7), (7, 3), (3, 8), (8, 3), (4, 5), (5, 4), (4, 8), (8, 4),
         (4, 9), (9, 4), (5, 9), (9, 5), (5, 10), (10, 5), (6, 7), (7, 6),
         (6, 11), (11, 6), (7, 8), (8, 7), (7, 11), (11, 7), (7, 12), (12, 7),
         (8, 9), (9, 8), (8, 12), (12, 8), (8, 13), (13, 8), (9, 10), (10, 9),
         (9, 13), (13, 9), (9, 14), (14, 9), (10, 14), (14, 10), (10, 15), (15, 10),
         (11, 12), (12, 11), (11, 16), (16, 11), (11, 17), (17, 11), (12, 13), (13, 12),
         (12, 17), (17, 12), (12, 18), (18, 12), (13, 14), (14, 13), (13, 18), (18, 13),
         (13, 19), (19, 13), (14, 15), (15, 14), (14, 19), (19, 14), (15, 19), (19, 15),
         (16, 17), (17, 16), (16, 20), (20, 16), (17, 18), (18, 17), (17, 20), (20, 17),
         (18, 19), (19, 18), (18, 20), (20, 18), (19, 20), (20, 19)]  # 共94条边

# 基础通行时间下限（单位：分钟），对应每条边
l_base = [9, 9, 7, 7, 6, 6, 10, 10, 6, 6,
          12, 12, 10, 10, 11, 11, 8, 8, 10, 10,
          9, 9, 7, 7, 6, 6, 7, 7, 9, 9,
          8, 8, 6, 6, 9, 9, 8, 8, 6, 6,
          8, 8, 11, 11, 6, 6, 10, 10, 11, 11,
          6, 6, 10, 10, 10, 10, 8, 8, 6, 6,
          7, 7, 8, 8, 6, 6, 9, 9, 9, 9,
          10, 10, 6, 6, 7, 7, 8, 8, 6, 6,
          9, 9, 7, 10, 8, 8, 6, 6, 9, 9,  # 补充缺失的6个值
          7, 8, 9, 10]  # 总长度94

# 基础通行时间上限，对应每条边
u_base = [14, 14, 13, 13, 12, 12, 15, 15,
          11, 11, 15, 15, 14, 14, 15, 15,
          12, 12, 15, 15, 14, 14, 13, 13,
          12, 12, 12, 12, 15, 15, 14, 14,
          12, 12, 14, 14, 14, 14, 11, 11,
          12, 12, 15, 15, 11, 11, 14, 14,
          15, 15, 11, 11, 14, 14, 14, 14,
          12, 12, 11, 11, 15, 15, 13, 13,
          11, 11, 15, 15, 15, 15, 13, 13,
          11, 11, 12, 12, 15, 15, 10, 10,
          15, 15, 14, 14, 15, 15, 12, 12,
          15, 15, 13, 14, 15, 15]  # 总长度8*11+6=94

# 道路等级（1-4分别代表快速路、主干路、次干路、支路）
road_levels = [1, 1, 2, 2, 3, 3, 4, 4,
               1, 1, 4, 4, 3, 3, 1, 1,
               2, 2, 1, 1, 1, 1, 4, 4,
               3, 3, 2, 2, 1, 1, 2, 2,
               1, 1, 2, 2, 2, 2, 3, 3,
               2, 2, 4, 4, 1, 1, 2, 2,
               2, 2, 3, 3, 4, 4, 1, 1,
               3, 3, 4, 4, 3, 3, 3, 3,
               2, 2, 1, 1, 3, 3, 4, 4,
               3, 3, 3, 3, 2, 2, 1, 1,
               4, 4, 1, 1, 4, 4, 2, 2,
               4, 4, 3, 3, 4, 4]

# 分时段拥堵系数（按道路等级区分）
congestion_factors = {
    1: {  # 快速路
        "00:00-07:00": (-0.8, -0.1),  # 格式：(下限调整系数, 上限调整系数)
        "07:00-07:10": (0.3, 1.1),  # 早高峰
        "07:10-07:20": (0.3, 1.2),
        "07:20-07:30": (0.4, 1.1),
        "07:30-07:40": (0.2, 1.2),
        "07:40-07:50": (0.4, 1.3),
        "07:50-08:00": (0.2, 1.5),
        "08:00-08:10": (0.3, 1.4),
        "08:10-08:20": (0.4, 1.4),
        "08:20-08:30": (0.3, 1.3),
        "08:30-08:40": (0.2, 1.2),
        "08:40-08:50": (0.4, 1.1),
        "08:50-09:00": (0.3, 1.0),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.2, 1.5),  # 晚高峰
        "19:00-00:00": (-0.8, -0.1)  # 平
    },
    2: {  # 主干路
        "00:00-07:00": (-0.7, -0.2),  # 平
        "07:00-07:10": (0.4, 1.0),  # 早高峰
        "07:10-07:20": (0.4, 1.1),
        "07:20-07:30": (0.5, 1.0),
        "07:30-07:40": (0.3, 1.1),
        "07:40-07:50": (0.5, 1.2),
        "07:50-08:00": (0.3, 1.4),
        "08:00-08:10": (0.4, 1.3),
        "08:10-08:20": (0.5, 1.3),
        "08:20-08:30": (0.4, 1.2),
        "08:30-08:40": (0.3, 1.1),
        "08:40-08:50": (0.5, 1.0),
        "08:50-09:00": (0.4, 0.9),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.3, 1.4),  # 晚高峰
        "19:00-00:00": (-0.7, -0.2)  # 平
    },
    3: {  # 次干路
        "00:00-07:00": (-0.6, -0.3),  # 平
        "07:00-07:10": (0.5, 0.9),  # 早高峰
        "07:10-07:20": (0.5, 1.0),
        "07:20-07:30": (0.6, 0.9),
        "07:30-07:40": (0.4, 1.0),
        "07:40-07:50": (0.6, 1.1),
        "07:50-08:00": (0.4, 1.3),
        "08:00-08:10": (0.5, 1.2),
        "08:10-08:20": (0.6, 1.2),
        "08:20-08:30": (0.5, 1.1),
        "08:30-08:40": (0.4, 1.0),
        "08:40-08:50": (0.6, 0.9),
        "08:50-09:00": (0.5, 0.8),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.4, 1.3),  # 晚高峰
        "19:00-00:00": (-0.6, -0.3)  # 平
    },
    4: {  # 支路
        "00:00-07:00": (-0.5, -0.4),  # 平
        "07:00-07:10": (0.6, 0.8),  # 早高峰
        "07:10-07:20": (0.6, 0.9),
        "07:20-07:30": (0.7, 0.8),
        "07:30-07:40": (0.5, 0.9),
        "07:40-07:50": (0.7, 1.0),
        "07:50-08:00": (0.5, 1.2),
        "08:00-08:10": (0.6, 1.1),
        "08:10-08:20": (0.7, 1.1),
        "08:20-08:30": (0.6, 1.0),
        "08:30-08:40": (0.5, 0.9),
        "08:40-08:50": (0.7, 0.8),
        "08:50-09:00": (0.6, 0.7),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.5, 1.2),  # 晚高峰
        "19:00-00:00": (-0.5, -0.4)  # 平
    }
}

# 时段定义（转换为分钟数）
TIME_PERIODS = {
    "00:00-07:00": (0, 420),  # 0点0分到7点0分（420分钟）
    "07:00-07:10": (420, 430),  # 7点0分到7点10分
    "07:10-07:20": (430, 440),
    "07:20-07:30": (440, 450),
    "07:30-07:40": (450, 460),
    "07:40-07:50": (460, 470),
    "07:50-08:00": (470, 480),
    "08:00-08:10": (480, 490),
    "08:10-08:20": (490, 500),
    "08:20-08:30": (500, 510),
    "08:30-08:40": (510, 520),
    "08:40-08:50": (520, 530),
    "08:50-09:00": (530, 540),
    "09:00-17:00": (540, 1020),
    "17:00-19:00": (1020, 1140),
    "19:00-00:00": (1140, 1440)
}


# ============== 时变阻抗生成模块 ==============
def generate_time_dependent_data(edges, l_base, u_base, congestion_factors, road_levels):
    """生成时变路网数据
    返回数据结构示例：
    {
        '1': {
            '2': [[0,420,8.1,12.6], [420,430,11.7,18.9], ...],
            '3': [...],
            ...
        },
        ...
    }
    """
    network_data = defaultdict(dict)  # 使用默认字典存储网络数据
    for idx, (i, j) in enumerate(edges):  # 遍历所有边
        source = str(i)  # 起点转换为字符串
        target = str(j)  # 终点转换为字符串
        level = road_levels[idx]  # 获取当前边的道路等级
        
        time_ranges = []  # 存储该边的时间段数据
        # 遍历该等级道路的所有时段
        for period, (f, g) in congestion_factors[level].items():
            start, end = TIME_PERIODS[period]  # 获取时段的起止分钟数
            # 计算时变阻抗值（保留两位小数）
            l_t = round(l_base[idx] * (1 + f), 2)  # 下限时间
            u_t = round(u_base[idx] * (1 + g), 2)  # 上限时间
            time_ranges.append([start, end, l_t, u_t])
        
        # 将处理好的时间段数据存入网络结构
        network_data[source][target] = time_ranges
    return network_data


# ============== 增强版路径规划类 ==============
class EnhancedRoadNetwork:
    def __init__(self, edges, l_base, u_base, congestion_factors, road_levels):
        # 初始化基础数据
        self.edges = edges  # 边集合
        self.l_base = l_base  # 下限基准时间
        self.u_base = u_base  # 上限基准时间
        self.congestion_factors = congestion_factors  # 拥堵系数
        self.road_levels = road_levels  # 道路等级
        
        # 生成时变路网数据
        network_data = generate_time_dependent_data(
            edges, l_base, u_base, congestion_factors, road_levels
        )
        
        # 构建图结构
        self.graph = defaultdict(list)  # 使用邻接表存储图
        self._initialize_graph(network_data)  # 初始化图
        
        # 鲁棒成本缓存（提升性能）
        self.robust_cost_cache = {}

    def _initialize_graph(self, network_data):
        """构建图结构的邻接表"""
        for source, neighbors in network_data.items():  # 遍历所有起点
            for target, time_ranges in neighbors.items():  # 遍历所有终点
                # 添加边及其时间段数据
                self.graph[source].append((target, time_ranges))

    def get_impedance(self, impedance_ranges, time):
        """获取当前时间的阻抗范围（带跨天处理）"""
        time %= 1440  # 将时间规范到0-1439分钟范围内（24小时）
        # 查找匹配的时间段
        for start, end, low, high in impedance_ranges:
            if start <= time < end:
                return low, high  # 返回找到的阻抗值
        # 未找到时返回最后一个区间（处理边界情况）
        return impedance_ranges[-1][2], impedance_ranges[-1][3]

    def calculate_robust_cost(self, path, start_time):
        """计算路径的鲁棒成本（核心算法）"""
        # 生成缓存键（路径+出发时间）
        key = (tuple(path), start_time)
        if key in self.robust_cost_cache:  # 检查缓存
            return self.robust_cost_cache[key]

        # 计算原路径最坏情况耗时（a）
        a = 0  # 总耗时
        current_time = start_time  # 当前时间
        blocked_edges = set()  # 需要排除的原路径边
        
        # 遍历路径中的每个节点
        for i in range(len(path) - 1):
            u, v = path[i], path[i+1]  # 当前边
            blocked_edges.add((u, v))  # 记录需要排除的边
            
            # 查找当前边的阻抗数据
            for neighbor, ranges in self.graph[u]:
                if neighbor == v:
                    # 获取当前时间的阻抗值
                    low, high = self.get_impedance(ranges, current_time)
                    a += high  # 累加最坏情况（使用上限时间）
                    current_time = (current_time + high) % 1440  # 更新时间
                    break

        # 使用改进Dijkstra算法寻找备选路径（最佳情况b）
        heap = [(0, start_time, path[0], [])]  # 优先队列（成本，时间，当前节点，路径）
        best_b = float('inf')  # 最佳耗时初始化为无穷大
        best_alt_path = []  # 最佳备选路径
        
        # 开始搜索
        while heap:
            # 弹出当前最优候选
            cost, time, node, alt_path = heapq.heappop(heap)
            
            # 到达终点处理
            if node == path[-1]:
                if cost < best_b:
                    best_b = cost
                    best_alt_path = alt_path + [node]
                continue
            
            # 剪枝：当前路径已不如已知最优解
            if cost > best_b:
                continue
            
            # 遍历相邻节点
            for neighbor, ranges in self.graph[node]:
                # 排除原路径边且避免循环
                if (node, neighbor) in blocked_edges or neighbor in alt_path:
                    continue
                
                # 获取当前时间阻抗（使用下限时间）
                low, _ = self.get_impedance(ranges, time)
                new_time = (time + low) % 1440  # 计算新时间
                new_cost = cost + low  # 计算新成本
                new_path = alt_path + [node]  # 更新路径
                
                # 加入优先队列
                if new_cost < best_b:
                    heapq.heappush(heap, (new_cost, new_time, neighbor, new_path))

        # 计算结果
        b = best_b if best_alt_path else float('inf')  # 处理无解情况
        robust_cost = a - b if b != float('inf') else float('inf')  # 鲁棒成本
        result = (robust_cost, a, b, best_alt_path)  # 打包结果
        self.robust_cost_cache[key] = result  # 存入缓存
        return result

    def find_min_robust_path(self, start, end, start_time):
        """主搜索函数"""
        # 初始化优先队列（预估鲁棒成本，当前路径，当前时间）
        heap = [(0, [start], start_time)]
        final_result = (None, float('inf'), 0, 0, [])  # （最优路径，鲁棒成本，a，b，备选路径）
        
        # 开始搜索
        while heap:
            # 弹出当前最优候选
            _, current_path, current_time = heapq.heappop(heap)
            
            # 到达终点处理
            if current_path[-1] == end:
                # 计算完整鲁棒成本
                robust_cost, a, b, alt = self.calculate_robust_cost(current_path, start_time)
                # 更新最优解
                if robust_cost < final_result[1]:
                    final_result = (current_path, robust_cost, a, b, alt)
                continue
            
            # 扩展当前路径
            current_node = current_path[-1]
            for neighbor, ranges in self.graph[current_node]:
                # 避免循环
                if neighbor in current_path:
                    continue
                
                # 生成临时路径
                temp_path = current_path + [neighbor]
                # 快速预估鲁棒成本
                _, a_est, b_est, _ = self.calculate_robust_cost(temp_path, start_time)
                est = a_est - b_est if b_est != 0 else float('inf')  # 预估成本
                
                # 加入队列（仅当预估成本优于当前最优解）
                if est < final_result[1]:
                    heapq.heappush(heap, (est, temp_path, current_time))
        
        return final_result  # 返回最终结果


# ============== 主程序模块 ==============
if __name__ == '__main__':
    # 数据完整性检查
    assert len(edges) == len(l_base) == len(u_base), "基础数据长度不匹配"
    
    # 时间覆盖验证（必须覆盖24小时）
    total = sum(end - start for start, end in TIME_PERIODS.values())
    assert total == 1440, f"时间区间覆盖不完整，当前总时长：{total}分钟"
    print(f"时间覆盖验证通过，总时长：{total}分钟")

    # 交互式查询功能
    def time_to_minutes(t_str):
        """时间转换函数（HH:MM → 分钟数）"""
        try:
            hour, minute = map(int, t_str.split(':'))
            if 0 <= hour < 24 and 0 <= minute < 60:
                return hour * 60 + minute
            return None
        except:
            return None

    # 初始化路网
    road_net = EnhancedRoadNetwork(
        edges, l_base, u_base, congestion_factors, road_levels
    )

    # 用户交互循环
    while True:
        print("\n=== 路径规划查询 ===")
        start_node = input("请输入起点编号（1-20，q退出）: ").strip()
        if start_node.lower() == 'q':
            break

        end_node = input("请输入终点编号（1-20）: ").strip()
        time_str = input("请输入出发时间（格式HH:MM，如07:30）: ").strip()

        # 输入验证
        if not (start_node.isdigit() and end_node.isdigit()):
            print("错误：节点编号必须为数字")
            continue

        depart_time = time_to_minutes(time_str)
        if depart_time is None:
            print("错误：时间格式无效")
            continue

        # 执行路径规划
        try:
            path, cost, worst, best, alt = road_net.find_min_robust_path(
                start_node, end_node, depart_time)

            print("\n=== 规划结果 ===")
            print(f"出发时间: {time_str}")
            print(f"路径: {' → '.join(path)}")
            print(f"鲁棒成本: {cost:.2f} 分钟")
            print(f"最坏情况耗时: {worst:.2f} 分钟")
            print(f"备选路径最佳耗时: {best:.2f} 分钟")
            print(f"备选路径: {' → '.join(alt) if alt else '无可用备选'}")

        except Exception as e:
            print(f"路径规划失败: {str(e)}")


# 性能测试函数
def performance_test():
    """执行10次随机查询测试性能"""
    road_net = EnhancedRoadNetwork(edges, l_base, u_base, congestion_factors, road_levels)
    start_time = time.time()
    
    for _ in range(10):
        # 生成随机查询参数...
        start = str(np.random.randint(1, 21))
        end = str(np.random.randint(1, 21))
        depart = np.random.randint(0, 1440)

        road_net.find_min_robust_path(start, end, depart)
    
    # 输出平均耗时
    print(f"平均查询时间: {(time.time() - start_time)/10:.2f}秒")

# 特定案例测试
def test_specific_case():
    """测试预设案例（节点1到20，7:00出发）"""
    road_net = EnhancedRoadNetwork(edges, l_base, u_base, congestion_factors, road_levels)
    path, cost, a, b, alt = road_net.find_min_robust_path('1', '20', 420)
    print(f"实际最坏耗时: {a}, 备选最佳: {b}")
    print(f"备选路径: {'→'.join(alt)}")

# 执行测试
performance_test()
test_specific_case()