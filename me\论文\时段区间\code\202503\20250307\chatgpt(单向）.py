import networkx as nx
import heapq
from collections import defaultdict

def yen_k_shortest_paths(graph, start, target, k=5):
    G = nx.DiGraph()
    for u in graph:
        for v, intervals in graph[u].items():
            min_t, _ = get_time_interval(intervals, 0)  # 取下界计算初始最短路径
            G.add_edge(u, v, weight=min_t)
    return list(nx.shortest_simple_paths(G, start, target, weight="weight"))[:k]

# 时间依赖阻抗查询（优化为二分查找）
def get_time_interval(edge_intervals, current_time):
    for start, end, min_t, max_t in edge_intervals:
        if start <= current_time < end:
            return min_t, max_t
    return float('inf'), float('inf')

# 计算路径 p 的上界总成本
def compute_upper_bound_cost(graph, path, start_time):
    current_time = start_time
    total_max = 0
    time_records = []
    for i in range(len(path)-1):
        u, v = path[i], path[i+1]
        edge_intervals = graph[u].get(v, [])
        min_t, max_t = get_time_interval(edge_intervals, current_time)
        total_max += max_t
        time_records.append((u, v, current_time, max_t))
        current_time += max_t  # 更新当前时间
    return total_max, time_records

# 生成修改图
def build_modified_graph(original_graph, time_records, start_time):
    modified_graph = defaultdict(dict)
    for u in original_graph:
        for v in original_graph[u]:
            if any(record[0] == u and record[1] == v for record in time_records):
                for record in time_records:
                    if record[0] == u and record[1] == v:
                        modified_graph[u][v] = record[3]
            else:
                min_t, _ = get_time_interval(original_graph[u][v], start_time)
                modified_graph[u][v] = min_t
    return modified_graph

# Dijkstra算法计算最短路径
def time_dependent_dijkstra(graph, start, target, start_time):
    heap = [(0, start, start_time, [start])]
    visited = {}

    while heap:
        cost, node, curr_time, path = heapq.heappop(heap)
        if node == target:
            return cost, path
        if node in visited and curr_time >= visited[node]:
            continue
        visited[node] = curr_time

        for neighbor in graph.get(node, {}):
            weight = graph[node][neighbor]
            new_time = curr_time + weight
            new_cost = cost + weight
            if neighbor not in visited or new_time < visited[neighbor]:
                heapq.heappush(heap, (new_cost, neighbor, new_time, path + [neighbor]))
    return float('inf'), []

# 计算鲁棒成本
def compute_robust_cost(graph, path, start, target, start_time):
    upper_cost, time_records = compute_upper_bound_cost(graph, path, start_time)
    modified_graph = build_modified_graph(graph, time_records, start_time)
    p_star_cost, p_star_path = time_dependent_dijkstra(modified_graph, start, target, start_time)
    return upper_cost - p_star_cost if p_star_cost != float('inf') else float('inf'), p_star_path

# 生成候选路径并选择最优
def find_min_robust_path(graph, start, target, start_time, k=5):
    candidate_paths = yen_k_shortest_paths(graph, start, target, k)
    min_cost = float('inf')
    best_path = None
    best_p_star_path = None
    for path in candidate_paths:
        robust_cost, p_star_path = compute_robust_cost(graph, path, start, target, start_time)
        print(f"路径 {path} 鲁棒成本: {robust_cost}, p* 路径: {p_star_path}")
        if robust_cost < min_cost:
            min_cost = robust_cost
            best_path = path
            best_p_star_path = p_star_path
    return best_path, min_cost, best_p_star_path

# 示例
graph = {
    'A': {
        'B': [
            [0, 480, 20, 30],
            [480, 600, 30, 50],
            [600, 1440, 20, 40]
        ],
        'C': [[0, 1440, 6, 15]]
    },
    'B': {
        'C': [
            [0, 540, 15, 25],
            [540, 1440, 20, 30]
        ],
        'D': [[0, 1440, 8, 10]]
    },
    'C': {'D': [[0, 1440, 7, 20]]},
    'D': {}
}

best_path, min_cost, best_p_star_path = find_min_robust_path(graph, 'A', 'D', start_time=475)
print(f"最优路径: {best_path}, 最小鲁棒成本: {min_cost}, p* 路径: {best_p_star_path}")
