import heapq
from collections import defaultdict, deque
import multiprocessing
import time


# 定义路网类
class RoadNetwork:
    def __init__(self, network_data):
        self.graph = defaultdict(list)
        self._initialize_graph(network_data)
        self.robust_cost_cache = {}  # 缓存鲁棒成本计算结果
        # 添加全局计数器
        self.find_min_robust_iterations = 0  # 主循环迭代次数
        self.dijkstra_iterations = 0  # Dijkstra总迭代次数
        self.robust_cost_iterations = 0  # 鲁棒成本计算迭代次数

    def _initialize_graph(self, network_data):
        # 从输入数据初始化图
        for source, neighbors in network_data.items():
            for target, time_ranges in neighbors.items():
                self.graph[source].append((target, time_ranges))

    def get_impedance(self, impedance_ranges, time):
        # 根据时间选择合适的阻抗
        for start, end, low, high in impedance_ranges:
            if start <= time < end:
                return low, high
        return None

    def dijkstra(self, start, end, time, lower_bound=True):
        # Dijkstra 算法，用于计算最短路径
        priority_queue = [(0, start, time)]
        dist = {start: 0}
        prev = {start: None}
        iterations = 0  # 本次Dijkstra的迭代次数

        while priority_queue:
            current_dist, current_node, current_time = heapq.heappop(priority_queue)

            if current_node == end:
                path = []
                while current_node is not None:
                    path.append(current_node)
                    current_node = prev[current_node]
                path.reverse()
                # 累加到全局计数器
                self.dijkstra_iterations += iterations
                return path, current_dist

            if current_dist > dist.get(current_node, float('inf')):
                continue

            for neighbor, impedance_ranges in self.graph[current_node]:
                impedance = self.get_impedance(impedance_ranges, current_time)
                if impedance is None:
                    continue
                if lower_bound:
                    cost = impedance[0]
                else:
                    cost = impedance[1]

                new_dist = current_dist + cost
                new_time = current_time + cost
                if new_time >= 1440:
                    new_time %= 1440

                if new_dist < dist.get(neighbor, float('inf')):
                    dist[neighbor] = new_dist
                    prev[neighbor] = current_node
                    heapq.heappush(priority_queue, (new_dist, neighbor, new_time))

        self.dijkstra_iterations += iterations
        return None, float('inf')

    def calculate_robust_cost(self, path, start_time):
        key = (tuple(path), start_time)
        if key in self.robust_cost_cache:
            return self.robust_cost_cache[key]

        # 计算路径的鲁棒成本
        # 计算原路径最坏时间
        a = 0
        current_time = start_time
        for i in range(len(path) - 1):
            self.robust_cost_iterations += 1  # 统计鲁棒成本计算迭代
            u = path[i]
            v = path[i + 1]
            for neighbor, impedance_ranges in self.graph[u]:
                if neighbor == v:
                    impedance = self.get_impedance(impedance_ranges, current_time)
                    if impedance is None:
                        continue
                    a += impedance[1]
                    current_time += impedance[1]
                    if current_time >= 1440:
                        current_time %= 1440
                    break

        # 计算备选路径最佳时间和路径
        remaining_graph = defaultdict(list)
        for u in self.graph:
            for v, impedance_ranges in self.graph[u]:
                if (u, v) not in zip(path[:-1], path[1:]):
                    remaining_graph[u].append((v, impedance_ranges))

        remaining_network = RoadNetwork({})
        remaining_network.graph = remaining_graph
        alternative_path, b = remaining_network.dijkstra(path[0], path[-1], start_time, lower_bound=True)

        robust_cost = a - b
        result = (robust_cost, a, b, alternative_path)
        self.robust_cost_cache[key] = result
        return result

    def find_min_robust_path(self, start, end, start_time):
        # 找到最小鲁棒成本的路径
        all_paths = []
        priority_queue = [(0, deque([start]), start_time)]
        min_robust_cost = float('inf')
        self.find_min_robust_iterations = 0  # 重置计数器

        while priority_queue:
            self.find_min_robust_iterations += 1  # 主循环计数器
            current_dist, current_path, current_time = heapq.heappop(priority_queue)
            current_node = current_path[-1]

            # 剪枝：如果当前路径成本已超过最小鲁棒成本，跳过
            if current_dist >= min_robust_cost:
                continue

            if current_node == end:
                path_list = list(current_path)
                all_paths.append(path_list)
                continue

            for neighbor, impedance_ranges in self.graph[current_node]:
                if neighbor not in current_path:
                    impedance = self.get_impedance(impedance_ranges, current_time)
                    if impedance is None:
                        continue
                    new_dist = current_dist + impedance[0]
                    new_time = current_time + impedance[0]
                    if new_time >= 1440:
                        new_time %= 1440
                    new_path = current_path.copy()
                    new_path.append(neighbor)
                    heapq.heappush(priority_queue, (new_dist, new_path, new_time))

        min_robust_path = None
        worst_case_time = 0
        best_case_time = 0
        alternative_path = None

        # 并行计算鲁棒成本
        pool = multiprocessing.Pool()
        results = pool.map(calculate_robust_cost_wrapper, [(self, path, start_time) for path in all_paths])
        pool.close()
        pool.join()

        for path, (robust_cost, a, b, alt_path) in zip(all_paths, results):
            if robust_cost < min_robust_cost:
                min_robust_cost = robust_cost
                min_robust_path = path
                worst_case_time = a
                best_case_time = b
                alternative_path = alt_path

        return min_robust_path, min_robust_cost, worst_case_time, best_case_time, alternative_path


def calculate_robust_cost_wrapper(args):
    network, path, start_time = args
    return network.calculate_robust_cost(path, start_time)


# 示例使用
network_data = {
"1": {
"2": [[300, 420, 2, 4], [420, 540, 9, 14], [540, 660, 3, 6]],
"3": [[300, 420, 1, 5], [420, 540, 7, 13], [540, 660, 2, 5]],
"4": [[300, 420, 3, 5], [420, 540, 6, 12], [540, 660, 4, 7]],
"5": [[300, 420, 2, 4], [420, 540, 10, 15], [540, 660, 3, 6]]
},
"2": {
"1": [[300, 420, 2, 4], [420, 540, 9, 14], [540, 660, 3, 6]],
"3": [[300, 420, 2, 5], [420, 540, 6, 11], [540, 660, 3, 7]],
"6": [[300, 420, 4, 5], [420, 540, 12, 15], [540, 660, 5, 7]],
"7": [[300, 420, 3, 5], [420, 540, 10, 14], [540, 660, 2, 6]]
},
"3": {
"1": [[300, 420, 1, 5], [420, 540, 7, 13], [540, 660, 2, 5]],
"2": [[300, 420, 2, 5], [420, 540, 6, 11], [540, 660, 3, 7]],
"4": [[300, 420, 3, 5], [420, 540, 11, 15], [540, 660, 2, 5]],
"7": [[300, 420, 4, 5], [420, 540, 8, 12], [540, 660, 5, 7]],
"8": [[300, 420, 2, 4], [420, 540, 10, 15], [540, 660, 3, 6]]
},
"4": {
"1": [[300, 420, 3, 5], [420, 540, 6, 12], [540, 660, 4, 7]],
"3": [[300, 420, 3, 5], [420, 540, 11, 15], [540, 660, 2, 5]],
"5": [[300, 420, 3, 5], [420, 540, 9, 14], [540, 660, 3, 6]],
"8": [[300, 420, 1, 4], [420, 540, 7, 13], [540, 660, 5, 7]],
"9": [[300, 420, 2, 5], [420, 540, 6, 12], [540, 660, 2, 5]]
},
"5": {
"1": [[300, 420, 2, 4], [420, 540, 10, 15], [540, 660, 3, 6]],
"4": [[300, 420, 3, 5], [420, 540, 9, 14], [540, 660, 3, 6]],
"9": [[300, 420, 1, 5], [420, 540, 7, 12], [540, 660, 2, 5]],
"10": [[300, 420, 2, 4], [420, 540, 9, 15], [540, 660, 3, 7]]
},
"6": {
"2": [[300, 420, 4, 5], [420, 540, 12, 15], [540, 660, 5, 7]],
"7": [[300, 420, 3, 5], [420, 540, 8, 14], [540, 660, 2, 6]],
"11": [[300, 420, 1, 3], [420, 540, 6, 12], [540, 660, 3, 5]]
},
"7": {
"2": [[300, 420, 3, 5], [420, 540, 10, 14], [540, 660, 2, 6]],
"3": [[300, 420, 4, 5], [420, 540, 8, 12], [540, 660, 5, 7]],
"6": [[300, 420, 3, 5], [420, 540, 8, 14], [540, 660, 2, 6]],
"8": [[300, 420, 1, 4], [420, 540, 9, 14], [540, 660, 3, 6]],
"11": [[300, 420, 2, 4], [420, 540, 8, 14], [540, 660, 2, 5]],
"12": [[300, 420, 1, 5], [420, 540, 6, 11], [540, 660, 3, 6]]
},
"8": {
"3": [[300, 420, 2, 4], [420, 540, 10, 15], [540, 660, 3, 6]],
"4": [[300, 420, 1, 4], [420, 540, 7, 13], [540, 660, 5, 7]],
"7": [[300, 420, 1, 4], [420, 540, 9, 14], [540, 660, 3, 6]],
"9": [[300, 420, 2, 5], [420, 540, 8, 12], [540, 660, 5, 7]],
"12": [[300, 420, 3, 5], [420, 540, 11, 15], [540, 660, 2, 6]],
"13": [[300, 420, 1, 3], [420, 540, 6, 11], [540, 660, 3, 5]]
},
"9": {
"4": [[300, 420, 2, 5], [420, 540, 6, 12], [540, 660, 2, 5]],
"5": [[300, 420, 1, 5], [420, 540, 7, 12], [540, 660, 2, 5]],
"8": [[300, 420, 2, 5], [420, 540, 8, 12], [540, 660, 5, 7]],
"10": [[300, 420, 2, 4], [420, 540, 10, 14], [540, 660, 2, 5]],
"13": [[300, 420, 3, 5], [420, 540, 11, 15], [540, 660, 5, 7]],
"14": [[300, 420, 1, 3], [420, 540, 6, 11], [540, 660, 3, 6]]
},
"10": {
"5": [[300, 420, 2, 4], [420, 540, 9, 15], [540, 660, 3, 7]],
"9": [[300, 420, 2, 4], [420, 540, 10, 14], [540, 660, 2, 5]],
"14": [[300, 420, 1, 4], [420, 540, 10, 14], [540, 660, 3, 6]],
"15": [[300, 420, 2, 5], [420, 540, 8, 12], [540, 660, 5, 7]]
},
"11": {
"6": [[300, 420, 1, 3], [420, 540, 6, 12], [540, 660, 3, 5]],
"7": [[300, 420, 2, 4], [420, 540, 8, 14], [540, 660, 2, 5]],
"12": [[300, 420, 3, 5], [420, 540, 10, 15], [540, 660, 4, 7]],
"16": [[300, 420, 1, 4], [420, 540, 7, 13], [540, 660, 2, 5]],
"17": [[300, 420, 2, 5], [420, 540, 8, 12], [540, 660, 3, 6]]
},
"12": {
"7": [[300, 420, 1, 5], [420, 540, 6, 11], [540, 660, 3, 6]],
"8": [[300, 420, 3, 5], [420, 540, 11, 15], [540, 660, 2, 6]],
"11": [[300, 420, 3, 5], [420, 540, 10, 15], [540, 660, 4, 7]],
"13": [[300, 420, 3, 5], [420, 540, 8, 14], [540, 660, 5, 7]],
"17": [[300, 420, 1, 3], [420, 540, 6, 11], [540, 660, 2, 5]],
"18": [[300, 420, 2, 4], [420, 540, 9, 15], [540, 660, 3, 6]]
},
"13": {
"8": [[300, 420, 1, 3], [420, 540, 6, 11], [540, 660, 3, 5]],
"9": [[300, 420, 3, 5], [420, 540, 11, 15], [540, 660, 5, 7]],
"12": [[300, 420, 3, 5], [420, 540, 8, 14], [540, 660, 5, 7]],
"14": [[300, 420, 1, 4], [420, 540, 7, 13], [540, 660, 2, 5]],
"18": [[300, 420, 2, 4], [420, 540, 9, 15], [540, 660, 3, 6]],
"19": [[300, 420, 3, 5], [420, 540, 9, 15], [540, 660, 5, 7]]
},
"14": {
"9": [[300, 420, 1, 3], [420, 540, 6, 11], [540, 660, 3, 6]],
"10": [[300, 420, 1, 4], [420, 540, 10, 14], [540, 660, 3, 6]],
"13": [[300, 420, 1, 4], [420, 540, 7, 13], [540, 660, 2, 5]],
"15": [[300, 420, 2, 5], [420, 540, 10, 13], [540, 660, 2, 5]],
"19": [[300, 420, 3, 5], [420, 540, 6, 11], [540, 660, 3, 6]]
},
"15": {
"10": [[300, 420, 2, 5], [420, 540, 8, 12], [540, 660, 5, 7]],
"14": [[300, 420, 2, 5], [420, 540, 10, 13], [540, 660, 2, 5]],
"19": [[300, 420, 3, 5], [420, 540, 7, 12], [540, 660, 4, 7]]
},
"16": {
"11": [[300, 420, 1, 4], [420, 540, 7, 13], [540, 660, 2, 5]],
"17": [[300, 420, 3, 5], [420, 540, 8, 12], [540, 660, 3, 6]],
"20": [[300, 420, 1, 3], [420, 540, 6, 11], [540, 660, 4, 7]]
},
"17": {
"11": [[300, 420, 2, 5], [420, 540, 8, 12], [540, 660, 3, 6]],
"12": [[300, 420, 1, 3], [420, 540, 6, 11], [540, 660, 2, 5]],
"16": [[300, 420, 3, 5], [420, 540, 8, 12], [540, 660, 3, 6]],
"18": [[300, 420, 2, 4], [420, 540, 7, 15], [540, 660, 2, 6]],
"20": [[300, 420, 1, 4], [420, 540, 6, 12], [540, 660, 3, 7]]
},
"18": {
"12": [[300, 420, 2, 4], [420, 540, 9, 15], [540, 660, 3, 6]],
"13": [[300, 420, 2, 4], [420, 540, 9, 15], [540, 660, 3, 6]],
"17": [[300, 420, 2, 4], [420, 540, 7, 15], [540, 660, 2, 6]],
"19": [[300, 420, 2, 5], [420, 540, 9, 10], [540, 660, 3, 7]],
"20": [[300, 420, 2, 5], [420, 540, 6, 15], [540, 660, 2, 7]]
},
"19": {
"13": [[300, 420, 3, 5], [420, 540, 9, 15], [540, 660, 5, 7]],
"14": [[300, 420, 3, 5], [420, 540, 6, 11], [540, 660, 3, 6]],
"15": [[300, 420, 3, 5], [420, 540, 7, 12], [540, 660, 4, 7]],
"18": [[300, 420, 2, 5], [420, 540, 9, 10], [540, 660, 3, 7]],
"20": [[300, 420, 1, 5], [420, 540, 8, 13], [540, 660, 2, 7]]
},
"20": {
"16": [[300, 420, 1, 3], [420, 540, 6, 11], [540, 660, 4, 7]],
"17": [[300, 420, 1, 4], [420, 540, 6, 12], [540, 660, 3, 7]],
"18": [[300, 420, 2, 5], [420, 540, 6, 15], [540, 660, 2, 7]],
"19": [[300, 420, 1, 5], [420, 540, 8, 13], [540, 660, 2, 7]]
}
}

if __name__ == '__main__':
    start_time_total = time.time()
    network = RoadNetwork(network_data)
    start = '9'
    end = '16'
    start_time = 410

    min_robust_path, min_robust_cost, worst_case_time, best_case_time, alternative_path = network.find_min_robust_path(
        start, end, start_time)

    # 输出统计结果
    print("最小鲁棒成本的路径:", min_robust_path)
    print("最小鲁棒成本:", min_robust_cost)
    print("原路径最坏时间:", worst_case_time)
    print("备选路径最佳时间:", best_case_time)
    print("备选路径:", alternative_path)

    print("\n--- 迭代次数统计 ---")
    print(f"主算法循环迭代次数: {network.find_min_robust_iterations} 次")
    print(f"Dijkstra算法总迭代次数: {network.dijkstra_iterations} 次")
    print(f"鲁棒成本计算迭代次数: {network.robust_cost_iterations} 次")
    print(f"总运行时间: {time.time() - start_time_total:.2f} 秒")
