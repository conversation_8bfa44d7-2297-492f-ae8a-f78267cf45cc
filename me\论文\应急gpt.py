import pyomo.environ as pyo
from pyomo.opt import SolverFactory
import networkx as nx

# 节点和路段（简化示例）
nodes = list(range(1, 6))  # 节点1~5
edges = {
    (1, 2): (3, 6),
    (2, 3): (2, 4),
    (3, 4): (1, 3),
    (4, 5): (2, 5),
    (1, 3): (4, 7),
    (2, 4): (3, 5),
}

# 数据
disaster_nodes = [3, 4, 5]
supply_nodes = [1]
vehicles = [1, 2]
vehicle_capacity = 1500
max_travel_time = 45
demand = {3: 200, 4: 300, 5: 500}

# 区间阻抗
tl = {e: edges[e][0] for e in edges}
tu = {e: edges[e][1] for e in edges}

def build_master_model(wk=None):
    model = pyo.ConcreteModel()
    model.N = pyo.Set(initialize=nodes)
    model.K = pyo.Set(initialize=vehicles)
    model.E = pyo.Set(initialize=edges.keys(), dimen=2)

    model.x = pyo.Var(model.K, model.E, domain=pyo.Binary)

    def obj_rule(m):
        return sum(tu[i, j] * m.x[k, (i, j)] for (i, j) in model.E for k in model.K)
    model.obj = pyo.Objective(rule=obj_rule, sense=pyo.minimize)

    # 约束示例：每个灾点只服务一次
    def serve_once_rule(m, j):
        if j not in disaster_nodes:
            return pyo.Constraint.Skip
        return sum(m.x[k, (i, j)]
                   for (i, j) in m.E if j in disaster_nodes and (i, j) in m.E for k in m.K) == 1
    model.serve_once = pyo.Constraint(disaster_nodes, rule=serve_once_rule)

    # 其他约束略（可扩展到论文中所有约束）

    return model

def build_sub_model(x_vals):
    model = pyo.ConcreteModel()
    model.N = pyo.Set(initialize=nodes)
    model.K = pyo.Set(initialize=vehicles)
    model.E = pyo.Set(initialize=edges.keys(), dimen=2)

    model.w = pyo.Var(model.K, model.E, domain=pyo.Binary)

    def obj_rule(m):
        return sum((tu[i, j] if x_vals[k, (i, j)] else tl[i, j]) * m.w[k, (i, j)]
                   for (i, j) in model.E for k in model.K)
    model.obj = pyo.Objective(rule=obj_rule, sense=pyo.minimize)

    # 示例约束：同灾点只服务一次
    def serve_once_rule(m, j):
        if j not in disaster_nodes:
            return pyo.Constraint.Skip
        return sum(m.w[k, (i, j)]
                   for (i, j) in m.E if j in disaster_nodes and (i, j) in m.E for k in m.K) == 1
    model.serve_once = pyo.Constraint(disaster_nodes, rule=serve_once_rule)

    return model

def benders_decomposition(max_iter=10):
    best_robust_cost = float('inf')
    best_x = None

    for iteration in range(max_iter):
        print(f"--- Iteration {iteration + 1} ---")

        master = build_master_model()
        solver = SolverFactory('gurobi')
        solver.solve(master, tee=False)

        x_vals = {(k, e): pyo.value(master.x[k, e]) > 0.5 for k in vehicles for e in edges}
        cost_upper = sum(tu[e] for (k, e) in x_vals if x_vals[(k, e)])

        # 子问题求 w
        sub = build_sub_model(x_vals)
        solver.solve(sub, tee=False)

        cost_lower = pyo.value(sub.obj)

        robust_cost = cost_upper - cost_lower
        print(f"Upper: {cost_upper}, Lower: {cost_lower}, Robust Cost: {robust_cost}")

        if robust_cost < best_robust_cost:
            best_robust_cost = robust_cost
            best_x = x_vals

    print("Best robust cost:", best_robust_cost)
    return best_x

if __name__ == "__main__":
    best_plan = benders_decomposition()
    print("Best dispatching plan (x_k_ij):")
    for (k, (i, j)), val in best_plan.items():
        if val:
            print(f"Vehicle {k}: {i} → {j}")
