{"cells": [{"cell_type": "code", "execution_count": null, "id": "f1986961", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T03:12:32.923925Z", "start_time": "2025-07-21T03:12:31.967485Z"}}, "outputs": [], "source": ["import json\n", "from urllib import request\n", "import time\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import csv\n", "\n", "# --- 配置区域 ---\n", "# 请在此处填入您自己的高德Web服务API Key\n", "AMAP_KEY = \"7fc11539ead7b69f460073d205714b4b\"  \n", "\n", "# 起终点经纬度坐标文件路径 (格式: 经度1\\t纬度1\\t经度2\\t纬度2)\n", "# 原始文件路径: 'D:/Desktop/高德数据采集/脚本节点.txt'\n", "OD_FILE_PATH = 'D:/Desktop/高德数据采集/脚本节点.txt'\n", "\n", "# 数据输出文件路径\n", "OUTPUT_CSV_PATH = 'speed-result.csv'\n", "\n", "# 采集总次数\n", "MAX_ITERATIONS = 24\n", "\n", "# 每次采集的时间间隔（秒）\n", "SLEEP_INTERVAL_S = 3600\n", "\n", "# --- <PERSON><PERSON><PERSON><PERSON><PERSON> 中文显示设置 ---\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# --- 读取OD坐标点 ---\n", "try:\n", "    OD = open(OD_FILE_PATH).read().splitlines()\n", "    print(f\"成功读取 {len(OD)} 组OD坐标。\")\n", "except FileNotFoundError:\n", "    print(f\"错误: OD坐标文件 '{OD_FILE_PATH}' 未找到。请检查文件路径。\")\n", "    OD = [] # 初始化为空列表以避免后续错误"]}, {"cell_type": "markdown", "id": "8708836d", "metadata": {}, "source": ["抓取数据并保存"]}, {"cell_type": "code", "execution_count": null, "id": "c520bafc", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T02:39:52.517454Z", "start_time": "2025-07-21T02:39:52.499009Z"}}, "outputs": [], "source": ["url_template = f'https://restapi.amap.com/v5/direction/driving?key={AMAP_KEY}&origin={{0}},{{1}}&destination={{2}},{{3}}&show_fields=cost'\n", "\n", "# 检查OD列表是否为空\n", "if not OD:\n", "    print(\"OD坐标列表为空，跳过数据采集。请检查OD_FILE_PATH配置或文件内容。\")\n", "else:\n", "    # 打开CSV文件用于存放数据\n", "    with open(OUTPUT_CSV_PATH, 'w', newline='', encoding='utf-8') as output:\n", "        writer = csv.writer(output)\n", "        # 写入CSV文件的头部，增加了'Time'列\n", "        writer.writerow(['Time', 'Distance (m)', 'Duration (s)', 'Speed (km/h)'])\n", "        \n", "        iteration_count = 0\n", "        while iteration_count < MAX_ITERATIONS:\n", "            print(f\"--- 开始第 {iteration_count + 1}/{MAX_ITERATIONS} 轮数据采集 ---\")\n", "            # 在每一轮采集开始时记录一个统一的时间戳\n", "            current_timestamp = time.strftime('%H:%M:%S') \n", "            for i in OD:\n", "                url = None  # 在循环开始时初始化url，防止NameError\n", "                try:\n", "                    # 使用.split()可以分割一个或多个制表符或空格，更具兼容性\n", "                    x1, y1, x2, y2 = i.split()\n", "                    url = url_template.format(x1, y1, x2, y2)\n", "                    \n", "                    html = request.urlopen(url, timeout=15).read()\n", "                    js = json.loads(html)\n", "                    \n", "                    # 检查API调用是否成功并返回了路径信息\n", "                    if js.get('status') == '1' and js.get('route') and js['route'].get('paths'):\n", "                        d = js['route']['paths'][0].get('distance', 0)\n", "                        c = js['route']['paths'][0].get('cost', {}).get('duration', 0)\n", "                        v = 0\n", "                        if float(c) > 0:\n", "                            v = round((float(d) / float(c)) * 3.6, 2)  # 计算速度（千米/小时）并进行单位换算\n", "                        writer.writerow([current_timestamp, d, c, v])  # 写入带时间戳的数据到CSV文件\n", "                    else:\n", "                        # 如果API没有返回有效路径，记录错误信息\n", "                        info = js.get('info', '未知API错误')\n", "                        infocode = js.get('infocode', 'N/A')\n", "                        error_message = f\"API错误: {info} (代码: {infocode})\"\n", "                        print(f\"处理路段 '{i}' 时发生错误: {error_message}\")\n", "                        writer.writerow([current_timestamp, 'Error', info, url])\n", "                        \n", "                except ValueError:\n", "                    # 捕获因坐标文件行格式错误导致的 .split() 失败\n", "                    error_message = \"OD坐标文件格式错误，无法正确分割\"\n", "                    print(f\"处理路段 '{i}' 时发生错误: {error_message}\")\n", "                    writer.writerow([current_timestamp, 'Error', error_message, i])\n", "                except Exception as e:\n", "                    # 捕获其他所有异常，如网络问题\n", "                    print(f\"处理路段 '{i}' 时发生未知异常: {repr(e)}\")\n", "                    log_info = url if url else f\"有问题的行: {i}\"\n", "                    writer.writerow([current_timestamp, 'Error', repr(e), log_info]) \n", "                    \n", "            iteration_count += 1\n", "            print(f\"第 {iteration_count} 轮采集完成。\")\n", "            if iteration_count < MAX_ITERATIONS:\n", "                 print(f\"等待 {SLEEP_INTERVAL_S} 秒后开始下一轮采集...\")\n", "                 time.sleep(SLEEP_INTERVAL_S) # 使用配置的时间间隔\n", "    print(\"--- 所有数据采集完成 ---\")"]}, {"cell_type": "markdown", "id": "ecc46fdc", "metadata": {}, "source": ["处理csv文件，将其重新排序（等上面的代码运行完成之后再运行下面的代码）"]}, {"cell_type": "code", "execution_count": null, "id": "8dcf94e4", "metadata": {}, "outputs": [], "source": ["# 读取CSV文件\n", "try:\n", "    data = pd.read_csv(OUTPUT_CSV_PATH)\n", "\n", "    # 使用pandas进行排序\n", "    data_sorted = data.sort_values(by='Distance (m)')\n", "\n", "    # 写入排序后的数据到CSV文件\n", "    data_sorted.to_csv(OUTPUT_CSV_PATH, index=False)\n", "    print(f\"文件 '{OUTPUT_CSV_PATH}' 已成功排序并保存。\")\n", "except FileNotFoundError:\n", "    print(f\"错误: 文件 '{OUTPUT_CSV_PATH}' 未找到。请先运行数据采集步骤。\")\n", "except Exception as e:\n", "    print(f\"处理文件时发生错误: {e}\")"]}, {"cell_type": "markdown", "id": "c71d176b", "metadata": {}, "source": ["绘制时间-速度折线图（需要自己在csv文件中把time字段填充好，并且是时：分：秒格式）"]}, {"cell_type": "code", "execution_count": null, "id": "b297cd89", "metadata": {}, "outputs": [], "source": ["try:\n", "    # 加载CSV文件\n", "    df = pd.read_csv(OUTPUT_CSV_PATH)\n", "\n", "    # 将'Time'列转换为时间对象 (注意：列名已从'time'改为'Time')\n", "    df['Time'] = pd.to_datetime(df['Time'].astype(str), format='%H:%M:%S').dt.time\n", "\n", "    # 定义一个函数，将时间转换为一天开始以来的秒数\n", "    def time_to_seconds(t):\n", "        return t.hour * 3600 + t.minute * 60 + t.second\n", "\n", "    # 应用函数到'Time'列，创建一个'time_since_start'列\n", "    df['time_since_start'] = df['Time'].apply(lambda t: time_to_seconds(t))\n", "\n", "    # 定义一个函数，将秒数格式化为小时和分钟\n", "    def time_to_hours_minutes(s):\n", "        return f\"{int(s // 3600):02d}:{int((s % 3600) // 60):02d}\"\n", "\n", "    # 创建图表\n", "    plt.figure(figsize=(12, 7))\n", "\n", "    # 获取唯一的距离值以进行绘图\n", "    unique_distances = df['Distance (m)'].unique()\n", "\n", "    # --- 请根据实际情况修改图例 ---\n", "    # 自动为每个路段创建图例，您也可以像原来一样手动指定\n", "    # distance_to_legend = { unique_distances[0]: '路段1', ... }\n", "\n", "    # 绘制每个组的数据\n", "    for i, distance in enumerate(unique_distances):\n", "        subset = df[df['Distance (m)'] == distance]\n", "        legend_name = f'路段{i+1} (长度: {distance} m)'\n", "        plt.plot(subset['time_since_start'], subset['Speed (km/h)'], marker='o', linestyle='-', label=legend_name)\n", "\n", "    # 设置x轴的定制刻度标签，只显示小时和分钟\n", "    xticks = plt.xticks()[0]\n", "    # 过滤掉负数刻度，避免绘图错误\n", "    plt.xticks(xticks[xticks>=0], labels=[time_to_hours_minutes(x) for x in xticks if x >= 0])\n", "\n", "    # 完成图表\n", "    plt.title('各路段速度随时间变化图')\n", "    plt.xlabel('采集时间 (时:分)')\n", "    plt.ylabel('速度 (km/h)')\n", "    plt.legend()\n", "    plt.grid(True, linestyle='--')\n", "    plt.xticks(rotation=45) # 旋转x轴标签以防重叠\n", "    plt.tight_layout() # 调整布局\n", "    plt.show()\n", "\n", "except FileNotFoundError:\n", "    print(f\"错误: 文件 '{OUTPUT_CSV_PATH}' 未找到。请先运行数据采集和排序步骤。\")\n", "except KeyError:\n", "    print(\"错误: CSV文件中未找到 'Time' 列。请确保采集脚本已正确生成带时间戳的数据。\")\n", "except Exception as e:\n", "    print(f\"绘制图表时发生未知错误: {e}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "vp": {"vp_config_version": "1.0.0", "vp_menu_width": 273, "vp_note_display": false, "vp_note_width": 0, "vp_position": {"width": 278}, "vp_section_display": false, "vp_signature": "VisualPython"}}, "nbformat": 4, "nbformat_minor": 5}