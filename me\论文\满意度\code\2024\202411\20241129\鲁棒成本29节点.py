
# 失败


import networkx as nx

def robust_shortest_path(G, r, t):
    # 初始化最佳路径及其成本
    best_path = None
    best_cost = float('inf')

    # 遍历所有简单路径
    for path in nx.all_simple_paths(G, source=r, target=t):
        cost = robust_cost(G, path, r, t)
        if cost < best_cost:
            best_cost = cost
            best_path = path

    return best_path if best_path is not None else "无可行路径"

def robust_cost(G, path, r, t):
    # 计算路径的鲁棒成本
    cost = sum(G[path[i]][path[i + 1]]['upper_bound'] for i in range(len(path) - 1))

    # 移除路径对应的边并计算替代最短路径成本
    G_copy = G.copy()
    G_copy.remove_edges_from([(path[i], path[i + 1]) for i in range(len(path) - 1)])

    try:
        shortest_path_cost = nx.dijkstra_path_length(G_copy, r, t, weight='lower_bound')
        cost -= shortest_path_cost
    except nx.NetworkXNoPath:
        cost = float('inf')  # 如果无连通路径，设置为无穷大

    return cost

# 创建有向图
G = nx.DiGraph()
edges = [
    (1, 2, 6, 10), (1, 3, 6, 8), (2, 3, 6, 10), (1, 4, 5, 9), (3, 4, 6, 10), (1, 5, 3, 5), (4, 5, 1, 4),
    (2, 6, 6, 10), (3, 6, 6, 10), (3, 7, 6, 8), (6, 7, 6, 10), (3, 8, 6, 10), (4, 8, 6, 8), (7, 8, 6, 10),
    (4, 9, 3, 9), (8, 9, 6, 10), (4, 10, 6, 10), (5, 10, 3, 10), (9, 10, 6, 10), (6, 11, 6, 10), (7, 11, 6, 10),
    (7, 12, 6, 8), (11, 12, 6, 10), (7, 13, 6, 10), (8, 13, 6, 8), (12, 13, 6, 10), (8, 14, 6, 10), (9, 14, 6, 8),
    (13, 14, 6, 10), (9, 15, 3, 9), (10, 15, 6, 12), (14, 15, 6, 10), (10, 16, 3, 10), (15, 16, 6, 10),
    (11, 17, 6, 10),
    (12, 17, 6, 10), (12, 18, 6, 8), (13, 18, 6, 8), (17, 18, 6, 10), (13, 19, 6, 10), (14, 19, 6, 8),
    (18, 19, 6, 10),
    (14, 20, 6, 10), (15, 20, 4, 9), (19, 20, 6, 10), (15, 21, 6, 10), (16, 21, 3, 10), (20, 21, 6, 10),
    (17, 22, 6, 10),
    (18, 22, 6, 10), (18, 23, 6, 8), (19, 23, 6, 8), (22, 23, 6, 10), (19, 24, 6, 8), (20, 24, 5, 10),
    (23, 24, 6, 10),
    (20, 25, 6, 10), (21, 25, 3, 11), (24, 25, 6, 10), (22, 26, 6, 10), (23, 26, 6, 10), (23, 27, 6, 8),
    (24, 27, 4, 9),
    (26, 27, 2, 3), (24, 28, 6, 10), (25, 28, 3, 10), (27, 28, 6, 10), (26, 29, 5, 13), (27, 29, 2, 4),
    (28, 29, 3, 13),
    (2, 1, 6, 10), (3, 1, 6, 8), (3, 2, 6, 10), (4, 1, 5, 9), (4, 3, 6, 10), (5, 1, 3, 5), (5, 4, 1, 4),
    (6, 2, 6, 10), (6, 3, 6, 10), (7, 3, 6, 8), (7, 6, 6, 10), (8, 3, 6, 10), (8, 4, 6, 8), (8, 7, 6, 10),
    (9, 4, 3, 9), (9, 8, 6, 10), (10, 4, 6, 10), (10, 5, 3, 10), (10, 9, 6, 10), (11, 6, 6, 10), (11, 7, 6, 10),
    (12, 7, 6, 8), (12, 11, 6, 10), (13, 7, 6, 10), (13, 8, 6, 8), (13, 12, 6, 10), (14, 8, 6, 10), (14, 9, 6, 8),
    (14, 13, 6, 10), (15, 9, 3, 9), (15, 10, 6, 12), (15, 14, 6, 10), (16, 10, 3, 10), (16, 15, 6, 10),
    (17, 11, 6, 10),
    (17, 12, 6, 10), (18, 12, 6, 8), (18, 13, 6, 8), (18, 17, 6, 10), (19, 13, 6, 10), (19, 14, 6, 8),
    (19, 18, 6, 10),
    (20, 14, 6, 10), (20, 15, 4, 9), (20, 19, 6, 10), (21, 15, 6, 10), (21, 16, 3, 10), (21, 20, 6, 10),
    (22, 17, 6, 10),
    (22, 18, 6, 10), (23, 18, 6, 8), (23, 19, 6, 8), (23, 22, 6, 10), (24, 19, 6, 8), (24, 20, 5, 10),
    (24, 23, 6, 10),
    (25, 20, 6, 10), (25, 21, 3, 11), (25, 24, 6, 10), (26, 22, 6, 10), (26, 23, 6, 10), (27, 23, 6, 8),
    (27, 24, 4, 9),
    (27, 26, 2, 3), (28, 24, 6, 10), (28, 25, 3, 10), (28, 27, 6, 10), (29, 26, 5, 13), (29, 27, 2, 4),
    (29, 28, 3, 13)
]
for u, v, lb, ub in edges:
    G.add_edge(u, v, lower_bound=lb, upper_bound=ub)

# 起点和终点
r, t = 1, 29

# 计算鲁棒最短路径
robust_path = robust_shortest_path(G, r, t)
print("鲁棒最短路径:", robust_path)
