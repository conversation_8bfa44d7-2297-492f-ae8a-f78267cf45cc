import heapq


def get_impedance(u, v, t, is_upper):
    """
    返回路段(u, v)在时间t的阻抗（上界或下界）
    假设已预定义每个时段的上下界，例如：
    - c_lower = 基础阻抗
    - c_upper = 基础阻抗 * 1.5
    """
    # 示例：简单时间依赖阻抗模型
    hour = t % 24
    if 0 <= hour < 6:
        return edges[(u, v)]['night_upper'] if is_upper else edges[(u, v)]['night_lower']
    elif 6 <= hour < 18:
        return edges[(u, v)]['day_upper'] if is_upper else edges[(u, v)]['day_lower']
    else:
        return edges[(u, v)]['evening_upper'] if is_upper else edges[(u, v)]['evening_lower']


def time_dependent_dijkstra(nodes, edges, start, end, start_time, use_upper_for_p=None):
    arrival = {node: float('inf') for node in nodes}
    arrival[start] = start_time
    predecessors = {node: None for node in nodes}
    heap = [(start_time, start)]

    while heap:
        current_time, u = heapq.heappop(heap)
        if u == end:
            break
        if current_time > arrival[u]:
            continue
        for v in edges.get(u, []):
            # 判断是否在路径p中，决定取上界或下界
            if use_upper_for_p and (u, v) in use_upper_for_p:
                travel_time = get_impedance(u, v, current_time, is_upper=True)
            else:
                travel_time = get_impedance(u, v, current_time, is_upper=False)
            new_time = current_time + travel_time
            if new_time < arrival[v]:
                arrival[v] = new_time
                predecessors[v] = u
                heapq.heappush(heap, (new_time, v))

    # 回溯路径
    path = []
    current = end
    while current is not None:
        path.append(current)
        current = predecessors.get(current)
    path.reverse()
    return arrival[end], path


def calculate_robust_cost(nodes, edges, start, end, departure_time):
    # 阶段一：找到传统时间依赖最短路径p
    p_time, p_path = time_dependent_dijkstra(nodes, edges, start, end, departure_time)

    # 提取路径p中的路段集合
    p_edges = set()
    for i in range(len(p_path) - 1):
        u, v = p_path[i], p_path[i + 1]
        p_edges.add((u, v))
        # 处理双向路段
        if (v, u) in edges:
            p_edges.add((v, u))

    # 阶段二：在修正路网G'中找p*
    p_star_time, p_star_path = time_dependent_dijkstra(
        nodes, edges, start, end, departure_time, use_upper_for_p=p_edges
    )

    # 计算鲁棒成本：sum(p_upper) - sum(p*)
    sum_p_upper = 0
    current_time = departure_time
    for i in range(len(p_path) - 1):
        u, v = p_path[i], p_path[i + 1]
        sum_p_upper += get_impedance(u, v, current_time, is_upper=True)
        current_time += get_impedance(u, v, current_time, is_upper=True)

    robust_cost = sum_p_upper - p_star_time
    return robust_cost, p_path, p_star_path


# 示例调用
nodes = list(range(1, 30))
edges = {
    (1, 2): {
        'night_lower': 3, 'night_upper': 5,
        'day_lower': 4, 'day_upper': 8,
        'evening_lower': 4, 'evening_upper': 6
    },
    # ... 其他路段定义
}

departure_time = 8  # 假设出发时间为8:00
robust_cost, p_path, p_star_path = calculate_robust_cost(nodes, edges, 1, 29, departure_time)
print(f"鲁棒成本: {robust_cost}")
print(f"传统最优路径: {p_path}")
print(f"修正路网最短路径: {p_star_path}")