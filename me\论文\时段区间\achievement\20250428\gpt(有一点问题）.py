import numpy as np
import heapq
from collections import defaultdict, deque
import multiprocessing
import time

# ==================== 数据准备部分 ====================
edges = [(1, 2), (2, 1), (1, 3), (3, 1), (1, 4), (4, 1), (1, 5), (5, 1),
         (2, 3), (3, 2), (2, 6), (6, 2), (2, 7), (7, 2), (3, 4), (4, 3),
         (3, 7), (7, 3), (3, 8), (8, 3), (4, 5), (5, 4), (4, 8), (8, 4),
         (4, 9), (9, 4), (5, 9), (9, 5), (5, 10), (10, 5), (6, 7), (7, 6),
         (6, 11), (11, 6), (7, 8), (8, 7), (7, 11), (11, 7), (7, 12), (12, 7),
         (8, 9), (9, 8), (8, 12), (12, 8), (8, 13), (13, 8), (9, 10), (10, 9),
         (9, 13), (13, 9), (9, 14), (14, 9), (10, 14), (14, 10), (10, 15), (15, 10),
         (11, 12), (12, 11), (11, 16), (16, 11), (11, 17), (17, 11), (12, 13), (13, 12),
         (12, 17), (17, 12), (12, 18), (18, 12), (13, 14), (14, 13), (13, 18), (18, 13),
         (13, 19), (19, 13), (14, 15), (15, 14), (14, 19), (19, 14), (15, 19), (19, 15),
         (16, 17), (17, 16), (16, 20), (20, 16), (17, 18), (18, 17), (17, 20), (20, 17),
         (18, 19), (19, 18), (18, 20), (20, 18), (19, 20), (20, 19)]  # 共94条边

l_base = [9, 9, 7, 7, 6, 6, 10, 10, 6, 6,
          12, 12, 10, 10, 11, 11, 8, 8, 10, 10,
          9, 9, 7, 7, 6, 6, 7, 7, 9, 9,
          8, 8, 6, 6, 9, 9, 8, 8, 6, 6,
          8, 8, 11, 11, 6, 6, 10, 10, 11, 11,
          6, 6, 10, 10, 10, 10, 8, 8, 6, 6,
          7, 7, 8, 8, 6, 6, 9, 9, 9, 9,
          10, 10, 6, 6, 7, 7, 8, 8, 6, 6,
          9, 9, 7, 10, 8, 8, 6, 6, 9, 9,  # 补充缺失的6个值
          7, 8, 9, 10]  # 总长度94

u_base = [14, 14, 13, 13, 12, 12, 15, 15, 11, 11,
          15, 15, 14, 14, 15, 15, 12, 12, 15, 15,
          14, 14, 13, 13, 12, 12, 12, 12, 15, 15,
          14, 14, 12, 12, 14, 14, 14, 14, 11, 11,
          12, 12, 15, 15, 11, 11, 14, 14, 15, 15,
          11, 11, 14, 14, 14, 14, 12, 12, 11, 11,
          15, 15, 13, 13, 11, 11, 15, 15, 15, 15,
          13, 13, 11, 11, 12, 12, 15, 15, 10, 10,
          15, 15, 14, 14, 15, 15, 12, 12, 15, 15,  # 补充缺失的6个值
          13, 14, 15, 15]  # 总长度94

# 道路等级 road_levels，长度和 edges 一样
road_levels = [1,1,2,2,3,3,4,4,
               1,1,4,4,3,3,1,1,
               2,2,1,1,1,1,4,4,
               3,3,2,2,1,1,2,2,
               1,1,2,2,2,2,3,3,
               2,2,4,4,1,1,2,2,
               2,2,3,3,4,4,1,1,
               3,3,4,4,3,3,3,3,
               2,2,1,1,3,3,4,4,
               3,3,3,3,2,2,1,1,
               4,4,1,1,4,4,2,2,
               4,4,3,3,4,4]

# 拆分congestion_factors，根据道路等级区分
congestion_factors = {
    1: {  # 快速路
        "00:00-07:00": (-0.8, -0.1),  # 平
        "07:00-07:10": (0.3, 1.1),  # 早高峰
        "07:10-07:20": (0.3, 1.2),
        "07:20-07:30": (0.4, 1.1),
        "07:30-07:40": (0.2, 1.2),
        "07:40-07:50": (0.4, 1.3),
        "07:50-08:00": (0.2, 1.5),
        "08:00-08:10": (0.3, 1.4),
        "08:10-08:20": (0.4, 1.4),
        "08:20-08:30": (0.3, 1.3),
        "08:30-08:40": (0.2, 1.2),
        "08:40-08:50": (0.4, 1.1),
        "08:50-09:00": (0.3, 1.0),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.2, 1.5),  # 晚高峰
        "19:00-00:00": (-0.8, -0.1)  # 平
    },
    2: {  # 主干路
        "00:00-07:00": (-0.7, -0.2),  # 平
        "07:00-07:10": (0.4, 1.0),  # 早高峰
        "07:10-07:20": (0.4, 1.1),
        "07:20-07:30": (0.5, 1.0),
        "07:30-07:40": (0.3, 1.1),
        "07:40-07:50": (0.5, 1.2),
        "07:50-08:00": (0.3, 1.4),
        "08:00-08:10": (0.4, 1.3),
        "08:10-08:20": (0.5, 1.3),
        "08:20-08:30": (0.4, 1.2),
        "08:30-08:40": (0.3, 1.1),
        "08:40-08:50": (0.5, 1.0),
        "08:50-09:00": (0.4, 0.9),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.3, 1.4),  # 晚高峰
        "19:00-00:00": (-0.7, -0.2)  # 平
    },
    3: {  # 次干路
        "00:00-07:00": (-0.6, -0.3),  # 平
        "07:00-07:10": (0.5, 0.9),  # 早高峰
        "07:10-07:20": (0.5, 1.0),
        "07:20-07:30": (0.6, 0.9),
        "07:30-07:40": (0.4, 1.0),
        "07:40-07:50": (0.6, 1.1),
        "07:50-08:00": (0.4, 1.3),
        "08:00-08:10": (0.5, 1.2),
        "08:10-08:20": (0.6, 1.2),
        "08:20-08:30": (0.5, 1.1),
        "08:30-08:40": (0.4, 1.0),
        "08:40-08:50": (0.6, 0.9),
        "08:50-09:00": (0.5, 0.8),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.4, 1.3),  # 晚高峰
        "19:00-00:00": (-0.6, -0.3)  # 平
    },
    4: {  # 支路
        "00:00-07:00": (-0.5, -0.4),  # 平
        "07:00-07:10": (0.6, 0.8),  # 早高峰
        "07:10-07:20": (0.6, 0.9),
        "07:20-07:30": (0.7, 0.8),
        "07:30-07:40": (0.5, 0.9),
        "07:40-07:50": (0.7, 1.0),
        "07:50-08:00": (0.5, 1.2),
        "08:00-08:10": (0.6, 1.1),
        "08:10-08:20": (0.7, 1.1),
        "08:20-08:30": (0.6, 1.0),
        "08:30-08:40": (0.5, 0.9),
        "08:40-08:50": (0.7, 0.8),
        "08:50-09:00": (0.6, 0.7),
        "09:00-17:00": (0, 0),  # 平
        "17:00-19:00": (0.5, 1.2),  # 晚高峰
        "19:00-00:00": (-0.5, -0.4)  # 平
    }
}

# 注意！TIME_PERIODS也要适配成：
TIME_PERIODS = {
    "00:00-07:00": (0, 420),
    "07:00-07:10": (420, 430),
    "07:10-07:20": (430, 440),
    "07:20-07:30": (440, 450),
    "07:30-07:40": (450, 460),
    "07:40-07:50": (460, 470),
    "07:50-08:00": (470, 480),
    "08:00-08:10": (480, 490),
    "08:10-08:20": (490, 500),
    "08:20-08:30": (500, 510),
    "08:30-08:40": (510, 520),
    "08:40-08:50": (520, 530),
    "08:50-09:00": (530, 540),
    "09:00-17:00": (540, 1020),
    "17:00-19:00": (1020, 1140),
    "19:00-00:00": (1140, 1440)
}

# ==================== 时变阻抗生成模块 ====================
# 数据预处理 ----------------------------------------------
# 构建带时间维度的路网数据（关键预处理步骤）
def generate_time_dependent_data(edges, l_base, u_base, congestion_factors, road_levels):
    network_data = defaultdict(dict)
    for idx, (i, j) in enumerate(edges):  # 遍历所有边
        source = str(i)
        target = str(j)
        level = road_levels[idx]  # 获取当前边的道路等级
        time_ranges = []
        # 遍历该等级道路的所有时段配置
        for period, (f, g) in congestion_factors[level].items():
            start, end = TIME_PERIODS[period]  # 获取时段起止分钟数
            l_t = round(l_base[idx] * (1 + f), 2)  # 计算时段下限：l*(1+拥堵系数)
            u_t = round(u_base[idx] * (1 + g), 2)  # 计算时段上限：u*(1+拥堵系数)
            time_ranges.append([start, end, l_t, u_t])  # 存储时段阻抗范围
        network_data[source][target] = time_ranges  # 构建邻接表
    return network_data

# ==================== 增强版路径规划类 ====================
class EnhancedRoadNetwork:
    def __init__(self, edges, l_base, u_base, congestion_factors, road_levels):
        # 初始化网络参数
        self.edges = edges
        self.l_base = l_base  # 基准下限
        self.u_base = u_base  # 基准上限
        self.congestion_factors = congestion_factors  # 各等级道路的拥堵系数
        self.road_levels = road_levels  # 各边对应的道路等级
        
        # 生成动态路网数据（核心数据结构）
        network_data = generate_time_dependent_data(
            edges, l_base, u_base, congestion_factors, road_levels
        )
        self.graph = defaultdict(list)  # 图结构：邻接表
        self._initialize_graph(network_data)  # 初始化图
        self.robust_cost_cache = {}  # 鲁棒成本缓存

    def _initialize_graph(self, network_data):
        """构建邻接表结构，格式：{
            '节点A': [('节点B', [[时段1起止, 下限, 上限], ...]), ...],
            ...
        }"""
        for source, neighbors in network_data.items():
            for target, time_ranges in neighbors.items():
                self.graph[source].append((target, time_ranges))

    def get_impedance(self, impedance_ranges, time):
        """动态阻抗查询（关键时间处理逻辑）"""
        time %= 1440  # 处理跨天时间（1440=24*60）
        # 线性查找匹配时段（数据量小效率可接受）
        for start, end, low, high in impedance_ranges:
            if start <= time < end:
                return low, high  # 返回当前时段的上下界
        return impedance_ranges[-1][2], impedance_ranges[-1][3]  # 默认返回最后时段

    def dijkstra(self, start, end, time, lower_bound=True):
        """改进的Dijkstra算法（支持上下界模式）"""
        priority_queue = [(0, start, time)]  # (累计成本, 当前节点, 当前时间)
        dist = {start: 0}  # 最短距离记录
        prev = {start: None}  # 前驱节点

        while priority_queue:
            current_dist, current_node, current_time = heapq.heappop(priority_queue)
            
            # 终止条件：到达终点
            if current_node == end:
                # 回溯路径
                path = []
                while current_node:
                    path.append(current_node)
                    current_node = prev[current_node]
                return path[::-1], current_dist  # 返回反转后的路径

            # 剪枝：已有更优路径
            if current_dist > dist.get(current_node, float('inf')):
                continue

            # 遍历邻接节点
            for neighbor, impedance_ranges in self.graph[current_node]:
                # 获取当前时间的阻抗范围
                impedance = self.get_impedance(impedance_ranges, current_time)
                if not impedance:
                    continue
                
                # 选择计算模式：下界（优化模式）或上界（最坏情况）
                cost = impedance[0] if lower_bound else impedance[1]
                new_dist = current_dist + cost
                new_time = (current_time + cost) % 1440  # 时间推进

                # 松弛操作
                if new_dist < dist.get(neighbor, float('inf')):
                    dist[neighbor] = new_dist
                    prev[neighbor] = current_node
                    heapq.heappush(priority_queue, (new_dist, neighbor, new_time))
        
        return None, float('inf')  # 无路径情况

    def calculate_robust_cost(self, path, start_time):
        """鲁棒成本计算（目标函数实现）"""
        cache_key = (tuple(path), start_time)
        if cache_key in self.robust_cost_cache:
            return self.robust_cost_cache[cache_key]
        
        # 计算原路径最坏情况耗时（使用上界值）
        a = 0
        current_time = start_time
        for i in range(len(path)-1):
            u, v = path[i], path[i+1]
            # 查找对应边的阻抗范围
            for neighbor, ranges in self.graph[u]:
                if neighbor == v:
                    low, high = self.get_impedance(ranges, current_time)
                    a += high  # 累加上界值
                    current_time = (current_time + high) % 1440
                    break
        
        # 构建剩余图（排除原路径边）
        remaining_graph = defaultdict(list)
        for u in self.graph:
            for v, ranges in self.graph[u]:
                # 排除原路径中的边（确保路径独立性）
                if (u, v) not in zip(path[:-1], path[1:]):
                    remaining_graph[u].append((v, ranges))
        
        # 在剩余图中寻找最优路径（使用下界值）
        remaining_net = EnhancedRoadNetwork(
            self.edges,
            self.l_base,
            self.u_base,
            self.congestion_factors,
            self.road_levels
        )
        remaining_net.graph = remaining_graph
        alt_path, b = remaining_net.dijkstra(path[0], path[-1], start_time)
        
        # 计算鲁棒成本：原路径最坏情况 - 替代路径最佳情况
        robust_cost = a - b if alt_path else float('inf')
        result = (robust_cost, a, b, alt_path)
        self.robust_cost_cache[cache_key] = result
        return result

    def calculate_robust_cost_wrapper(self, args):
        """实例方法包装器"""
        path, start_time = args
        return self.calculate_robust_cost(path, start_time)

    def find_min_robust_path(self, start, end, start_time):
        """主优化算法（带剪枝的路径枚举）"""
        all_paths = []
        # 使用优先队列进行启发式搜索
        priority_queue = [(0, deque([start]), start_time)]
        min_cost = float('inf')
        
        while priority_queue:
            current_dist, current_path, current_time = heapq.heappop(priority_queue)
            current_node = current_path[-1]
            
            # 剪枝：当前路径已劣于已知最优
            if current_dist >= min_cost:
                continue
            
            # 路径到达终点
            if current_node == end:
                valid_path = list(current_path)
                all_paths.append(valid_path)
                # 更新当前最优估计值
                if current_dist < min_cost:
                    min_cost = current_dist
                continue
            
            # 扩展邻接节点
            for neighbor, ranges in self.graph[current_node]:
                if neighbor not in current_path:  # 防止环路
                    # 获取当前边的最优情况（下界）
                    low, _ = self.get_impedance(ranges, current_time)
                    new_dist = current_dist + low
                    new_time = (current_time + low) % 1440
                    new_path = current_path.copy()
                    new_path.append(neighbor)
                    heapq.heappush(priority_queue, (new_dist, new_path, new_time))
        
        # 并行计算鲁棒成本
        with multiprocessing.Pool() as pool:
            results = pool.map(self.calculate_robust_cost_wrapper, 
                             [(p, start_time) for p in all_paths])
        
        # 寻找最小鲁棒成本
        min_robust = (None, float('inf'), 0, 0, None)
        for path, (rc, a, b, alt) in zip(all_paths, results):
            if rc < min_robust[1]:
                min_robust = (path, rc, a, b, alt)
        
        return min_robust


# ======================= 主程序模块 ======================
if __name__ == '__main__':
    # 数据完整性检查
    assert len(edges) == len(l_base) == len(u_base), "基础数据长度不匹配"

    # 正确的时间覆盖验证
    total = sum(end - start for start, end in TIME_PERIODS.values())
    assert total == 1440, f"时间区间覆盖不完整，当前总时长：{total}分钟"
    print(f"时间覆盖验证通过，总时长：{total}分钟")


    # 交互式查询功能
    def time_to_minutes(t_str):
        """将时间字符串转换为分钟数"""
        try:
            hour, minute = map(int, t_str.split(':'))
            if 0 <= hour < 24 and 0 <= minute < 60:
                return hour * 60 + minute
            return None
        except:
            return None


    road_net = EnhancedRoadNetwork(
        edges, l_base, u_base, congestion_factors, road_levels
    )

    while True:
        print("\n=== 路径规划查询 ===")
        start_node = input("请输入起点编号（1-20，q退出）: ").strip()
        if start_node.lower() == 'q':
            break

        end_node = input("请输入终点编号（1-20）: ").strip()
        time_str = input("请输入出发时间（格式HH:MM，如07:30）: ").strip()

        # 输入验证
        if not (start_node.isdigit() and end_node.isdigit()):
            print("错误：节点编号必须为数字")
            continue

        depart_time = time_to_minutes(time_str)
        if depart_time is None:
            print("错误：时间格式无效")
            continue

        # 执行路径规划
        try:
            path, cost, worst, best, alt = road_net.find_min_robust_path(
                start_node, end_node, depart_time)

            print("\n=== 规划结果 ===")
            print(f"出发时间: {time_str}")
            print(f"路径: {' → '.join(path)}")
            print(f"鲁棒成本: {cost:.2f} 分钟")
            print(f"最坏情况耗时: {worst:.2f} 分钟")
            print(f"备选路径最佳耗时: {best:.2f} 分钟")
            print(f"备选路径: {' → '.join(alt) if alt else '无可用备选'}")

        except Exception as e:
            print(f"路径规划失败: {str(e)}")