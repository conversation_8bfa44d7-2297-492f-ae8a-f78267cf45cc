import numpy as np
import heapq
import time

# 数据定义
nodes = np.arange(1, 27)  # 26个节点
edges = np.array([
    [1, 2], [1, 3], [1, 4], [1, 5],
    [2, 3], [2, 6], [2, 11],
    [3, 4], [3, 6], [3, 7],
    [4, 5], [4, 7], [4, 8], [4, 9],
    [5, 9], [5, 10],
    [6, 7], [6, 11], [6, 12], [6, 13],
    [7, 8], [7, 13],
    [8, 9], [8, 13], [8, 14],
    [9, 10], [9, 14], [9, 15],
    [10, 15], [10, 16],
    [11, 12],
    [12, 13], [12, 17],
    [13, 14], [13, 17], [13, 18],
    [14, 15], [14, 18], [14, 19],
    [15, 16], [15, 19], [15, 20],
    [16, 20], [16, 21],
    [17, 18], [17, 22],
    [18, 19], [18, 22], [18, 23],
    [19, 20], [19, 23], [19, 24],
    [20, 21], [20, 24],
    [21, 24], [21, 25],
    [22, 23], [22, 26],
    [23, 24], [23, 26],
    [24, 25], [24, 26],
    [25, 26]
])

# 边的下界和上界阻抗（需要与边的数量一致）
l = np.array([0.2, 1, 0.9, 0.2, 0.9, 0.8, 1.5, 1.1, 1.5, 0.8, 0.1, 1.1, 0.5, 1.1, 0.9, 0.5, 0.7, 0.7, 1.4,
              0.9, 0.2, 0.4, 0.6, 1.1, 0.6, 0.7, 0.7, 1.2, 1.1, 1, 0.7, 0.3, 0.4, 1.2, 1.5, 0.9, 0.5, 0.9,
              0.6, 1.1, 1, 1, 1.2, 0.8, 1.2, 1, 0.5, 0.5, 0.6, 0.3, 0.9, 0.2, 1.1, 0.5, 1.2, 1.1, 0.7,
              0.9, 2, 0.8, 0.5, 0.5, 2.4])
u = np.array([4, 2, 1.8,  2.1, 1.7, 2.1, 3.2, 1.7, 2.1, 1.5, 1.8, 1.3, 1.3, 2.4, 1.5, 1.5, 1.2, 2.1, 2.3,
              1, 1.5, 2.5, 1.6, 3.5, 1.7, 1.4, 3, 1.8, 1.7, 2.4, 2.1, 1.5, 1.9, 1.9, 3.2, 1.9, 2.5, 2.5,
              2.1, 1.6, 2.8, 1.7, 2.7, 2.2, 2.5, 1.6, 1.5, 1.7, 2.2, 2.3, 2.5, 2.6, 2.4, 2.2, 2.8, 2.7, 1.8,
              2.8, 2.2, 2.3, 1.4, 3.8, 4.4])


# 起点和终点
start_node = 1
end_node = 26

# 为了后续找出上界阻抗总和、未经过路段的最小下界阻抗
def find_edge(node1, node2, edges):
    index = np.where((edges[:, 0] == node1) & (edges[:, 1] == node2))
    return index[0][0] if index[0].size > 0 else None

def branch_and_bound(nodes, edges, l, u, start, end):
    optimal_path = []
    R_ort = float('inf')

    def search(current_node, current_path, current_cost):
        nonlocal optimal_path, R_ort

        # 检查是否到达终点
        if current_node == end:
            R_j = compute_robust_cost(current_path, edges, l, u)
            if R_j < R_ort:
                R_ort = R_j
                optimal_path = current_path
            return

        # 遍历相邻节点
        neighbors = get_neighbors(current_node, edges)
        for next_node in neighbors:
            R_ri = compute_robust_cost(current_path + [next_node], edges, l, u)
            if R_ri <= R_ort:
                search(next_node, current_path + [next_node], R_ri)

    # 启动搜索
    search(start, [start], 0)
    return optimal_path, R_ort

def compute_robust_cost(path, edges, l, u):
    max_cost = 0

    # 累加当前路径的上界阻抗
    for i in range(len(path) - 1):
        edge_index = find_edge(path[i], path[i + 1], edges)
        if edge_index is not None:
            max_cost += u[edge_index]  # 累加上界阻抗

    # 找到未经过的路段并计算最小下界阻抗
    visited_edges = set()
    for i in range(len(path) - 1):
        visited_edges.add(find_edge(path[i], path[i + 1], edges))

    # 创建未经过的边的列表
    unused_edges = []
    for i in range(len(edges)):
        if i not in visited_edges:
            unused_edges.append(i)
    min_cost_path = dijkstra_min_cost(unused_edges, edges, l, start_node)

    # 使用Dijkstra算法或其他方法找出未经过的路段组成的最小下界阻抗路径
    min_cost = dijkstra_min_cost(unused_edges, edges, l)

    return max_cost - min_cost, min_cost_path  # 返回鲁棒成本和对应路径

def dijkstra_min_cost(unused_edges, edges, l, start_node=1, end_node=26):
    # 创建图的邻接列表
    graph = {}
    for edge in unused_edges:
        node1, node2 = edges[edge]
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        graph[node1].append((node2, l[edge]))  # (相邻节点, 下界阻抗)
        graph[node2].append((node1, l[edge]))  # 双向

    # Dijkstra 算法初始化
    min_heap = [(0, start_node)]  # (成本, 当前节点)
    min_cost = {node: float('inf') for node in graph}
    min_cost[start_node] = 0

    while min_heap:
        current_cost, current_node = heapq.heappop(min_heap)

        # 如果到达终点，返回当前成本
        if current_node == end_node:
            return current_cost

        # 遍历相邻节点
        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor))

    # 如果无法到达终点，返回无限大
    return float('inf')

def get_neighbors(node, edges):
    return np.unique(edges[edges[:, 0] == node][:, 1])

def find_edge(node1, node2, edges):
    index = np.where((edges[:, 0] == node1) & (edges[:, 1] == node2))
    return index[0][0] if index[0].size > 0 else None

def find_shortest_path(edges, l, u, start_node, end_node, cost_type='lower'):
    graph = {}
    for i, (node1, node2) in enumerate(edges):
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        if cost_type == 'lower':
            graph[node1].append((node2, l[i]))
            graph[node2].append((node1, l[i]))
        else:
            graph[node1].append((node2, u[i]))
            graph[node2].append((node1, u[i]))

    # Dijkstra 算法初始化
    min_heap = [(0, start_node, [])]  # (成本, 当前节点, 路径)
    min_cost = {node: float('inf') for node in graph}
    min_cost[start_node] = 0

    while min_heap:
        current_cost, current_node, path = heapq.heappop(min_heap)
        path = path + [current_node]

        # 如果到达终点，返回当前路径
        if current_node == end_node:
            return path

        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor, path))

    return []  # 如果无法到达终点，返回空路径

# 记录整个程序的运行时间
start_time = time.time()

# 调用算法求解-最优路径，最小鲁棒成本
optimal_path, robust_cost = branch_and_bound(nodes, edges, l, u, start_node, end_node)
# 求上界阻抗总和
max_cost = sum(u[find_edge(optimal_path[i], optimal_path[i + 1], edges)] for i in range(len(optimal_path) - 1))
# 找到未经过的边
visited_edges = set(find_edge(optimal_path[i], optimal_path[i + 1], edges) for i in range(len(optimal_path) - 1))
unused_edges = [i for i in range(len(edges)) if i not in visited_edges]
# 使用Dijkstra算法计算未经过路段的最小下界阻抗
min_cost = dijkstra_min_cost(unused_edges, edges, l)
# 求运行时间
end_time = time.time()
# 计算总路网的下界最短路径和上界最短路径
lower_bound_path = find_shortest_path(edges, l, u, start_node, end_node, cost_type='lower')
upper_bound_path = find_shortest_path(edges, l, u, start_node, end_node, cost_type='upper')

# 输出结果
print("最优路径:", [int(node) for node in optimal_path])
print("最小鲁棒成本:", robust_cost)
print("上界阻抗总和:", max_cost)
print("未经过路段的最小下界阻抗:", min_cost)
print("运行时间:", end_time - start_time, "秒")
print("总路网下界最短路径:", [int(node) for node in lower_bound_path])
print("总路网上界最短路径:", [int(node) for node in upper_bound_path])


"""
运行结果：最优路径: [1, 4, 10, 15, 21, 25, 29]
        最小鲁棒成本: 33
        上界阻抗总和: 59
        未经过路段的最小下界阻抗: 26
        运行时间: 0.2767437744140625 秒
        总路网下界最短路径: [1, 5, 4, 8, 14, 19, 24, 26]
        总路网上界最短路径: [1, 3, 6, 13, 18, 22, 26]
"""
