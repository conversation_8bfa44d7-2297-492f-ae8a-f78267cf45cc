# 数据部分,29个节点70条路段

import numpy as np

#第一种：上下界都放在一起
Links = [(1, 2, 6, 10), (1, 3, 6, 8), (2, 3, 6, 10), (1, 4, 5, 9), (3, 4, 6, 10), (1, 5, 3, 5), (4, 5, 1, 4),
         (2, 6, 6, 10), (3, 6, 6, 10), (3, 7, 6, 8), (6, 7, 6, 10), (3, 8, 6, 10), (4, 8, 6, 8), (7, 8, 6, 10),
         (4, 9, 3, 9), (8, 9, 6, 10), (4, 10, 6, 10), (5, 10, 3, 10), (9, 10, 6, 10), (6, 11, 6, 10), (7, 11, 6, 10),
         (7, 12, 6, 8), (11, 12, 6, 10), (7, 13, 6, 10), (8, 13, 6, 8), (12, 13, 6, 10), (8, 14, 6, 10), (9, 14, 6, 8),
         (13, 14, 6, 10), (9, 15, 3, 9), (10, 15, 6, 12), (14, 15, 6, 10), (10, 16, 3, 10), (15, 16, 6, 10),
         (11, 17, 6, 10),
         (12, 17, 6, 10), (12, 18, 6, 8), (13, 18, 6, 8), (17, 18, 6, 10), (13, 19, 6, 10), (14, 19, 6, 8),
         (18, 19, 6, 10),
         (14, 20, 6, 10), (15, 20, 4, 9), (19, 20, 6, 10), (15, 21, 6, 10), (16, 21, 3, 10), (20, 21, 6, 10),
         (17, 22, 6, 10),
         (18, 22, 6, 10), (18, 23, 6, 8), (19, 23, 6, 8), (22, 23, 6, 10), (19, 24, 6, 8), (20, 24, 5, 10),
         (23, 24, 6, 10),
         (20, 25, 6, 10), (21, 25, 3, 11), (24, 25, 6, 10), (22, 26, 6, 10), (23, 26, 6, 10), (23, 27, 6, 8),
         (24, 27, 4, 9),
         (26, 27, 2, 3), (24, 28, 6, 10), (25, 28, 3, 10), (27, 28, 6, 10), (26, 29, 5, 13), (27, 29, 2, 4),
         (28, 29, 3, 13),
         (2, 1, 6, 10), (3, 1, 6, 8), (3, 2, 6, 10), (4, 1, 5, 9), (4, 3, 6, 10), (5, 1, 3, 5), (5, 4, 1, 4),
         (6, 2, 6, 10), (6, 3, 6, 10), (7, 3, 6, 8), (7, 6, 6, 10), (8, 3, 6, 10), (8, 4, 6, 8), (8, 7, 6, 10),
         (9, 4, 3, 9), (9, 8, 6, 10), (10, 4, 6, 10), (10, 5, 3, 10), (10, 9, 6, 10), (11, 6, 6, 10), (11, 7, 6, 10),
         (12, 7, 6, 8), (12, 11, 6, 10), (13, 7, 6, 10), (13, 8, 6, 8), (13, 12, 6, 10), (14, 8, 6, 10), (14, 9, 6, 8),
         (14, 13, 6, 10), (15, 9, 3, 9), (15, 10, 6, 12), (15, 14, 6, 10), (16, 10, 3, 10), (16, 15, 6, 10),
         (17, 11, 6, 10),
         (17, 12, 6, 10), (18, 12, 6, 8), (18, 13, 6, 8), (18, 17, 6, 10), (19, 13, 6, 10), (19, 14, 6, 8),
         (19, 18, 6, 10),
         (20, 14, 6, 10), (20, 15, 4, 9), (20, 19, 6, 10), (21, 15, 6, 10), (21, 16, 3, 10), (21, 20, 6, 10),
         (22, 17, 6, 10),
         (22, 18, 6, 10), (23, 18, 6, 8), (23, 19, 6, 8), (23, 22, 6, 10), (24, 19, 6, 8), (24, 20, 5, 10),
         (24, 23, 6, 10),
         (25, 20, 6, 10), (25, 21, 3, 11), (25, 24, 6, 10), (26, 22, 6, 10), (26, 23, 6, 10), (27, 23, 6, 8),
         (27, 24, 4, 9),
         (27, 26, 2, 3), (28, 24, 6, 10), (28, 25, 3, 10), (28, 27, 6, 10), (29, 26, 5, 13), (29, 27, 2, 4),
         (29, 28, 3, 13)]
#坐标：
pos = {1: (0.46035, 5.0452), 2: (0.73589, 7.177), 3: (1.4617, 6.0917), 4: (1.6095, 4.4897), 5: (1.0316, 3.1072),
           6: (1.5827, 8.4173), 7: (2.2749, 7.1641), 8: (2.7251, 5.3165), 9: (2.6781, 3.624), 10: (2.127, 1.7765),
           11: (2.7722, 9.1279), 12: (3.4778, 7.7842), 13: (3.8071, 6.2726), 14: (3.8542, 4.2313), 15: (3.6929, 2.5517),
           16: (3.2359, 0.67829), 17: (4.5128, 9.0504), 18: (5.0302, 7.5), 19: (5.2587, 5.3036), 20: (5.2386, 3.2235),
           21: (4.8353, 1.1305), 22: (6.0921, 8.6886), 23: (6.4751, 6.7119), 24: (6.5558, 4.593), 25: (6.334, 2.0995),
           26: (7.5034, 8.1202), 27: (7.8058, 5.8075), 28: (7.584, 3.1589), 29: (8.9751, 5.7041)}

#第二种：上下界合并
middle = {
    (1, 2, 16), (1, 3, 14), (2, 3, 16), (1, 4, 14), (3, 4, 16), (1, 5, 8), (4, 5, 5),
    (2, 6, 16), (3, 6, 16), (3, 7, 14), (6, 7, 16), (3, 8, 16), (4, 8, 14), (7, 8, 16),
    (4, 9, 12), (8, 9, 16), (4, 10, 16), (5, 10, 13), (9, 10, 16), (6, 11, 16), (7, 11, 16),
    (7, 12, 14), (11, 12, 16), (7, 13, 16), (8, 13, 14), (12, 13, 16), (8, 14, 16), (9, 14, 14),
    (13, 14, 16), (9, 15, 12), (10, 15, 18), (14, 15, 16), (10, 16, 13), (15, 16, 16), (11, 17, 16),
    (12, 17, 16), (12, 18, 14), (13, 18, 14), (17, 18, 16), (13, 19, 16), (14, 19, 14), (18, 19, 16),
    (14, 20, 16), (15, 20, 13), (19, 20, 16), (15, 21, 16), (16, 21, 13), (20, 21, 16), (17, 22, 16),
    (18, 22, 16), (18, 23, 14), (19, 23, 14), (22, 23, 16), (19, 24, 14), (20, 24, 15), (23, 24, 16),
    (20, 25, 16), (21, 25, 14), (24, 25, 16), (22, 26, 16), (23, 26, 16), (23, 27, 14), (24, 27, 13),
    (26, 27, 5), (24, 28, 16), (25, 28, 13), (27, 28, 16), (26, 29, 18), (27, 29, 6), (28, 29, 16),
    (2, 1, 16), (3, 1, 14), (3, 2, 16), (4, 1, 14), (4, 3, 16), (5, 1, 8), (5, 4, 5),
    (6, 2, 16), (6, 3, 16), (7, 3, 14), (7, 6, 16), (8, 3, 16), (8, 4, 14), (8, 7, 16),
    (9, 4, 12), (9, 8, 16), (10, 4, 16), (10, 5, 13), (10, 9, 16), (11, 6, 16), (11, 7, 16),
    (12, 7, 14), (12, 11, 16), (13, 7, 16), (13, 8, 14), (13, 12, 16), (14, 8, 16), (14, 9, 14),
    (14, 13, 16), (15, 9, 12), (15, 10, 18), (15, 14, 16), (16, 10, 13), (16, 15, 16), (17, 11, 16),
    (17, 12, 16), (18, 12, 14), (18, 13, 14), (18, 17, 16), (19, 13, 16), (19, 14, 14), (19, 18, 16),
    (20, 14, 16), (20, 15, 13), (20, 19, 16), (21, 15, 16), (21, 16, 13), (21, 20, 16), (22, 17, 16),
    (22, 18, 16), (23, 18, 14), (23, 19, 14), (23, 22, 16), (24, 19, 14), (24, 20, 15), (24, 23, 16),
    (25, 20, 16), (25, 21, 14), (25, 24, 16), (26, 22, 16), (26, 23, 16), (27, 23, 14), (27, 24, 13),
    (27, 26, 5), (28, 24, 16), (28, 25, 13), (28, 27, 16), (29, 26, 18), (29, 27, 6), (29, 28, 16)
}


#第三种：分开的
links = [(1, 2), (1, 3), (2, 3), (1, 4), (3, 4), (1, 5), (4, 5), (2, 6), (3, 6), (3, 7), (6, 7),
         (3, 8), (4, 8), (7, 8), (4, 9), (8, 9), (4, 10), (5, 10), (9, 10), (6, 11), (7, 11),
         (7, 12), (11, 12), (7, 13), (8, 13), (12, 13), (8, 14), (9, 14), (13, 14), (9, 15),
         (10, 15), (14, 15), (10, 16), (15, 16), (11, 17), (12, 17), (12, 18), (13, 18), (17, 18),
         (13, 19), (14, 19), (18, 19), (14, 20), (15, 20), (19, 20), (15, 21), (16, 21), (20, 21),
         (17, 22), (18, 22), (18, 23), (19, 23), (22, 23), (19, 24), (20, 24), (23, 24), (20, 25),
         (21, 25), (24, 25), (22, 26), (23, 26), (23, 27), (24, 27), (26, 27), (24, 28), (25, 28),
         (27, 28), (26, 29), (27, 29), (28, 29), (2, 1), (3, 1), (3, 2), (4, 1), (4, 3), (5, 1),
         (5, 4), (6, 2), (6, 3), (7, 3), (7, 6), (8, 3), (8, 4), (8, 7), (9, 4), (9, 8), (10, 4),
         (10, 5), (10, 9), (11, 6), (11, 7), (12, 7), (12, 11), (13, 7), (13, 8), (13, 12), (14, 8),
         (14, 9), (14, 13), (15, 9), (15, 10), (15, 14), (16, 10), (16, 15), (17, 11), (17, 12),
         (18, 12), (18, 13), (18, 17), (19, 13), (19, 14), (19, 18), (20, 14), (20, 15), (20, 19),
         (21, 15), (21, 16), (21, 20), (22, 17), (22, 18), (23, 18), (23, 19), (23, 22), (24, 19),
         (24, 20), (24, 23), (25, 20), (25, 21), (25, 24), (26, 22), (26, 23), (27, 23), (27, 24),
         (27, 26), (28, 24), (28, 25), (28, 27), (29, 26), (29, 27), (29, 28)]
edges = [[1, 2], [1, 3], [2, 3], [1, 4], [3, 4], [1, 5], [4, 5], [2, 6], [3, 6], [3, 7], [6, 7],
[3, 8], [4, 8], [7, 8], [4, 9], [8, 9], [4, 10], [5, 10], [9, 10], [6, 11], [7, 11],
[7, 12], [11, 12], [7, 13], [8, 13], [12, 13], [8, 14], [9, 14], [13, 14], [9, 15],
[10, 15], [14, 15], [10, 16], [15, 16], [11, 17], [12, 17], [12, 18], [13, 18], [17, 18],
[13, 19], [14, 19], [18, 19], [14, 20], [15, 20], [19, 20], [15, 21], [16, 21], [20, 21],
[17, 22], [18, 22], [18, 23], [19, 23], [22, 23], [19, 24], [20, 24], [23, 24], [20, 25],
[21, 25], [24, 25], [22, 26], [23, 26], [23, 27], [24, 27], [26, 27], [24, 28], [25, 28],
[27, 28], [26, 29], [27, 29], [28, 29], [2, 1], [3, 1], [3, 2], [4, 1], [4, 3], [5, 1],
[5, 4], [6, 2], [6, 3], [7, 3], [7, 6], [8, 3], [8, 4], [8, 7], [9, 4], [9, 8], [10, 4],
[10, 5], [10, 9], [11, 6], [11, 7], [12, 7], [12, 11], [13, 7], [13, 8], [13, 12], [14, 8],
[14, 9], [14, 13], [15, 9], [15, 10], [15, 14], [16, 10], [16, 15], [17, 11], [17, 12],
[18, 12], [18, 13], [18, 17], [19, 13], [19, 14], [19, 18], [20, 14], [20, 15], [20, 19],
[21, 15], [21, 16], [21, 20], [22, 17], [22, 18], [23, 18], [23, 19], [23, 22], [24, 19],
[24, 20], [24, 23], [25, 20], [25, 21], [25, 24], [26, 22], [26, 23], [27, 23], [27, 24],
[27, 26], [28, 24], [28, 25], [28, 27], [29, 26], [29, 27], [29, 28]]

l = [6, 6, 6, 5, 6, 3, 1, 6, 6, 6, 6, 6, 6, 6, 3, 6, 6, 3, 6, 6,
     6, 6, 6, 6, 6, 6, 6, 6, 6, 3, 6, 6, 3, 6, 6, 6, 6, 6, 6, 6,
     6, 6, 6, 4, 6, 6, 3, 6, 6, 6, 6, 6, 6, 6, 5, 6, 6, 3, 6, 6,
     6, 6, 4, 2, 6, 3, 6, 9, 2, 3, 6, 6, 6, 5, 6, 3, 1, 6, 6, 6,
     6, 6, 6, 6, 3, 6, 6, 3, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 3,
     6, 6, 3, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 4, 6, 6, 3, 6, 6, 6,
     6, 6, 6, 6, 5, 6, 6, 3, 6, 6, 6, 6, 4, 2, 6, 3, 6, 9, 2, 3]

u = [10, 8, 10, 9, 10, 5, 4, 10, 10, 8, 10, 10, 8, 10, 9, 10, 10, 10, 10, 10,
     10, 8, 10, 10, 8, 10, 10, 8, 10, 9, 12, 10, 10, 10, 10, 10, 8, 8, 10, 10,
     8, 10, 10, 9, 10, 10, 10, 10, 10, 10, 8, 8, 10, 8, 10, 10, 10, 11, 10, 10,
     10, 8, 9, 3, 10, 10, 10, 13, 4, 13, 10, 8, 10, 9, 10, 5, 4, 10, 10, 8,
     10, 10, 8, 10, 9, 10, 10, 10, 10, 10, 10, 8, 10, 10, 8, 10, 10, 8, 10, 9,
     12, 10, 10, 10, 10, 10, 8, 8, 10, 10, 8, 10, 10, 9, 10, 10, 10, 10, 10, 10,
     8, 8, 10, 8, 10, 10, 10, 11, 10, 10, 10, 8, 9, 3, 10, 10, 10, 13, 4, 13]

# 起点和终点
start_node = 1
end_node = 29
