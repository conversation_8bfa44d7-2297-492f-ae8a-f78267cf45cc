# 手动定义每个路段的阻抗区间（夜间、白天、晚间）
edges = {
    # 1号节点连接的边
    (1, 2): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4,
             'evening_upper': 8},
    (1, 3): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5,
             'evening_upper': 9},
    (1, 4): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3,
             'evening_upper': 6},
    (1, 5): {'night_lower': 3, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 5,
             'evening_upper': 10},

    # 2号节点连接的边
    (2, 1): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4,
             'evening_upper': 8},  # 对称
    (2, 3): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3,
             'evening_upper': 6},
    (2, 6): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5,
             'evening_upper': 9},

    # 3号节点连接的边
    (3, 1): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5,
             'evening_upper': 9},  # 对称
    (3, 2): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3,
             'evening_upper': 6},  # 对称
    (3, 4): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4,
             'evening_upper': 8},
    (3, 6): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5,
             'evening_upper': 9},
    (3, 7): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6,
             'evening_upper': 11},
    (3, 8): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4,
             'evening_upper': 8},

    # 4号节点连接的边（部分示例，其他路段类似）
    (4, 1): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3,
             'evening_upper': 6},  # 对称
    (4, 3): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4,
             'evening_upper': 8},  # 对称
    (4, 5): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5,
             'evening_upper': 9},
    (4, 8): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3,
             'evening_upper': 6},
    (4, 9): {'night_lower': 3, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 5,
             'evening_upper': 10},
    (4, 10): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4,
              'evening_upper': 8},

    # 继续为其他路段定义...
    # 例如节点5到10的边：
    (5, 1): {'night_lower': 3, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 5,
             'evening_upper': 10},  # 对称
    (5, 4): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5,
             'evening_upper': 9},  # 对称
    (5, 10): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3,
              'evening_upper': 6},

    # 其他节点依此类推，确保双向对称...
    # 示例：节点29的边
    (26, 29): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5,
               'evening_upper': 9},
    (27, 29): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4,
               'evening_upper': 8},
    (28, 29): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6,
               'evening_upper': 11},

    # ... 其他路段按相同模式补充完整
}

# 确保所有双向路段对称（例如 (2,1) 与 (1,2) 相同）
for (u, v) in list(edges.keys()):
    if (v, u) not in edges:
        edges[(v, u)] = edges[(u, v)]