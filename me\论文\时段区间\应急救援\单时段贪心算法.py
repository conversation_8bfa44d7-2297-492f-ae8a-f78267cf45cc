from gurobipy import GRB
import numpy as np
import math
import random
import time

# ---------- 节点与边定义 ----------
edges = [
    (1, 8), (8, 1), (1, 20), (20, 1), (1, 29), (29, 1), (1, 45), (45, 1), (1, 50), (50, 1), (1, 51), (51, 1), (1, 53), (53, 1),
    (2, 22), (22, 2), (2, 23), (23, 2), (2, 33), (33, 2), (2, 48), (48, 2), (2, 49), (49, 2),
    (3, 22), (22, 3), (3, 23), (23, 3), (3, 25), (25, 3), (3, 27), (27, 3), (3, 41), (41, 3), (3, 42), (42, 3),
    (4, 5), (5, 4), (4, 30), (30, 4), (4, 36), (36, 4), (4, 37), (37, 4), (4, 38), (38, 4), (4, 39), (39, 4), (4, 40), (40, 4), (4, 43), (43, 4),
    (5, 37), (37, 5), (5, 38), (38, 5),
    (6, 17), (17, 6), (6, 18), (18, 6), (6, 20), (20, 6), (6, 46), (46, 6), (6, 47), (47, 6), (6, 48), (48, 6),
    (7, 17), (17, 7), (7, 20), (20, 7), (7, 53), (53, 7),
    (8, 50), (50, 8), (8, 51), (51, 8),
    (9, 31), (31, 9), (9, 32), (32, 9), (9, 47), (47, 9), (9, 48), (48, 9),
    (10, 18), (18, 10), (10, 28), (28, 10), (10, 34), (34, 10), (10, 37), (37, 10), (10, 45), (45, 10), (10, 47), (47, 10),
    (11, 29), (29, 11), (11, 30), (30, 11), (11, 51), (51, 11),
    (12, 19), (19, 12), (12, 31), (31, 12), (12, 33), (33, 12), (12, 49), (49, 12),
    (14, 27), (27, 14), (14, 40), (40, 14), (14, 41), (41, 14),
    (15, 21), (21, 15), (15, 22), (22, 15), (15, 26), (26, 15), (15, 42), (42, 15),
    (16, 25), (25, 16), (16, 26), (26, 16), (16, 42), (42, 16),
    (17, 48), (48, 17),
    (18, 46), (46, 18), (18, 47), (47, 18),
    (19, 31), (31, 19), (19, 32), (32, 19), (19, 33), (33, 19), (19, 34), (34, 19),
    (20, 45), (45, 20), (20, 53), (53, 20),
    (21, 22), (22, 21), (21, 49), (49, 21),
    (22, 23), (23, 22), (22, 42), (42, 22),
    (23, 24), (24, 23), (23, 35), (35, 23), (23, 41), (41, 23), (23, 48), (48, 23),
    (24, 35), (35, 24), (24, 39), (39, 24), (24, 40), (40, 24), (24, 41), (41, 24),
    (25, 27), (27, 25), (25, 42), (42, 25), (25, 52), (52, 25),
    (26, 42), (42, 26),
    (27, 41), (41, 27), (27, 52), (52, 27),
    (28, 43), (43, 28), (28, 44), (44, 28), (28, 45), (45, 28),
    (29, 44), (44, 29), (29, 45), (45, 29), (29, 51), (51, 29),
    (30, 36), (36, 30), (30, 43), (43, 30), (30, 44), (44, 30),
    (31, 32), (32, 31),
    (32, 34), (34, 32), (32, 47), (47, 32),
    (33, 34), (34, 33), (33, 48), (48, 33), (33, 49), (49, 33),
    (34, 35), (35, 34), (34, 37), (37, 34), (34, 38), (38, 34), (34, 47), (47, 34), (34, 48), (48, 34),
    (35, 38), (38, 35), (35, 48), (48, 35),
    (36, 40), (40, 36),
    (37, 43), (43, 37),
    (38, 39), (39, 38),
    (40, 41), (41, 40),
    (43, 44), (44, 43),
    (45, 46), (46, 45),
    (47, 48), (48, 47),
    (50, 53), (53, 50)
]

N = sorted(set(i for e in edges for i in e))
S = [1, 2, 3]  # 起点集合
M = [i for i in range(17, 47) ]  
K = [0, 1, 2, 3, 4, 5]  # 车辆集合，固定6辆车

# 受灾点需求
p = {
    17: 192, 18: 197, 19: 107, 20: 183, 21: 197, 22: 232, 23: 153, 24: 217, 25: 193, 26: 216,
    27: 196, 28: 243, 29: 218, 30: 277, 31: 186, 32: 184, 33: 107, 34: 203, 35: 178, 36: 247,
    37: 194, 38: 179, 39: 183, 40: 184, 41: 206, 42: 229, 43: 246, 44: 223, 45: 220, 46: 200
}  # 受灾点的实际需求量(kg)

Q = 1500  # 车辆容量
Tmax = 100  # 最大行程时间

# 路段行程时间区间：tl下界，tu上界（具体数据，精确到小数点后一位）
tl = {
    (1, 8): 1.5, (8, 1): 1.7, (1, 20): 2.0, (20, 1): 1.8, (1, 29): 2.2, (29, 1): 1.9, 
    (1, 45): 2.4, (45, 1): 2.0, (1, 50): 1.3, (50, 1): 1.1, (1, 51): 1.6, (51, 1): 1.7, 
    (1, 53): 2.1, (53, 1): 2.3, (2, 22): 1.7, (22, 2): 1.8, (2, 23): 2.1, (23, 2): 2.2, 
    (2, 33): 1.5, (33, 2): 1.6, (2, 48): 2.5, (48, 2): 2.7, (2, 49): 1.8, (49, 2): 1.7, 
    (3, 22): 1.1, (22, 3): 1.2, (3, 23): 1.6, (23, 3): 1.5, (3, 25): 2.0, (25, 3): 2.2, 
    (3, 27): 1.9, (27, 3): 1.8, (3, 41): 2.4, (41, 3): 2.3, (3, 42): 1.4, (42, 3): 1.5, 
    (4, 5): 1.2, (5, 4): 1.3, (4, 30): 2.3, (30, 4): 2.1, (4, 36): 2.5, (36, 4): 2.6, 
    (4, 37): 1.9, (37, 4): 1.7, (4, 38): 1.5, (38, 4): 1.6, (4, 39): 2.2, (39, 4): 2.3, 
    (4, 40): 1.7, (40, 4): 1.8, (4, 43): 2.1, (43, 4): 2.2, (5, 37): 1.3, (37, 5): 1.4, 
    (5, 38): 1.9, (38, 5): 1.8, (6, 17): 2.0, (17, 6): 2.1, (6, 18): 1.4, (18, 6): 1.5, 
    (6, 20): 1.7, (20, 6): 1.6, (6, 46): 2.3, (46, 6): 2.2, (6, 47): 1.2, (47, 6): 1.3, 
    (6, 48): 2.5, (48, 6): 2.7, (7, 17): 1.6, (17, 7): 1.5, (7, 20): 2.0, (20, 7): 2.1, 
    (7, 53): 1.8, (53, 7): 1.7, (8, 50): 2.2, (50, 8): 2.4, (8, 51): 1.9, (51, 8): 1.8, 
    (9, 31): 1.5, (31, 9): 1.4, (9, 32): 2.1, (32, 9): 2.3, (9, 47): 1.7, (47, 9): 1.8, 
    (9, 48): 2.5, (48, 9): 2.6, (10, 18): 1.3, (18, 10): 1.2, (10, 28): 2.0, (28, 10): 2.1, 
    (10, 34): 2.4, (34, 10): 2.3, (10, 37): 1.6, (37, 10): 1.7, (10, 45): 1.9, (45, 10): 1.8, 
    (10, 47): 2.2, (47, 10): 2.3, (11, 29): 1.5, (29, 11): 1.4, (11, 30): 2.0, (30, 11): 2.2, 
    (11, 51): 1.8, (51, 11): 1.9, (12, 19): 2.3, (19, 12): 2.2, (12, 31): 1.6, (31, 12): 1.5, 
    (12, 33): 1.3, (33, 12): 1.4, (12, 49): 2.1, (49, 12): 2.0, (14, 27): 1.7, (27, 14): 1.8, 
    (14, 40): 2.4, (40, 14): 2.3, (14, 41): 1.2, (41, 14): 1.3, (15, 21): 2.2, (21, 15): 2.0, 
    (15, 22): 1.5, (22, 15): 1.6, (15, 26): 1.9, (26, 15): 1.8, (15, 42): 2.3, (42, 15): 2.4, 
    (16, 25): 1.4, (25, 16): 1.3, (16, 26): 2.1, (26, 16): 2.2, (16, 42): 1.8, (42, 16): 1.7, 
    (17, 48): 2.5, (48, 17): 2.6, (18, 46): 1.3, (46, 18): 1.2, (18, 47): 2.0, (47, 18): 2.1, 
    (19, 31): 1.6, (31, 19): 1.5, (19, 32): 1.9, (32, 19): 1.8, (19, 33): 2.2, (33, 19): 2.3, 
    (19, 34): 2.7, (34, 19): 2.6, (20, 45): 1.4, (45, 20): 1.3, (20, 53): 2.0, (53, 20): 2.1, 
    (21, 22): 1.7, (22, 21): 1.8, (21, 49): 2.4, (49, 21): 2.2, (22, 23): 1.5, (23, 22): 1.6, 
    (22, 42): 1.9, (42, 22): 1.7, (23, 24): 2.3, (24, 23): 2.2, (23, 35): 1.2, (35, 23): 1.3, 
    (23, 41): 2.0, (41, 23): 2.1, (23, 48): 1.8, (48, 23): 1.7, (24, 35): 1.5, (35, 24): 1.6, 
    (24, 39): 2.4, (39, 24): 2.3, (24, 40): 1.9, (40, 24): 1.8, (24, 41): 1.3, (41, 24): 1.4, 
    (25, 27): 2.2, (27, 25): 2.0, (25, 42): 1.7, (42, 25): 1.6, (25, 52): 2.5, (52, 25): 2.3, 
    (26, 42): 1.4, (42, 26): 1.3, (27, 41): 2.1, (41, 27): 2.0, (27, 52): 1.8, (52, 27): 1.9, 
    (28, 43): 1.6, (43, 28): 1.5, (28, 44): 2.3, (44, 28): 2.2, (28, 45): 1.2, (45, 28): 1.3, 
    (29, 44): 2.0, (44, 29): 2.1, (29, 45): 2.5, (45, 29): 2.4, (29, 51): 1.7, (51, 29): 1.8, 
    (30, 36): 1.3, (36, 30): 1.4, (30, 43): 2.2, (43, 30): 2.3, (30, 44): 1.9, (44, 30): 1.8, 
    (31, 32): 1.5, (32, 31): 1.6, (32, 34): 2.1, (34, 32): 2.2, (32, 47): 1.4, (47, 32): 1.3, 
    (33, 34): 2.3, (34, 33): 2.4, (33, 48): 1.8, (48, 33): 1.7, (33, 49): 1.1, (49, 33): 1.2, 
    (34, 35): 2.0, (35, 34): 2.1, (34, 37): 1.6, (37, 34): 1.5, (34, 38): 2.4, (38, 34): 2.3, 
    (34, 47): 1.9, (47, 34): 1.8, (34, 48): 1.3, (48, 34): 1.2, (35, 38): 2.2, (38, 35): 2.0, 
    (35, 48): 1.5, (48, 35): 1.7, (36, 40): 2.1, (40, 36): 2.3, (37, 43): 1.8, (43, 37): 1.6, 
    (38, 39): 1.2, (39, 38): 1.4, (40, 41): 2.5, (41, 40): 2.3, (43, 44): 1.7, (44, 43): 1.9, 
    (45, 46): 1.3, (46, 45): 1.1, (47, 48): 2.0, (48, 47): 2.2, (50, 53): 1.5, (53, 50): 1.6
}

tu = {
    (1, 8): 4.2, (8, 1): 5.1, (1, 20): 7.3, (20, 1): 6.2, (1, 29): 8.5, (29, 1): 7.2, 
    (1, 45): 9.1, (45, 1): 8.4, (1, 50): 3.9, (50, 1): 4.5, (1, 51): 6.7, (51, 1): 5.8, 
    (1, 53): 7.4, (53, 1): 8.1, (2, 22): 5.2, (22, 2): 4.3, (2, 23): 7.6, (23, 2): 6.5, 
    (2, 33): 3.8, (33, 2): 4.7, (2, 48): 9.3, (48, 2): 8.2, (2, 49): 5.5, (49, 2): 4.6, 
    (3, 22): 3.6, (22, 3): 4.8, (3, 23): 6.3, (23, 3): 7.2, (3, 25): 8.7, (25, 3): 9.4, 
    (3, 27): 5.9, (27, 3): 4.4, (3, 41): 7.5, (41, 3): 6.7, (3, 42): 4.1, (42, 3): 3.7, 
    (4, 5): 5.3, (5, 4): 6.4, (4, 30): 9.2, (30, 4): 8.3, (4, 36): 7.1, (36, 4): 6.9, 
    (4, 37): 4.8, (37, 4): 5.6, (4, 38): 3.5, (38, 4): 4.9, (4, 39): 8.6, (39, 4): 7.7, 
    (4, 40): 5.4, (40, 4): 6.1, (4, 43): 9.5, (43, 4): 8.8, (5, 37): 3.7, (37, 5): 4.2, 
    (5, 38): 7.8, (38, 5): 6.6, (6, 17): 9.7, (17, 6): 8.5, (6, 18): 4.5, (18, 6): 3.9, 
    (6, 20): 6.8, (20, 6): 5.7, (6, 46): 9.4, (46, 6): 8.1, (6, 47): 4.3, (47, 6): 5.2, 
    (6, 48): 7.4, (48, 6): 6.3, (7, 17): 5.8, (17, 7): 4.7, (7, 20): 8.3, (20, 7): 7.2, 
    (7, 53): 4.1, (53, 7): 3.6, (8, 50): 9.8, (50, 8): 8.7, (8, 51): 5.6, (51, 8): 4.4, 
    (9, 31): 3.8, (31, 9): 4.9, (9, 32): 7.5, (32, 9): 6.2, (9, 47): 5.3, (47, 9): 4.6, 
    (9, 48): 9.1, (48, 9): 8.4, (10, 18): 3.7, (18, 10): 4.5, (10, 28): 7.9, (28, 10): 6.8, 
    (10, 34): 9.6, (34, 10): 8.3, (10, 37): 4.2, (37, 10): 5.5, (10, 45): 6.7, (45, 10): 7.4, 
    (10, 47): 8.9, (47, 10): 9.2, (11, 29): 4.7, (29, 11): 3.8, (11, 30): 7.3, (30, 11): 6.5, 
    (11, 51): 5.9, (51, 11): 4.8, (12, 19): 9.3, (19, 12): 8.6, (12, 31): 4.5, (31, 12): 3.7, 
    (12, 33): 6.4, (33, 12): 5.3, (12, 49): 8.2, (49, 12): 7.1, (14, 27): 5.7, (27, 14): 4.9, 
    (14, 40): 9.4, (40, 14): 8.5, (14, 41): 3.6, (41, 14): 4.4, (15, 21): 7.8, (21, 15): 6.6, 
    (15, 22): 4.3, (22, 15): 5.1, (15, 26): 6.9, (26, 15): 5.8, (15, 42): 9.5, (42, 15): 8.7, 
    (16, 25): 3.9, (25, 16): 4.8, (16, 26): 7.2, (26, 16): 6.3, (16, 42): 5.4, (42, 16): 4.5, 
    (17, 48): 8.8, (48, 17): 9.6, (18, 46): 3.5, (46, 18): 4.7, (18, 47): 7.6, (47, 18): 6.9, 
    (19, 31): 5.2, (31, 19): 4.3, (19, 32): 8.4, (32, 19): 7.5, (19, 33): 9.1, (33, 19): 8.2, 
    (19, 34): 6.7, (34, 19): 5.8, (20, 45): 3.4, (45, 20): 4.6, (20, 53): 7.9, (53, 20): 6.8, 
    (21, 22): 5.5, (22, 21): 4.2, (21, 49): 9.7, (49, 21): 8.3, (22, 23): 3.8, (23, 22): 4.9, 
    (22, 42): 6.6, (42, 22): 5.7, (23, 24): 9.2, (24, 23): 8.4, (23, 35): 4.5, (35, 23): 3.6, 
    (23, 41): 7.7, (41, 23): 6.8, (23, 48): 5.1, (48, 23): 4.3, (24, 35): 8.5, (35, 24): 9.3, 
    (24, 39): 6.4, (39, 24): 5.5, (24, 40): 3.9, (40, 24): 4.8, (24, 41): 7.1, (41, 24): 6.2, 
    (25, 27): 5.6, (27, 25): 4.7, (25, 42): 9.4, (42, 25): 8.5, (25, 52): 7.3, (52, 25): 6.4, 
    (26, 42): 3.7, (42, 26): 4.9, (27, 41): 8.6, (41, 27): 7.7, (27, 52): 5.2, (52, 27): 4.3, 
    (28, 43): 6.8, (43, 28): 5.9, (28, 44): 9.8, (44, 28): 8.9, (28, 45): 4.6, (45, 28): 3.5, 
    (29, 44): 7.4, (44, 29): 6.5, (29, 45): 9.9, (45, 29): 8.8, (29, 51): 5.3, (51, 29): 4.4, 
    (30, 36): 3.8, (36, 30): 4.7, (30, 43): 8.2, (43, 30): 7.3, (30, 44): 6.1, (44, 30): 5.2, 
    (31, 32): 4.8, (32, 31): 3.9, (32, 34): 9.5, (34, 32): 8.6, (32, 47): 5.7, (47, 32): 4.8, 
    (33, 34): 7.9, (34, 33): 6.8, (33, 48): 3.6, (48, 33): 4.5, (33, 49): 8.4, (49, 33): 7.5, 
    (34, 35): 5.3, (35, 34): 4.2, (34, 37): 9.6, (37, 34): 8.7, (34, 38): 7.1, (38, 34): 6.3, 
    (34, 47): 3.8, (47, 34): 4.9, (34, 48): 6.7, (48, 34): 5.8, (35, 38): 9.2, (38, 35): 8.3, 
    (35, 48): 4.4, (48, 35): 3.5, (36, 40): 7.6, (40, 36): 6.7, (37, 43): 5.9, (43, 37): 4.8, 
    (38, 39): 8.7, (39, 38): 9.5, (40, 41): 6.2, (41, 40): 5.3, (43, 44): 4.1, (44, 43): 3.6, 
    (45, 46): 7.8, (46, 45): 6.9, (47, 48): 5.5, (48, 47): 4.7, (50, 53): 9.3, (53, 50): 8.4
}

# ---------- 构建邻接矩阵 ----------
def build_adjacency_matrix(time_scenario='worst'):
    """构建邻接矩阵，用于Dijkstra算法
    
    参数:
        time_scenario: 'worst'使用最坏情况(tu)，'best'使用最好情况(tl)
    """
    n = max(N) + 1  # 最大节点编号+1
    adj_matrix = np.full((n, n), float('inf'))
    
    # 设置邻接矩阵
    for i, j in edges:
        # 根据情景选择时间
        if time_scenario == 'worst':
            adj_matrix[i, j] = tu[i, j]  # 使用最大行程时间作为权重
        else:
            adj_matrix[i, j] = tl[i, j]  # 使用最小行程时间作为权重
    
    # 对角线设为0
    for i in range(n):
        adj_matrix[i, i] = 0
    
    return adj_matrix

# ---------- Dijkstra算法计算最短路径 ----------
def dijkstra(adj_matrix, start):
    """Dijkstra算法计算从起点到所有其他点的最短路径"""
    n = len(adj_matrix)
    dist = [float('inf')] * n
    prev = [-1] * n
    visited = [False] * n
    
    dist[start] = 0
    
    for _ in range(n):
        # 找到当前距离最小的未访问节点
        min_dist = float('inf')
        min_node = -1
        for i in range(n):
            if not visited[i] and dist[i] < min_dist:
                min_dist = dist[i]
                min_node = i
        
        if min_node == -1:
            break
        
        visited[min_node] = True
        
        # 更新相邻节点的距离
        for i in range(n):
            if not visited[i] and adj_matrix[min_node, i] != float('inf'):
                new_dist = dist[min_node] + adj_matrix[min_node, i]
                if new_dist < dist[i]:
                    dist[i] = new_dist
                    prev[i] = min_node
    
    # 构建路径
    paths = {}
    for i in range(n):
        if i != start and dist[i] != float('inf'):
            path = []
            current = i
            while current != -1:
                path.append(current)
                current = prev[current]
            path.reverse()
            paths[i] = (path, dist[i])
    
    return paths

# ---------- 评估方案在不同情景下的行程时间 ----------
def evaluate_solution(routes, depots, time_scenario='worst'):
    """评估给定方案在指定情景下的总行程时间
    
    参数:
        routes: 每辆车的路径字典
        depots: 每辆车的起点字典
        time_scenario: 'worst'使用最坏情况(tu)，'best'使用最好情况(tl)
    
    返回:
        总行程时间
    """
    # 选择时间区间
    time_dict = tu if time_scenario == 'worst' else tl
    
    total_time = 0
    
    for k in routes:
        route = routes[k]
        if not route:
            continue
        
        # 重建连续的路径
        full_path = [depots[k]]  # 起点
        full_path.extend(route)  # 添加路径中的所有点
        
        time = 0
        
        # 计算相邻节点之间的时间
        for i in range(len(full_path) - 1):
            from_node = full_path[i]
            to_node = full_path[i + 1]
            
            # 跳过从节点到自身的连接
            if from_node == to_node:
                continue
                
            # 计算从from_node到to_node的时间
            if (from_node, to_node) in time_dict:
                time += time_dict[(from_node, to_node)]
            else:
                # 如果没有直接连接，可能是路径有问题
                print(f"警告: 节点{from_node}到{to_node}没有直接连接")
                
        total_time += time
        
    return total_time

# ---------- 贪心算法求解VRP ----------
def greedy_vrp():
    """使用贪心算法求解VRP问题"""
    print("使用贪心算法求解VRP问题...")
    start_time = time.time()
    
    # 构建邻接矩阵
    adj_matrix = build_adjacency_matrix()
    
    # 计算每个起点到每个需求点的最短路径
    paths_from_depots = {}
    for s in S:
        paths_from_depots[s] = dijkstra(adj_matrix, s)
    
    # 计算需求点之间的最短路径
    paths_between_demands = {}
    for m in M:
        paths_between_demands[m] = dijkstra(adj_matrix, m)
    
    # 初始化解
    unserved = set(M)  # 未服务的需求点
    routes = {k: [] for k in K}  # 每辆车的路径
    loads = {k: Q for k in K}  # 每辆车的剩余负载
    times = {k: 0 for k in K}  # 每辆车的行程时间
    # 固定分配起点
    depots = {
        0: 1,  # 0号车从节点1出发
        1: 1,  # 1号车从节点1出发
        2: 2,  # 2号车从节点2出发
        3: 2,  # 3号车从节点2出发
        4: 3,  # 4号车从节点3出发
        5: 3   # 5号车从节点3出发
    }
    served_by_vehicle = {k: set() for k in K}  # 每辆车服务的需求点
    
    # 贪心分配
    while unserved:
        best_cost = float('inf')
        best_k = None
        best_m = None
        
        # 第一轮：确保每辆车都有分配任务
        empty_vehicles = [k for k in K if not routes[k]]
        if empty_vehicles and len(unserved) >= len(empty_vehicles):
            # 优先为空车分配需求点
            for k in empty_vehicles:
                min_cost = float('inf')
                best_point = None
                for m in unserved:
                    if p[m] <= loads[k]:  # 确保车辆有足够的负载
                        path, cost = paths_from_depots[depots[k]].get(m, ([], float('inf')))
                        if cost < min_cost:
                            min_cost = cost
                            best_point = m
                
                if best_point:
                    path, _ = paths_from_depots[depots[k]][best_point]
                    routes[k].extend(path)
                    loads[k] -= p[best_point]
                    times[k] += min_cost
                    served_by_vehicle[k].add(best_point)
                    unserved.remove(best_point)
            
            # 如果已经服务了所有点，则退出循环
            if not unserved:
                break
                
            # 重新开始循环，不选择最佳车辆和需求点
            continue
        
        # 正常的贪心选择过程
        for k in K:
            if not routes[k]:  # 如果车辆k还没有分配路径
                # 计算从起点到每个未服务需求点的成本
                for m in unserved:
                    if p[m] <= loads[k]:  # 严格确保车辆有足够的负载
                        path, cost = paths_from_depots[depots[k]].get(m, ([], float('inf')))
                        if cost < best_cost:
                            best_cost = cost
                            best_k = k
                            best_m = m
            else:
                # 计算从当前路径的最后一个点到每个未服务需求点的成本
                last_node = routes[k][-1]
                for m in unserved:
                    if p[m] <= loads[k]:  # 严格确保车辆有足够的负载
                        path, cost = paths_between_demands[last_node].get(m, ([], float('inf')))
                        if cost < best_cost:
                            best_cost = cost
                            best_k = k
                            best_m = m
        
        if best_k is None:  # 无法分配更多需求点
            print(f"无法分配更多需求点，还剩 {len(unserved)} 个未服务的需求点")
            break
        
        # 更新路径
        if not routes[best_k]:
            # 从起点到第一个需求点的路径
            path, _ = paths_from_depots[depots[best_k]][best_m]
            routes[best_k].extend(path)
        else:
            # 从上一个需求点到下一个需求点的路径（排除起点）
            path, _ = paths_between_demands[routes[best_k][-1]][best_m]
            routes[best_k].extend(path[1:])  # 排除起点，因为已经在路径中
        
        # 更新负载、时间和服务点
        loads[best_k] -= p[best_m]
        times[best_k] += best_cost
        served_by_vehicle[best_k].add(best_m)
        
        # 将需求点标记为已服务
        unserved.remove(best_m)
    
    # 输出结果
    total_time = 0
    total_demand_served = 0
    
    print("\n贪心算法求解结果:")
    for k in K:
        if routes[k]:
            served_points = served_by_vehicle[k]
            remaining_load = Q
            demands_served = []
            
            # 计算实际消耗的物资
            for node in served_points:
                remaining_load -= p[node]
                demands_served.append((node, p[node]))
            
            # 生成路径字符串
            path_output = []
            path_output.append(f"  起点({depots[k]})[物资:{Q}kg]")
            
            # 打印路径信息
            current_load = Q
            for node in routes[k][1:]:  # 跳过起点
                if node in M:
                    if node in served_points:  # 如果是服务点
                        current_load -= p[node]
                        path_output.append(f"需求点({node})*[需求:{p[node]}kg,剩余:{current_load}kg]")
                    else:
                        path_output.append(f"需求点({node})[剩余:{current_load}kg]")
                else:
                    if node == depots[k]:  # 如果是返回集散中心
                        path_output.append(f"返回起点({node})[剩余:{current_load}kg]")
                    else:
                        path_output.append(f"{node}[剩余:{current_load}kg]")
            
            # 打印路径
            print(f"\n车辆 {k} 的路径:")
            print(" -> ".join(path_output))
            
            # 打印服务详情
            print(f"  服务的受灾点({len(served_points)}个): {sorted(served_points)}")
            print(f"  总配送物资: {sum(p[j] for j in served_points)}kg")
            print(f"  剩余物资: {remaining_load}kg")
            print(f"  行程时间: {times[k]:.2f}")
            print(f"  配送详情:")
            for node, demand in sorted(demands_served):
                print(f"    需求点 {node}: {demand}kg")
            print(f"  经过的节点总数: {len(routes[k])-1}")
            
            total_time += times[k]
            total_demand_served += sum(p[j] for j in served_points)
    
    # 检查是否所有需求点都被服务
    all_served = set()
    for k in K:
        all_served.update(served_by_vehicle[k])
    
    unserved_points = set(M) - all_served
    
    print("\n总结:")
    print(f"总行程时间: {total_time:.2f}")
    print(f"总需求量: {sum(p.values())}kg")
    print(f"总配送量: {total_demand_served}kg")
    print(f"未服务的需求点数量: {len(unserved_points)}")
    if unserved_points:
        print(f"未服务的需求点: {sorted(unserved_points)}")
    
    print(f"\n贪心算法求解时间: {time.time() - start_time:.2f}秒")

def min_max_regret_vrp():
    """使用最小最大后悔值算法求解VRP问题"""
    print("使用最小最大后悔值算法求解VRP问题...")
    start_time = time.time()
    
    # 固定分配起点
    depots = {
        0: 1,  # 0号车从节点1出发
        1: 1,  # 1号车从节点1出发
        2: 2,  # 2号车从节点2出发
        3: 2,  # 3号车从节点2出发
        4: 3,  # 4号车从节点3出发
        5: 3   # 5号车从节点3出发
    }
    
    # 生成多个候选方案
    num_solutions = 10
    candidate_solutions = []
    
    for solution_idx in range(num_solutions):
        print(f"生成第{solution_idx+1}个候选方案...")
        
        # 在最坏情景下构建邻接矩阵
        worst_adj_matrix = build_adjacency_matrix('worst')
        
        # 计算每个起点到每个需求点的最短路径
        paths_from_depots = {}
        for s in S:
            paths_from_depots[s] = dijkstra(worst_adj_matrix, s)
        
        # 计算需求点之间的最短路径
        paths_between_demands = {}
        for m in M:
            paths_between_demands[m] = dijkstra(worst_adj_matrix, m)
        
        # 初始化解
        unserved = set(M)  # 未服务的需求点
        routes = {k: [] for k in K}  # 每辆车的路径
        loads = {k: Q for k in K}  # 每辆车的剩余负载
        times = {k: 0 for k in K}  # 每辆车的行程时间
        served_by_vehicle = {k: set() for k in K}  # 每辆车服务的需求点
        
        # 在不同方案中引入随机性
        demand_points = list(unserved)
        random.shuffle(demand_points)
        
        # 先为每辆车分配一个需求点(如果可能)
        for k in K:
            if not demand_points:
                break
                
            for i, m in enumerate(demand_points):
                if p[m] <= loads[k]:  # 确保车辆有足够的负载
                    path, cost = paths_from_depots[depots[k]].get(m, ([], float('inf')))
                    if path:  # 如果存在路径
                        routes[k].extend(path)
                        loads[k] -= p[m]
                        times[k] += cost
                        served_by_vehicle[k].add(m)
                        unserved.remove(m)
                        demand_points.pop(i)
                        break
        
        # 贪心分配剩余需求点
        while unserved:
            best_cost = float('inf')
            best_k = None
            best_m = None
            
            # 随机选择一部分未服务的需求点来考虑，引入多样性
            candidate_demands = random.sample(list(unserved), min(5, len(unserved)))
            
            for k in K:
                if not routes[k]:
                    continue  # 跳过没有分配路径的车辆
                
                # 从当前路径的最后一个点到候选需求点
                last_node = routes[k][-1]
                for m in candidate_demands:
                    if p[m] <= loads[k]:  # 确保车辆有足够的负载
                        path, cost = paths_between_demands[last_node].get(m, ([], float('inf')))
                        
                        # 计算从需求点m返回集散中心的时间
                        return_path, return_cost = paths_between_demands[m].get(depots[k], ([], float('inf')))
                        
                        # 考虑Tmax限制，确保加上返回时间不超过最大行程时间
                        total_time = times[k] + cost + return_cost
                        if total_time <= Tmax:
                            # 添加随机扰动，使不同方案有差异
                            cost_with_noise = cost * (0.9 + 0.2 * random.random())
                            if cost_with_noise < best_cost:
                                best_cost = cost_with_noise
                                best_k = k
                                best_m = m
            
            if best_k is None:  # 无法分配更多需求点
                # 尝试分配给任何有容量的车辆，同时考虑Tmax限制
                for k in K:
                    for m in unserved:
                        if p[m] <= loads[k]:
                            if not routes[k]:  # 如果车辆k还没有分配路径
                                path, cost = paths_from_depots[depots[k]].get(m, ([], float('inf')))
                                
                                # 计算从需求点m返回集散中心的时间
                                return_path, return_cost = paths_between_demands[m].get(depots[k], ([], float('inf')))
                                
                                # 考虑Tmax限制
                                if cost + return_cost <= Tmax and path:
                                    best_k = k
                                    best_m = m
                                    break
                            else:
                                last_node = routes[k][-1]
                                path, cost = paths_between_demands[last_node].get(m, ([], float('inf')))
                                
                                # 计算从需求点m返回集散中心的时间
                                return_path, return_cost = paths_between_demands[m].get(depots[k], ([], float('inf')))
                                
                                # 考虑Tmax限制
                                total_time = times[k] + cost + return_cost
                                if total_time <= Tmax and path:
                                    best_k = k
                                    best_m = m
                                    break
                    if best_k is not None:
                        break
                        
                if best_k is None:
                    print(f"无法分配更多需求点，还剩 {len(unserved)} 个未服务的需求点")
                    break
            
            # 更新路径
            if not routes[best_k]:
                # 从起点到第一个需求点的路径
                path, _ = paths_from_depots[depots[best_k]][best_m]
                routes[best_k].extend(path)
            else:
                # 从上一个需求点到下一个需求点的路径（排除起点）
                path, _ = paths_between_demands[routes[best_k][-1]][best_m]
                routes[best_k].extend(path[1:])  # 排除起点，因为已经在路径中
            
            # 更新负载、时间和服务点
            loads[best_k] -= p[best_m]
            times[best_k] += best_cost
            served_by_vehicle[best_k].add(best_m)
            
            # 将需求点标记为已服务
            unserved.remove(best_m)
        
        # 如果有未服务的需求点，这个方案不完整，跳过
        if unserved:
            print(f"方案{solution_idx+1}不完整，有{len(unserved)}个未服务的需求点")
            continue
            
        # 保存当前方案
        candidate_solutions.append({
            'routes': {k: routes[k][:] for k in K},  # 深拷贝路径
            'served_by_vehicle': {k: served_by_vehicle[k].copy() for k in K},  # 深拷贝服务点
            'depots': depots.copy()  # 深拷贝起点
        })
        
        print(f"方案{solution_idx+1}完成，服务了所有{len(M)}个需求点")
    
    # 计算每个方案在最坏情景下的行程时间
    print("\n评估候选方案...")
    worst_case_times = []
    
    # 在最坏情景下构建邻接矩阵(用于查找返回路径)
    worst_adj_matrix = build_adjacency_matrix('worst')
    
    for i, solution in enumerate(candidate_solutions):
        # 确保每辆车返回集散中心，使用合法路径
        for k in solution['routes']:
            if solution['routes'][k]:  # 如果路径不为空
                last_node = solution['routes'][k][-1]
                depot = solution['depots'][k]
                
                # 只有当最后一个节点不是集散中心时，才需要添加返回路径
                if last_node != depot:
                    # 使用Dijkstra算法查找从最后一个节点到集散中心的路径
                    paths = dijkstra(worst_adj_matrix, last_node)
                    if depot in paths:
                        return_path, _ = paths[depot]
                        # 添加返回路径(除了起点，因为已经在路径中)
                        solution['routes'][k].extend(return_path[1:])
                    else:
                        print(f"警告: 无法找到从节点{last_node}到集散中心{depot}的路径")
                
        worst_time = evaluate_solution(solution['routes'], solution['depots'], 'worst')
        worst_case_times.append(worst_time)
        print(f"方案{i+1}在最坏情景下的总行程时间: {worst_time:.2f}")
    
    # 找到最坏情景下的最优解作为基准
    best_worst_time = min(worst_case_times) if worst_case_times else float('inf')
    print(f"\n在最坏情景下的最优总行程时间: {best_worst_time:.2f}")
    
    # 计算每个方案的后悔值 (最坏情景下的总行程时间-最佳情景下的总行程时间)
    regret_values = []
    best_case_times = []
    
    # 计算每个方案在最佳情景下的行程时间
    for i, solution in enumerate(candidate_solutions):
        best_time = evaluate_solution(solution['routes'], solution['depots'], 'best')
        best_case_times.append(best_time)
        print(f"方案{i+1}在最佳情景下的总行程时间: {best_time:.2f}")
    
    # 计算每个方案的后悔值
    for i, (worst_time, best_time) in enumerate(zip(worst_case_times, best_case_times)):
        regret = worst_time - best_time
        regret_values.append(regret)
        print(f"方案{i+1}的后悔值: {regret:.2f}")
    
    # 找到后悔值最小的方案 (最小化最坏和最好情景间的差距)
    if regret_values:
        min_regret_idx = regret_values.index(min(regret_values))
        best_solution = candidate_solutions[min_regret_idx]
        
        print(f"\n最小最大后悔值的方案是方案{min_regret_idx+1}，后悔值为: {regret_values[min_regret_idx]:.2f}")
        print(f"该方案在最坏情景下的总行程时间: {worst_case_times[min_regret_idx]:.2f}")
        print(f"该方案在最佳情景下的总行程时间: {best_case_times[min_regret_idx]:.2f}")
        
        # 输出最佳方案详情
        routes = best_solution['routes']
        depots = best_solution['depots']
        served_by_vehicle = best_solution['served_by_vehicle']
        
        total_time_worst = evaluate_solution(routes, depots, 'worst')
        total_time_best = evaluate_solution(routes, depots, 'best')
        
        print("\n最佳方案详情:")
        for k in K:
            if routes[k]:
                served_points = served_by_vehicle[k]
                remaining_load = Q
                demands_served = []
                
                # 计算实际消耗的物资
                for node in served_points:
                    remaining_load -= p[node]
                    demands_served.append((node, p[node]))
                
                # 生成路径字符串
                path_output = []
                path_output.append(f"  起点({depots[k]})[物资:{Q}kg]")
                
                # 打印路径信息
                current_load = Q
                for node in routes[k][1:]:  # 跳过起点
                    if node in M:
                        if node in served_points:  # 如果是服务点
                            current_load -= p[node]
                            path_output.append(f"需求点({node})*[需求:{p[node]}kg,剩余:{current_load}kg]")
                        else:
                            path_output.append(f"需求点({node})[剩余:{current_load}kg]")
                    else:
                        if node == depots[k]:  # 如果是返回集散中心
                            path_output.append(f"返回起点({node})[剩余:{current_load}kg]")
                        else:
                            path_output.append(f"{node}[剩余:{current_load}kg]")
                
                # 打印路径
                print(f"\n车辆 {k} 的路径:")
                print(" -> ".join(path_output))
                
                # 打印服务详情
                print(f"  服务的受灾点({len(served_points)}个): {sorted(served_points)}")
                print(f"  总配送物资: {sum(p[j] for j in served_points)}kg")
                print(f"  剩余物资: {remaining_load}kg")
                print(f"  行程时间(最坏情景): {evaluate_solution({k: routes[k]}, {k: depots[k]}, 'worst'):.2f}")
                print(f"  行程时间(最佳情景): {evaluate_solution({k: routes[k]}, {k: depots[k]}, 'best'):.2f}")
                print(f"  配送详情:")
                for node, demand in sorted(demands_served):
                    print(f"    需求点 {node}: {demand}kg")
        
        print("\n总结:")
        print(f"最坏情景下的总行程时间: {total_time_worst:.2f}")
        print(f"最佳情景下的总行程时间: {total_time_best:.2f}")
        print(f"后悔值: {regret_values[min_regret_idx]:.2f}")
    else:
        print("没有找到可行的方案")
    
    print(f"\n最小最大后悔值算法求解时间: {time.time() - start_time:.2f}秒")

if __name__ == "__main__":
    # 设置随机种子，使结果可重现
    random.seed(42)
    
    # 运行贪心算法
    # greedy_vrp()
    
    # 运行最小最大后悔值算法
    min_max_regret_vrp() 