import numpy as np
import heapq
import time

# 数据定义
nodes = np.arange(1, 30)  # 29个节点
edges = np.array([
    [1, 2], [1, 3], [2, 3], [1, 4], [3, 4], [1, 5], [4, 5], [2, 6], [3, 6], [3, 7],
    [6, 7], [3, 8], [4, 8], [7, 8], [4, 9], [8, 9], [4, 10], [5, 10], [9, 10], [6, 11],
    [7, 11], [7, 12], [11, 12], [7, 13], [8, 13], [12, 13], [8, 14], [9, 14], [13, 14],
    [9, 15], [10, 15], [14, 15], [10, 16], [15, 16], [11, 17], [12, 17], [12, 18], [13, 18],
    [17, 18], [13, 19], [14, 19], [18, 19], [14, 20], [15, 20], [19, 20], [15, 21], [16, 21],
    [20, 21], [17, 22], [18, 22], [18, 23], [19, 23], [22, 23], [19, 24], [20, 24], [23, 24],
    [20, 25], [21, 25], [24, 25], [22, 26], [23, 26], [23, 27], [24, 27], [26, 27], [24, 28],
    [25, 28], [27, 28], [26, 29], [27, 29], [28, 29]
])

# 边的下界和上界阻抗（需要与边的数量一致）
l = np.array([6, 6, 6, 5, 6, 3, 1, 6, 6, 6, 6, 6, 6, 6, 3, 6, 6, 3, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 3, 6, 6, 3, 6, 6,
     6, 6, 6, 6, 6, 6, 6, 6, 4, 6, 6, 3, 6, 6, 6, 6, 6, 6, 6, 5, 6, 6, 3, 6, 6, 6, 6, 4, 2, 6, 3, 6, 9, 2, 3])
u = np.array([10, 8, 10, 9, 10, 5, 4, 10, 10, 8, 10, 10, 8, 10, 9, 10, 10, 10, 10, 10, 10, 8, 10, 10, 8, 10, 10, 8, 10,
     9, 12, 10, 10, 10, 10, 10, 8, 8, 10, 10, 8, 10, 10, 9, 10, 10, 10, 10, 10, 10, 8, 8, 10, 8, 10, 10, 10,
     11, 10, 10, 10, 8, 9, 3, 10, 10, 10, 13, 4, 13])


# 起点和终点
start_node = 1
end_node = 29

# 为了后续找出上界阻抗总和、未经过路段的最小下界阻抗
def find_edge(node1, node2, edges):
    index = np.where((edges[:, 0] == node1) & (edges[:, 1] == node2))
    return index[0][0] if index[0].size > 0 else None

def branch_and_bound(nodes, edges, l, u, start, end):
    optimal_path = []
    R_ort = float('inf')

    def search(current_node, current_path, current_cost):
        nonlocal optimal_path, R_ort

        # 检查是否到达终点
        if current_node == end:
            R_j = compute_robust_cost(current_path, edges, l, u)
            if R_j < R_ort:
                R_ort = R_j
                optimal_path = current_path
            return

        # 遍历相邻节点
        neighbors = get_neighbors(current_node, edges)
        for next_node in neighbors:
            R_ri = compute_robust_cost(current_path + [next_node], edges, l, u)
            if R_ri <= R_ort:
                search(next_node, current_path + [next_node], R_ri)

    # 启动搜索
    search(start, [start], 0)
    return optimal_path, R_ort

def compute_robust_cost(path, edges, l, u):
    max_cost = 0

    # 累加当前路径的上界阻抗
    for i in range(len(path) - 1):
        edge_index = find_edge(path[i], path[i + 1], edges)
        if edge_index is not None:
            max_cost += u[edge_index]  # 累加上界阻抗

    # 找到未经过的路段并计算最小下界阻抗
    visited_edges = set()
    for i in range(len(path) - 1):
        visited_edges.add(find_edge(path[i], path[i + 1], edges))

    # 创建未经过的边的列表
    unused_edges = []
    for i in range(len(edges)):
        if i not in visited_edges:
            unused_edges.append(i)

    # 使用Dijkstra算法或其他方法找出未经过的路段组成的最小下界阻抗路径
    min_cost = dijkstra_min_cost(unused_edges, edges, l)

    return max_cost - min_cost # 返回鲁棒成本

def dijkstra_min_cost(unused_edges, edges, l, start_node=1, end_node=29):
    # 创建图的邻接列表
    graph = {}
    for edge in unused_edges:
        node1, node2 = edges[edge]
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        graph[node1].append((node2, l[edge]))  # (相邻节点, 下界阻抗)
        graph[node2].append((node1, l[edge]))  # 双向

    # Dijkstra 算法初始化
    min_heap = [(0, start_node)]  # (成本, 当前节点)
    min_cost = {node: float('inf') for node in graph}
    min_cost[start_node] = 0

    while min_heap:
        current_cost, current_node = heapq.heappop(min_heap)

        # 如果到达终点，返回当前成本
        if current_node == end_node:
            return current_cost

        # 遍历相邻节点
        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor))

    # 如果无法到达终点，返回无限大
    return float('inf')

def get_neighbors(node, edges):
    return np.unique(edges[edges[:, 0] == node][:, 1])

def find_edge(node1, node2, edges):
    index = np.where((edges[:, 0] == node1) & (edges[:, 1] == node2))
    return index[0][0] if index[0].size > 0 else None

# 记录整个程序的运行时间
start_time = time.time()

# 调用算法求解-最优路径，最小鲁棒成本
optimal_path, robust_cost = branch_and_bound(nodes, edges, l, u, start_node, end_node)
# 求上界阻抗总和
max_cost = sum(u[find_edge(optimal_path[i], optimal_path[i + 1], edges)] for i in range(len(optimal_path) - 1))
# 找到未经过的边
visited_edges = set(find_edge(optimal_path[i], optimal_path[i + 1], edges) for i in range(len(optimal_path) - 1))
unused_edges = [i for i in range(len(edges)) if i not in visited_edges]
# 使用Dijkstra算法计算未经过路段的最小下界阻抗
min_cost = dijkstra_min_cost(unused_edges, edges, l)
# 求运行时间
end_time = time.time()

# 输出结果
print("最优路径:", [int(node) for node in optimal_path])
print("最小鲁棒成本:", robust_cost)
print("上界阻抗总和:", max_cost)
print("未经过路段的最小下界阻抗:", min_cost)
print("运行时间:", end_time - start_time, "秒")

