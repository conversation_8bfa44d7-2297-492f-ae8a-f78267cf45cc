import numpy as np
import random
import heapq
import time

# 去掉这两行，结果就和分开的一样了
np.random.seed(42)  # 或者任何整数
random.seed(42)  # 同样设置 Python 的 random 模块

# 数据定义
nodes = np.arange(1, 27)  # 26个节点
edges = np.array([
    [1, 2], [1, 3], [1, 4], [1, 5],
    [2, 3], [2, 6], [2, 11],
    [3, 4], [3, 6], [3, 7],
    [4, 5], [4, 7], [4, 8], [4, 9],
    [5, 9], [5, 10],
    [6, 7], [6, 11], [6, 12], [6, 13],
    [7, 8], [7, 13],
    [8, 9], [8, 13], [8, 14],
    [9, 10], [9, 14], [9, 15],
    [10, 15], [10, 16],
    [11, 12],
    [12, 13], [12, 17],
    [13, 14], [13, 17], [13, 18],
    [14, 15], [14, 18], [14, 19],
    [15, 16], [15, 19], [15, 20],
    [16, 20], [16, 21],
    [17, 18], [17, 22],
    [18, 19], [18, 22], [18, 23],
    [19, 20], [19, 23], [19, 24],
    [20, 21], [20, 24],
    [21, 24], [21, 25],
    [22, 23], [22, 26],
    [23, 24], [23, 26],
    [24, 25], [24, 26],
    [25, 26]
])

# 边的下界和上界阻抗
l = np.array([0.2, 1, 0.9, 0.2, 0.9, 0.8, 1.5, 1.1, 1.5, 0.8, 0.1, 1.1, 0.5, 1.1, 0.9, 0.5, 0.7, 0.7, 1.4,
              0.9, 0.2, 0.4, 0.6, 1.1, 0.6, 0.7, 0.7, 1.2, 1.1, 1, 0.7, 0.3, 0.4, 1.2, 1.5, 0.9, 0.5, 0.9,
              0.6, 1.1, 1, 1, 1.2, 0.8, 1.2, 1, 0.5, 0.5, 0.6, 0.3, 0.9, 0.2, 1.1, 0.5, 1.2, 1.1, 0.7,
              0.9, 2, 0.8, 0.5, 0.5, 2.4])
u = np.array([4, 2, 1.8, 2.1, 1.7, 2.1, 3.2, 1.7, 2.1, 1.5, 1.8, 1.3, 1.3, 2.4, 1.5, 1.5, 1.2, 2.1, 2.3,
              1, 1.5, 2.5, 1.6, 3.5, 1.7, 1.4, 3, 1.8, 1.7, 2.4, 2.1, 1.5, 1.9, 1.9, 3.2, 1.9, 2.5, 2.5,
              2.1, 1.6, 2.8, 1.7, 2.7, 2.2, 2.5, 1.6, 1.5, 1.7, 2.2, 2.3, 2.5, 2.6, 2.4, 2.2, 2.8, 2.7, 1.8,
              2.8, 2.2, 2.3, 1.4, 3.8, 4.4])

# 起点和终点
start_node = 1
end_node = 26

# 获取与指定节点相邻的所有节点
def get_neighbors(node, edges):
    return np.unique(edges[edges[:, 0] == node][:, 1])

# 查找图中连接两个节点的边的索引
def find_edge(node1, node2, edges):
    index = np.where((edges[:, 0] == node1) & (edges[:, 1] == node2))
    return index[0][0] if index[0].size > 0 else None


# 蚁群算法参数
num_ants = 50
num_iterations = 100
alpha = 1.0  # 信息素的重要程度
beta = 2.0  # 启发函数的重要程度
rho = 0.1  # 信息素挥发率
Q = 100  # 信息素强度

# 初始化信息素矩阵
pheromones = np.ones(len(edges))  # 初始信息素
# 构造路径
def construct_solution(pheromones, nodes, edges, l, u):
    current_node = start_node
    path = [current_node]

    while current_node != end_node:
        neighbors = get_neighbors(current_node, edges)
        if len(neighbors) == 0:
            break

        # 计算每个邻居节点的选择概率
        probabilities = []
        for neighbor in neighbors:
            edge_index = find_edge(current_node, neighbor, edges)
            if edge_index is not None:
                tau = pheromones[edge_index] ** alpha
                eta = (1 / u[edge_index]) ** beta  # 启发函数
                probabilities.append(tau * eta)
            else:
                probabilities.append(0)

        # 归一化概率
        probabilities = np.array(probabilities)
        probabilities /= np.sum(probabilities)

        # 按概率选择下一个节点
        next_node = np.random.choice(neighbors, p=probabilities)
        path.append(next_node)
        current_node = next_node

    return path
# 更新信息素
def update_pheromones(paths, costs):
    global pheromones
    pheromones *= (1 - rho)  # 挥发信息素

    for path, cost in zip(paths, costs):
        for i in range(len(path) - 1):
            edge_index = find_edge(path[i], path[i + 1], edges)
            if edge_index is not None:
                pheromones[edge_index] += Q / cost  # 更新信息素

# 计算鲁棒成本（复用之前的函数）
def compute_robust_cost(path, edges, l, u):
    max_cost = 0
    for i in range(len(path) - 1):
        edge_index = find_edge(path[i], path[i + 1], edges)
        if edge_index is not None:
            max_cost += u[edge_index]

    visited_edges = set(find_edge(path[i], path[i + 1], edges) for i in range(len(path) - 1))
    unused_edges = [i for i in range(len(edges)) if i not in visited_edges]

    min_cost = dijkstra_min_cost(unused_edges, edges, l)
    return max_cost - min_cost
def dijkstra_min_cost(unused_edges, edges, l, start_node=1, end_node=26):
    # 创建图的邻接列表
    graph = {}
    for edge in unused_edges:
        node1, node2 = edges[edge]
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        graph[node1].append((node2, l[edge]))  # (相邻节点, 下界阻抗)
        graph[node2].append((node1, l[edge]))  # 双向

    # Dijkstra 算法初始化
    min_heap = [(0, start_node)]  # (成本, 当前节点)
    min_cost = {node: float('inf') for node in graph}
    min_cost[start_node] = 0

    while min_heap:
        current_cost, current_node = heapq.heappop(min_heap)

        # 如果到达终点，返回当前成本
        if current_node == end_node:
            return current_cost

        # 遍历相邻节点
        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor))

    # 如果无法到达终点，返回无限大
    return float('inf')

def find_shortest_path(edges, l, u, start_node, end_node, cost_type='lower'):
    graph = {}
    for i, (node1, node2) in enumerate(edges):
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        if cost_type == 'lower':
            graph[node1].append((node2, l[i]))
            graph[node2].append((node1, l[i]))
        else:
            graph[node1].append((node2, u[i]))
            graph[node2].append((node1, u[i]))

    # Dijkstra 算法初始化
    min_heap = [(0, start_node, [])]  # (成本, 当前节点, 路径)
    min_cost = {node: float('inf') for node in graph}
    min_cost[start_node] = 0

    while min_heap:
        current_cost, current_node, path = heapq.heappop(min_heap)
        path = path + [current_node]

        # 如果到达终点，返回当前路径
        if current_node == end_node:
            return path

        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor, path))

    return []  # 如果无法到达终点，返回空路径

# 主程序
# 记录整个程序的运行时间
start_time = time.time()

best_path = []
best_cost = float('inf')

for iteration in range(num_iterations):
    all_paths = []
    all_costs = []

    # 每个蚂蚁构造路径
    for ant in range(num_ants):
        path = construct_solution(pheromones, nodes, edges, l, u)
        robust_cost = compute_robust_cost(path, edges, l, u)
        all_paths.append(path)
        all_costs.append(robust_cost)

        # 更新最佳路径
        if robust_cost < best_cost:
            best_cost = robust_cost
            best_path = path

    # 更新信息素
    update_pheromones(all_paths, all_costs)

# 计算总路网的下界和上界最短路径
lower_bound_path = find_shortest_path(edges, l, u, start_node, end_node, cost_type='lower')
upper_bound_path = find_shortest_path(edges, l, u, start_node, end_node, cost_type='upper')

end_time = time.time()

# 输出结果
print("最佳路径:", [int(node) for node in best_path])
print("最佳鲁棒成本:", best_cost)
print("运行时间:", end_time - start_time, "秒")
print("总路网下界最短路径:", [int(node) for node in lower_bound_path])
print("总路网上界最短路径:", [int(node) for node in upper_bound_path])