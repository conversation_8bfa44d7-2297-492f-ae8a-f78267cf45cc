import heapq
from collections import defaultdict

class RoadNetwork:
    def __init__(self):
        self.graph = defaultdict(list)  # 邻接表：{起点: [(终点, 长度, 道路等级)]}
    
    def add_edge(self, u, v, length, road_class):
        self.graph[u].append((v, length, road_class))

class CongestionProfile:
    def __init__(self):
        # 存储结构：{道路等级: [(开始时间（分钟）, 结束时间（分钟）, 拥堵因子)]}
        self.profiles = defaultdict(list)
    
    def add_time_interval(self, road_class, start_time, end_time, factor):
        """添加时段配置，时间格式为'HH:MM'"""
        start = self._time_to_minutes(start_time)
        end = self._time_to_minutes(end_time)
        self.profiles[road_class].append((start, end, factor))
        # 按开始时间排序以便二分查找
        self.profiles[road_class].sort()
    
    def get_factor(self, road_class, current_time):
        """根据当前时间（分钟）获取拥堵因子"""
        intervals = self.profiles.get(road_class, [])
        for start, end, factor in intervals:
            if start <= current_time < end:
                return factor
        return 1.0  # 默认无拥堵
    
    @staticmethod
    def _time_to_minutes(time_str):
        """将'HH:MM'转换为分钟数"""
        h, m = map(int, time_str.split(':'))
        return h * 60 + m

def find_robust_route(network, congestion, start, end, departure):
    """
    参数：
    network: RoadNetwork 路网对象
    congestion: CongestionProfile 拥堵配置
    start: str 起点节点
    end: str 终点节点
    departure: str 出发时间（'HH:MM'格式）
    
    返回：
    (最优路径, 最小成本)
    """
    dep_time = CongestionProfile._time_to_minutes(departure)
    heap = [(0, start, dep_time, [])]  # (累计成本, 当前节点, 当前时间, 路径)
    visited = dict()
    
    while heap:
        cost, node, time, path = heapq.heappop(heap)
        
        if node == end:
            return path + [node], cost
            
        if node in visited and visited[node] <= cost:
            continue
            
        visited[node] = cost
        
        for neighbor, length, road_class in network.graph[node]:
            # 获取当前时段拥堵因子
            factor = congestion.get_factor(road_class, time)
            # 计算路段成本（假设成本=长度*拥堵因子）
            segment_cost = length * factor
            # 计算到达新节点的时间（假设行驶时间与拥堵无关，仅用于时段判断）
            travel_time = length  # 可调整行驶时间计算逻辑
            new_time = time + travel_time
            
            if neighbor not in visited or (cost + segment_cost) < visited.get(neighbor, float('inf')):
                heapq.heappush(heap, (cost + segment_cost, 
                                    neighbor, 
                                    new_time % (24*60),  # 处理跨天
                                    path + [node]))
    
    return None, float('inf')

# 使用示例
if __name__ == "__main__":
    # 初始化路网
    network = RoadNetwork()
    network.add_edge("A", "B", 10, 1)
    network.add_edge("B", "C", 15, 2)
    network.add_edge("A", "C", 20, 3)
    
    # 配置拥堵方案
    congestion = CongestionProfile()
    congestion.add_time_interval(1, "07:30", "09:00", 1.8)  # 早高峰
    congestion.add_time_interval(2, "17:00", "19:00", 2.2)  # 晚高峰
    congestion.add_time_interval(3, "12:00", "14:00", 1.5)  # 午高峰
    
    # 计算最优路径
    path, cost = find_robust_route(network, congestion, "A", "C", "08:00")
    print(f"最优路径：{path}, 最小成本：{cost}")
