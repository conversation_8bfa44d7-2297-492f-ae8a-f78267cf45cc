# 数据定义
# 数据模块
import numpy as np


nodes = np.arange(1, 27)  # 26个节点
edges = np.array([
    [1, 2], [1, 3], [1, 4], [1, 5],
    [2, 3], [2, 6], [2, 11],
    [3, 4], [3, 6], [3, 7],
    [4, 5], [4, 7], [4, 8], [4, 9],
    [5, 9], [5, 10],
    [6, 7], [6, 11], [6, 12], [6, 13],
    [7, 8], [7, 13],
    [8, 9], [8, 13], [8, 14],
    [9, 10], [9, 14], [9, 15],
    [10, 15], [10, 16],
    [11, 12],
    [12, 13], [12, 17],
    [13, 14], [13, 17], [13, 18],
    [14, 15], [14, 18], [14, 19],
    [15, 16], [15, 19], [15, 20],
    [16, 20], [16, 21],
    [17, 18], [17, 22],
    [18, 19], [18, 22], [18, 23],
    [19, 20], [19, 23], [19, 24],
    [20, 21], [20, 24],
    [21, 24], [21, 25],
    [22, 23], [22, 26],
    [23, 24], [23, 26],
    [24, 25], [24, 26],
    [25, 26]
])

    # 边的下界和上界阻抗（需要与边的数量一致）
l = np.array([0.2, 1, 0.9, 0.2, 0.9, 0.8, 1.5, 1.1, 1.5, 0.8, 0.1, 1.1, 0.5, 1.1, 0.9, 0.5, 0.7, 0.7, 1.4,
              0.9, 0.2, 0.4, 0.6, 1.1, 0.6, 0.7, 0.7, 1.2, 1.1, 1, 0.7, 0.3, 0.4, 1.2, 1.5, 0.9, 0.5, 0.9,
              0.6, 1.1, 1, 1, 1.2, 0.8, 1.2, 1, 0.5, 0.5, 0.6, 0.3, 0.9, 0.2, 1.1, 0.5, 1.2, 1.1, 0.7,
              0.9, 2, 0.8, 0.5, 0.5, 2.4])
u = np.array([4, 2, 1.8, 2.1, 1.7, 2.1, 3.2, 1.7, 2.1, 1.5, 1.8, 1.3, 1.3, 2.4, 1.5, 1.5, 1.2, 2.1, 2.3,
              1, 1.5, 2.5, 1.6, 3.5, 1.7, 1.4, 3, 1.8, 1.7, 2.4, 2.1, 1.5, 1.9, 1.9, 3.2, 1.9, 2.5, 2.5,
              2.1, 1.6, 2.8, 1.7, 2.7, 2.2, 2.5, 1.6, 1.5, 1.7, 2.2, 2.3, 2.5, 2.6, 2.4, 2.2, 2.8, 2.7, 1.8,
              2.8, 2.2, 2.3, 1.4, 3.8, 4.4])


# 起点和终点
start_node = 1
end_node = 26
