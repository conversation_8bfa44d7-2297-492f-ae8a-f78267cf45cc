import heapq
from collections import deque


class IntervalImpedance:
    def __init__(self, lower, upper):
        self.lower = lower
        self.upper = upper

    def mean(self):
        return (self.lower + self.upper) / 2

    def width(self):
        return self.upper - self.lower


class RobustGraph:
    def __init__(self, nodes):
        self.nodes = nodes
        self.edges = {node: [] for node in nodes}

    def add_edge(self, u, v, lower, upper):
        self.edges[u].append((v, IntervalImpedance(lower, upper)))
        self.edges[v].append((u, IntervalImpedance(lower, upper)))  # 无向图

    def dijkstra(self, start, end, use_lower=True):
        dist = {node: float('inf') for node in self.nodes}
        dist[start] = 0
        heap = [(0, start)]
        prev = {node: None for node in self.nodes}

        while heap:
            current_dist, u = heapq.heappop(heap)
            if u == end:
                break
            for v, imp in self.edges[u]:
                weight = imp.lower if use_lower else imp.upper
                if dist[v] > current_dist + weight:
                    dist[v] = current_dist + weight
                    heapq.heappush(heap, (dist[v], v))
                    prev[v] = u
        # 重构路径
        path = []
        current = end
        while current is not None:
            path.append(current)
            current = prev[current]
        return path[::-1], dist[end]

    def calculate_acceptability(self, path_imp, ref_imp):
        m_p = path_imp.mean()
        w_p = path_imp.width() / 2
        m_ref = ref_imp.mean()
        w_ref = ref_imp.width() / 2
        if w_p + w_ref == 0:
            return 0
        return (m_ref - m_p) / (w_ref + w_p)

    def robust_shortest_path(self, start, end):
        # 步骤1: 计算上下界最短路
        _, upper_bound = self.dijkstra(start, end, use_lower=False)
        ref_path, _ = self.dijkstra(start, end, use_lower=False)
        ref_imp = IntervalImpedance(upper_bound, upper_bound)  # 参考路径阻抗（上界最短路）

        # 步骤2: 深度优先搜索鲁棒有效路径
        max_acceptability = -float('inf')
        best_path = []
        stack = deque()
        stack.append((start, [], IntervalImpedance(0, 0)))  # (当前节点, 路径, 累积阻抗)

        while stack:
            current, path, imp = stack.pop()

            # 剪枝条件（论文中的条件1和2）
            if current != end:
                for v, edge_imp in self.edges[current]:
                    if v in path:  # 避免环路
                        continue
                    new_lower = imp.lower + edge_imp.lower
                    new_upper = imp.upper + edge_imp.upper
                    new_imp = IntervalImpedance(new_lower, new_upper)

                    # 条件1: L_prk + M_pkt <= M_ref
                    cond1 = (new_imp.lower + (upper_bound - imp.upper)) <= upper_bound
                    # 条件2: M_prk + L_pkt <= M_ref
                    cond2 = (new_imp.upper + (upper_bound - imp.lower)) <= upper_bound

                    if cond1 and cond2:
                        stack.append((v, path + [current], new_imp))
            else:
                # 到达终点，计算可接受度
                acc = self.calculate_acceptability(imp, ref_imp)
                if acc > max_acceptability:
                    max_acceptability = acc
                    best_path = path + [current]

        return best_path, max_acceptability


# 初始化网络（节点1-26）
nodes = list(range(1, 27))
graph = RobustGraph(nodes)

# 添加所有提供的边数据
edges = [
    (1, 2, 0.2, 4), (1, 3, 1, 2), (1, 4, 0.9, 1.8), (1, 5, 0.2, 2.1),
    (2, 3, 0.9, 1.7), (2, 6, 0.8, 2.1), (2, 11, 1.5, 3.2),
    (3, 4, 1.1, 1.7), (3, 6, 1.5, 2.1), (3, 7, 0.8, 1.5),
    (4, 5, 0.1, 1.8), (4, 7, 1.1, 1.3), (4, 8, 0.5, 1.3), (4, 9, 1.1, 2.4),
    (5, 9, 0.9, 1.5), (5, 10, 0.5, 1.5),
    (6, 7, 0.7, 1.2), (6, 11, 0.7, 2.1), (6, 12, 1.4, 2.3), (6, 13, 0.9, 1),
    (7, 8, 0.2, 1.5), (7, 13, 0.4, 2.5),
    (8, 9, 0.6, 1.6), (8, 13, 1.1, 3.5), (8, 14, 0.6, 1.7),
    (9, 10, 0.7, 1.4), (9, 14, 0.7, 3), (9, 15, 1.2, 1.8),
    (10, 15, 1.1, 1.7), (10, 16, 1, 2.4),
    (11, 12, 0.7, 2.1),
    (12, 13, 0.3, 1.5), (12, 17, 0.4, 1.9),
    (13, 14, 1.2, 1.9), (13, 17, 1.5, 3.2), (13, 18, 0.9, 1.9),
    (14, 15, 0.5, 2.5), (14, 18, 0.9, 2.5), (14, 19, 0.6, 2.1),
    (15, 16, 1.1, 1.6), (15, 19, 1, 2.8), (15, 20, 1, 1.7),
    (16, 20, 1.2, 2.7), (16, 21, 0.8, 2.2),
    (17, 18, 1.2, 2.5), (17, 22, 1, 1.6),
    (18, 19, 0.5, 1.5), (18, 22, 0.5, 1.7), (18, 23, 0.6, 2.2),
    (19, 20, 0.3, 2.3), (19, 23, 0.9, 2.5), (19, 24, 0.2, 2.6),
    (20, 21, 1.1, 2.4), (20, 24, 0.5, 2.2),
    (21, 24, 1.2, 2.8), (21, 25, 1.1, 2.7),
    (22, 23, 0.7, 1.8), (22, 26, 0.9, 2.8),
    (23, 24, 2, 2.2), (23, 26, 0.8, 2.3),
    (24, 25, 0.5, 1.4), (24, 26, 0.5, 3.8),
    (25, 26, 2.4, 4.4),
    (2, 1, 0.2, 4), (3, 1, 1, 2), (4, 1, 0.9, 1.8), (5, 1, 0.2, 2.1),
    (3, 2, 0.9, 1.7), (6, 2, 0.8, 2.1), (11, 2, 1.5, 3.2),
    (4, 3, 1.1, 1.7), (6, 3, 1.5, 2.1), (7, 3, 0.8, 1.5),
    (5, 4, 0.1, 1.8), (7, 4, 1.1, 1.3), (8, 4, 0.5, 1.3), (9, 4, 1.1, 2.4),
    (9, 5, 0.9, 1.5), (10, 5, 0.5, 1.5),
    (7, 6, 0.7, 1.2), (11, 6, 0.7, 2.1), (12, 6, 1.4, 2.3), (13, 6, 0.9, 1),
    (8, 7, 0.2, 1.5), (13, 7, 0.4, 2.5),
    (9, 8, 0.6, 1.6), (13, 8, 1.1, 3.5), (14, 8, 0.6, 1.7),
    (10, 9, 0.7, 1.4), (14, 9, 0.7, 3), (15, 9, 1.2, 1.8),
    (15, 10, 1.1, 1.7), (16, 10, 1, 2.4),
    (12, 11, 0.7, 2.1),
    (13, 12, 0.3, 1.5), (17, 12, 0.4, 1.9),
    (14, 13, 1.2, 1.9), (17, 13, 1.5, 3.2), (18, 13, 0.9, 1.9),
    (15, 14, 0.5, 2.5), (18, 14, 0.9, 2.5), (19, 14, 0.6, 2.1),
    (16, 15, 1.1, 1.6), (19, 15, 1, 2.8), (20, 15, 1, 1.7),
    (20, 16, 1.2, 2.7), (21, 16, 0.8, 2.2),
    (18, 17, 1.2, 2.5), (22, 17, 1, 1.6),
    (19, 18, 0.5, 1.5), (22, 18, 0.5, 1.7), (23, 18, 0.6, 2.2),
    (20, 19, 0.3, 2.3), (23, 19, 0.9, 2.5), (24, 19, 0.2, 2.6),
    (21, 20, 1.1, 2.4), (24, 20, 0.5, 2.2),
    (24, 21, 1.2, 2.8), (25, 21, 1.1, 2.7),
    (23, 22, 0.7, 1.8), (26, 22, 0.9, 2.8),
    (24, 23, 2, 2.2), (26, 23, 0.8, 2.3),
    (25, 24, 0.5, 1.4), (26, 24, 0.5, 3.8),
    (26, 25, 2.4, 4.4)
]

for edge in edges:
    graph.add_edge(edge[0], edge[1], edge[2], edge[3])

# 运行算法
best_path, acceptability = graph.robust_shortest_path(1, 26)
print(f"鲁棒最短路径: {best_path}")
print(f"最大可接受度系数: {acceptability:.2f}")