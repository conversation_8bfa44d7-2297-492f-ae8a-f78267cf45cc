import heapq
import random

# 随机数种子确保可重复性
random.seed(42)

# 定义节点连接情况（双向路段）
edges =  {
    # ----------------------------
    # 节点1的连接 (4条双向边)
    # ----------------------------
    (1, 2): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (2, 1): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (1, 3): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (3, 1): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (1, 4): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3, 'evening_upper': 6},
    (4, 1): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3, 'evening_upper': 6},
    (1, 5): {'night_lower': 3, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 5, 'evening_upper': 10},
    (5, 1): {'night_lower': 3, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 5, 'evening_upper': 10},

    # ----------------------------
    # 节点2的连接 (3条双向边)
    # ----------------------------
    (2, 3): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3, 'evening_upper': 6},
    (3, 2): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3, 'evening_upper': 6},
    (2, 6): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (6, 2): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},

    # ----------------------------
    # 节点3的连接 (7条双向边)
    # ----------------------------
    (3, 4): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (4, 3): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (3, 6): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (6, 3): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (3, 7): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (7, 3): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (3, 8): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (8, 3): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点4的连接 (5条双向边)
    # ----------------------------
    (4, 5): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (5, 4): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (4, 8): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3, 'evening_upper': 6},
    (8, 4): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3, 'evening_upper': 6},
    (4, 9): {'night_lower': 3, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 5, 'evening_upper': 10},
    (9, 4): {'night_lower': 3, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 5, 'evening_upper': 10},
    (4, 10): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (10, 4): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点5的连接 (3条双向边)
    # ----------------------------
    (5, 10): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3, 'evening_upper': 6},
    (10, 5): {'night_lower': 2, 'night_upper': 4, 'day_lower': 4, 'day_upper': 8, 'evening_lower': 3, 'evening_upper': 6},

    # ----------------------------
    # 节点6的连接 (3条双向边)
    # ----------------------------
    (6, 7): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (7, 6): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (6, 11): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (11, 6): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},

    # ----------------------------
    # 节点7的连接 (6条双向边)
    # ----------------------------
    (7, 8): {'night_lower': 3, 'night_upper': 5, 'day_lower': 5, 'day_upper': 9, 'evening_lower': 3, 'evening_upper': 8},
    (8, 7): {'night_lower': 3, 'night_upper': 5, 'day_lower': 5, 'day_upper': 9, 'evening_lower': 3, 'evening_upper': 8},
    (7, 11): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (11, 7): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (7, 12): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (12, 7): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (7, 13): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (13, 7): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},

    # ----------------------------
    # 节点8的连接 (5条双向边)
    # ----------------------------
    (8, 9): {'night_lower': 3, 'night_upper': 5, 'day_lower': 6, 'day_upper': 11, 'evening_lower': 5, 'evening_upper': 10},
    (9, 8): {'night_lower': 3, 'night_upper': 5, 'day_lower': 6, 'day_upper': 11, 'evening_lower': 5, 'evening_upper': 10},
    (8, 13): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (13, 8): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (8, 14): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (14, 8): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点9的连接 (4条双向边)
    # ----------------------------
    (9, 10): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (10, 9): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (9, 14): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (14, 9): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (9, 15): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (15, 9): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点10的连接 (3条双向边)
    # ----------------------------
    (10, 15): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (15, 10): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (10, 16): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (16, 10): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},

    # ----------------------------
    # 节点11的连接 (3条双向边)
    # ----------------------------
    (11, 17): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (17, 11): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (11, 12): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (12, 11): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},

    # ----------------------------
    # 节点12的连接 (4条双向边)
    # ----------------------------
    (12, 17): {'night_lower': 3, 'night_upper': 7, 'day_lower': 6, 'day_upper': 14, 'evening_lower': 7, 'evening_upper': 11},
    (17, 12): {'night_lower': 3, 'night_upper': 7, 'day_lower': 6, 'day_upper': 14, 'evening_lower': 7, 'evening_upper': 11},
    (12, 18): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (18, 12): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (12, 13): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (13, 12): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点13的连接 (5条双向边)
    # ----------------------------
    (13, 18): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (18, 13): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (13, 14): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (14, 13): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (13, 19): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (19, 13): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点14的连接 (5条双向边)
    # ----------------------------
    (14, 19): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (19, 14): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (14, 20): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (20, 14): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (14, 15): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (15, 14): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点15的连接 (4条双向边)
    # ----------------------------
    (15, 16): {'night_lower': 3, 'night_upper': 7, 'day_lower': 6, 'day_upper': 13, 'evening_lower': 5, 'evening_upper': 10},
    (16, 15): {'night_lower': 3, 'night_upper': 7, 'day_lower': 6, 'day_upper': 13, 'evening_lower': 5, 'evening_upper': 10},
    (15, 20): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (20, 15): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (15, 21): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (21, 15): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},

    # ----------------------------
    # 节点16的连接 (2条双向边)
    # ----------------------------
    (16, 21): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (21, 16): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点17的连接 (3条双向边)
    # ----------------------------
    (17, 18): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (18, 17): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (17, 22): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (22, 17): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},

    # ----------------------------
    # 节点18的连接 (5条双向边)
    # ----------------------------
    (18, 19): {'night_lower': 3, 'night_upper': 5, 'day_lower': 5, 'day_upper': 9, 'evening_lower': 4, 'evening_upper': 9},
    (19, 18): {'night_lower': 3, 'night_upper': 5, 'day_lower': 5, 'day_upper': 9, 'evening_lower': 4, 'evening_upper': 9},
    (18, 22): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (22, 18): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (18, 23): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (23, 18): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},

    # ----------------------------
    # 节点19的连接 (4条双向边)
    # ----------------------------
    (19, 20): {'night_lower': 3, 'night_upper': 7, 'day_lower': 7, 'day_upper': 13, 'evening_lower': 7, 'evening_upper': 11},
    (20, 19): {'night_lower': 3, 'night_upper': 7, 'day_lower': 7, 'day_upper': 13, 'evening_lower': 7, 'evening_upper': 11},
    (19, 23): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (23, 19): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (19, 24): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (24, 19): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点20的连接 (5条双向边)
    # ----------------------------
    (20, 21): {'night_lower': 3, 'night_upper': 7, 'day_lower': 6, 'day_upper': 13, 'evening_lower': 5, 'evening_upper': 9},
    (21, 20): {'night_lower': 3, 'night_upper': 7, 'day_lower': 6, 'day_upper': 13, 'evening_lower': 5, 'evening_upper': 9},
    (20, 24): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (24, 20): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (20, 25): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (25, 20): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},

    # ----------------------------
    # 节点21的连接 (3条双向边)
    # ----------------------------
    (21, 25): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (25, 21): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点22的连接 (3条双向边)
    # ----------------------------
    (22, 26): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (26, 22): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (22, 23): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (23, 22): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},

    # ----------------------------
    # 节点23的连接 (5条双向边)
    # ----------------------------
    (23, 24): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 9, 'evening_lower': 4, 'evening_upper': 8},
    (24, 23): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 9, 'evening_lower': 4, 'evening_upper': 8},
    (23, 26): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (26, 23): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (23, 27): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (27, 23): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},

    # ----------------------------
    # 节点24的连接 (5条双向边)
    # ----------------------------
    (24, 25): {'night_lower': 4, 'night_upper': 8, 'day_lower': 7, 'day_upper': 13, 'evening_lower': 6, 'evening_upper': 11},
    (25, 24): {'night_lower': 4, 'night_upper': 8, 'day_lower': 7, 'day_upper': 13, 'evening_lower': 6, 'evening_upper': 11},
    (24, 27): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (27, 24): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (24, 28): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (28, 24): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},

    # ----------------------------
    # 节点25的连接 (3条双向边)
    # ----------------------------
    (25, 28): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (28, 25): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},

    # ----------------------------
    # 节点26的连接 (3条双向边)
    # ----------------------------
    (26, 27): {'night_lower': 4, 'night_upper': 8, 'day_lower': 7, 'day_upper': 13, 'evening_lower': 5, 'evening_upper': 11},
    (27, 26): {'night_lower': 4, 'night_upper': 8, 'day_lower': 7, 'day_upper': 13, 'evening_lower': 5, 'evening_upper': 11},
    (26, 29): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (29, 26): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},

    # ----------------------------
    # 节点27的连接 (4条双向边)
    # ----------------------------
    (27, 29): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (29, 27): {'night_lower': 2, 'night_upper': 5, 'day_lower': 5, 'day_upper': 10, 'evening_lower': 4, 'evening_upper': 8},
    (27, 28): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},
    (28, 27): {'night_lower': 3, 'night_upper': 6, 'day_lower': 6, 'day_upper': 12, 'evening_lower': 5, 'evening_upper': 9},

    # ----------------------------
    # 节点28的连接 (3条双向边)
    # ----------------------------
    (28, 29): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
    (29, 28): {'night_lower': 4, 'night_upper': 7, 'day_lower': 7, 'day_upper': 14, 'evening_lower': 6, 'evening_upper': 11},
}

nodes = list(range(1, 30))
# 检查总边数是否为70条双向边（即140条单向边）
assert len(edges) == 140, f"边数错误，当前边数: {len(edges)}"


# ---------------------------
# 算法实现
# ---------------------------
def get_impedance(u, v, t, is_upper):
    """根据时间和是否取上界返回阻抗值"""
    hour = t % 24
    if 0 <= hour < 6:
        key = 'night_upper' if is_upper else 'night_lower'
    elif 6 <= hour < 18:
        key = 'day_upper' if is_upper else 'day_lower'
    else:
        key = 'evening_upper' if is_upper else 'evening_lower'
    return edges[(u, v)][key]


def time_dependent_dijkstra(nodes, edges_dict, start, end, start_time, use_upper_for_p=None):
    """时间依赖Dijkstra算法"""
    adj = build_adjacency_list(edges_dict)
    arrival = {node: float('inf') for node in nodes}
    arrival[start] = start_time
    predecessors = {node: None for node in nodes}
    heap = [(start_time, start)]

    while heap:
        current_time, u = heapq.heappop(heap)
        if u == end:
            break
        if current_time > arrival[u]:
            continue
        for v in adj.get(u, []):
            # 判断是否在路径p中，决定取上界或下界
            if use_upper_for_p and (u, v) in use_upper_for_p:
                travel_time = get_impedance(u, v, current_time, is_upper=True)
            else:
                travel_time = get_impedance(u, v, current_time, is_upper=False)
            new_time = current_time + travel_time
            if new_time < arrival[v]:
                arrival[v] = new_time
                predecessors[v] = u
                heapq.heappush(heap, (new_time, v))

    # 回溯路径
    path = []
    current = end
    while current is not None:
        path.append(current)
        current = predecessors.get(current)
    path.reverse()
    return arrival[end], path


def build_adjacency_list(edges_dict):
    """构建邻接表"""
    adj = {}
    for (u, v) in edges_dict:
        if u not in adj:
            adj[u] = []
        adj[u].append(v)
    return adj


def calculate_robust_cost(nodes, edges_dict, start, end, departure_time):
    """计算鲁棒成本"""
    # 阶段一：找到传统时间依赖最短路径p
    p_time, p_path = time_dependent_dijkstra(nodes, edges_dict, start, end, departure_time)
    if p_time == float('inf'):
        return float('inf'), [], []

    # 提取路径p中的路段集合
    p_edges = set()
    for i in range(len(p_path) - 1):
        u, v = p_path[i], p_path[i + 1]
        p_edges.add((u, v))

    # 阶段二：在修正路网中找p*
    p_star_time, p_star_path = time_dependent_dijkstra(
        nodes, edges_dict, start, end, departure_time, use_upper_for_p=p_edges
    )

    # 计算sum(p_upper)
    sum_p_upper = 0
    current_time = departure_time
    for i in range(len(p_path) - 1):
        u, v = p_path[i], p_path[i + 1]
        sum_p_upper += get_impedance(u, v, current_time, is_upper=True)
        current_time += get_impedance(u, v, current_time, is_upper=True)

    robust_cost = sum_p_upper - p_star_time
    return robust_cost, p_path, p_star_path


# ---------------------------
# 测试运行
# ---------------------------
departure_time = 8  # 出发时间设为8:00
robust_cost, p_path, p_star_path = calculate_robust_cost(nodes, edges, 1, 29, departure_time)

print(f"[虚拟路网] 节点数: {len(nodes)}, 双向路段数: {len(edges) // 2}")
print(f"[出发时间] {departure_time % 24}:00")
print("-----------------------------")
print(f"传统最优路径: {p_path}")
print(f"修正路网最短路径: {p_star_path}")
print(f"鲁棒成本: {robust_cost}")