# -*- coding: utf-8 -*-
"""
城市路网鲁棒路径规划系统
功能特性：
1. 时变阻抗建模：考虑不同时段的拥堵因子
2. 鲁棒路径优化：计算路径的鲁棒性成本
3. 多进程加速：利用并行计算提升性能
4. 交互式查询：提供用户友好的命令行界面
"""

# 导入必要的库
import numpy as np  # 数值计算库
import heapq  # 堆队列算法，用于优先队列
from collections import defaultdict, deque  # 默认字典和双端队列
import multiprocessing  # 多进程处理
import time  # 时间相关功能

# ==================== 数据准备部分 ====================
# 定义路网边关系 (94条有向边)
edges = [(1, 2), (2, 1), (1, 3), (3, 1), (1, 4), (4, 1), (1, 5), (5, 1),
         (2, 3), (3, 2), (2, 6), (6, 2), (2, 7), (7, 2), (3, 4), (4, 3),
         (3, 7), (7, 3), (3, 8), (8, 3), (4, 5), (5, 4), (4, 8), (8, 4),
         (4, 9), (9, 4), (5, 9), (9, 5), (5, 10), (10, 5), (6, 7), (7, 6),
         (6, 11), (11, 6), (7, 8), (8, 7), (7, 11), (11, 7), (7, 12), (12, 7),
         (8, 9), (9, 8), (8, 12), (12, 8), (8, 13), (13, 8), (9, 10), (10, 9),
         (9, 13), (13, 9), (9, 14), (14, 9), (10, 14), (14, 10), (10, 15), (15, 10),
         (11, 12), (12, 11), (11, 16), (16, 11), (11, 17), (17, 11), (12, 13), (13, 12),
         (12, 17), (17, 12), (12, 18), (18, 12), (13, 14), (14, 13), (13, 18), (18, 13),
         (13, 19), (19, 13), (14, 15), (15, 14), (14, 19), (19, 14), (15, 19), (19, 15),
         (16, 17), (17, 16), (16, 20), (20, 16), (17, 18), (18, 17), (17, 20), (20, 17),
         (18, 19), (19, 18), (18, 20), (20, 18), (19, 20), (20, 19)]  # 共94条边

# 基础阻抗下限值 (每条边对应一个值)
l_base = [9, 9, 7, 7, 6, 6, 10, 10, 6, 6,
          12, 12, 10, 10, 11, 11, 8, 8, 10, 10,
          9, 9, 7, 7, 6, 6, 7, 7, 9, 9,
          8, 8, 6, 6, 9, 9, 8, 8, 6, 6,
          8, 8, 11, 11, 6, 6, 10, 10, 11, 11,
          6, 6, 10, 10, 10, 10, 8, 8, 6, 6,
          7, 7, 8, 8, 6, 6, 9, 9, 9, 9,
          10, 10, 6, 6, 7, 7, 8, 8, 6, 6,
          9, 9, 7, 10, 8, 8, 6, 6, 9, 9,  # 补充缺失的6个值
          7, 8, 9, 10]  # 总长度94

# 基础阻抗上限值 (每条边对应一个值)
u_base = [14, 14, 13, 13, 12, 12, 15, 15, 11, 11,
          15, 15, 14, 14, 15, 15, 12, 12, 15, 15,
          14, 14, 13, 13, 12, 12, 12, 12, 15, 15,
          14, 14, 12, 12, 14, 14, 14, 14, 11, 11,
          12, 12, 15, 15, 11, 11, 14, 14, 15, 15,
          11, 11, 14, 14, 14, 14, 12, 12, 11, 11,
          15, 15, 13, 13, 11, 11, 15, 15, 15, 15,
          13, 13, 11, 11, 12, 12, 15, 15, 10, 10,
          15, 15, 14, 14, 15, 15, 12, 12, 15, 15,  # 补充缺失的6个值
          13, 14, 15, 15]  # 总长度94

# 定义不同时段的拥堵因子 (下限系数, 上限系数)
congestion_factors = {
    "00:00-07:00": (-0.5, -0.1),        # 平
    "07:00-07:10": (0.5, 0.6),          # 早高峰
    "07:10-07:20": (0.4, 0.7),
    "07:20-07:30": (0.4, 0.6),
    "07:30-07:40": (0.5, 0.9),
    "07:40-07:50": (0.4, 0.8),
    "07:50-08:00": (0.6, 1.1),
    "08:00-08:10": (0.5, 0.9),
    "08:10-08:20": (0.7, 0.8),
    "08:20-08:30": (0.7, 1.1),
    "08:30-08:40": (0.6, 1.0),
    "08:40-08:50": (0.5, 1.1),
    "08:50-09:00": (0.5, 0.7),
    "09:00-17:00": (0, 0),              # 平
    "17:00-19:00": (0.6, 0.9),          # 晚高峰
    "19:00-00:00": (-0.3, -0.1)          # 平
}

# 定义时间区间常量 (转换为分钟数)
TIME_PERIODS = {
    "00:00-07:00": (0, 420),
    "07:00-07:10": (420, 430),
    "07:10-07:20": (430, 440),
    "07:20-07:30": (440, 450),
    "07:30-07:40": (450, 460),
    "07:40-07:50": (460, 470),
    "07:50-08:00": (470, 480),
    "08:00-08:10": (480, 490),
    "08:10-08:20": (490, 500),
    "08:20-08:30": (500, 510),
    "08:30-08:40": (510, 520),
    "08:40-08:50": (520, 530),
    "08:50-09:00": (530, 540),
    "09:00-17:00": (540, 1020),
    "17:00-19:00": (1020, 1140),
    "19:00-00:00": (1140, 1440)
}

# ==================== 时变阻抗生成模块 ====================
def generate_time_dependent_data(edges, l_base, u_base, congestion_factors):
    """
    生成时变路网数据
    参数：
        edges: 边集合
        l_base: 基础最小阻抗
        u_base: 基础最大阻抗
        congestion_factors: 时段拥堵因子
    返回：
        defaultdict: 时变路网数据结构
    """
    network_data = defaultdict(dict)  # 创建一个默认字典，用于存储网络数据，键为源节点，值为目标节点及其阻抗数据
    # 遍历所有边
    for idx, (i, j) in enumerate(edges):
        source = str(i)  # 将源节点转换为字符串类型
        target = str(j)  # 将目标节点转换为字符串类型
        time_ranges = []  # 初始化一个列表，用于存储当前边的时间段阻抗数据
        # 为每个时段计算阻抗值
        for period, (f, g) in congestion_factors.items():
            start, end = TIME_PERIODS[period]  # 获取当前时段的开始和结束时间（分钟数）
            # 计算时变阻抗值（四舍五入保留2位小数）
            l_t = round(l_base[idx] * (1 + f), 2)  # 计算当前时段的最小阻抗值
            u_t = round(u_base[idx] * (1 + g), 2)  # 计算当前时段的最大阻抗值
            time_ranges.append([start, end, l_t, u_t])  # 将时段信息和阻抗值添加到时间范围列表中
        network_data[source][target] = time_ranges  # 将源节点到目标节点的时间范围数据存入网络数据字典
    return network_data  # 返回生成的时变路网数据

# ==================== 增强版路径规划类 ====================
class EnhancedRoadNetwork:
    """时变路网路径规划核心类"""

    def __init__(self, edges, l_base, u_base, congestion_factors):
        """
        初始化路网
        参数：
            edges: 边集合
            l_base: 基础最小阻抗
            u_base: 基础最大阻抗
            congestion_factors: 时段拥堵因子
        """
        # 生成时变路网数据
        network_data = generate_time_dependent_data(edges, l_base, u_base, congestion_factors)
        self.graph = defaultdict(list)  # 使用字典存储图结构，键为节点，值为邻接节点及其阻抗数据
        self._initialize_graph(network_data)  # 初始化图结构
        self.robust_cost_cache = {}  # 初始化鲁棒成本缓存字典，用于存储已计算的鲁棒成本
        self.edges = edges  # 保存原始边数据，便于后续使用

    def _initialize_graph(self, network_data):
        """初始化图结构（内部方法）"""
        for source, neighbors in network_data.items():  # 遍历每个源节点及其邻接节点
            for target, time_ranges in neighbors.items():  # 遍历每个目标节点及其时间段阻抗数据
                # 存储格式：目标节点 + 时段阻抗数据
                self.graph[source].append((target, time_ranges))  # 将目标节点和对应的时间段阻抗数据添加到图中

    def get_impedance(self, impedance_ranges, time):
        """
        获取当前时间的阻抗范围
        参数：
            impedance_ranges: 阻抗数据范围列表
            time: 当前时间（分钟）
        返回：
            tuple: (low, high) 阻抗范围
        """
        time %= 1440  # 处理跨天时间（24小时=1440分钟）
        # 线性查找匹配的时间区间
        for start, end, low, high in impedance_ranges:
            if start <= time < end:  # 检查当前时间是否在该时间段内
                return low, high  # 返回对应的最小和最大阻抗值
        # 默认返回最后一个区间（处理23:59-00:00边界情况）
        return impedance_ranges[-1][2], impedance_ranges[-1][3]  # 返回最后一个时间段的阻抗值

    def dijkstra(self, start, end, time, lower_bound=True):
        """
        改进的Dijkstra最短路径算法
        参数：
            start: 起点
            end: 终点
            time: 出发时间
            lower_bound: 是否使用最小阻抗计算
        返回：
            tuple: (路径, 总成本)
        特点：
            - 带时间维度计算
            - 支持阻抗上下界选择
            - 动态更新时间状态
        """
        # 优先队列：(累计成本, 当前节点, 当前时间)
        priority_queue = [(0, start, time)]  # 初始化优先队列，起点的成本为0
        dist = {start: 0}  # 距离字典，记录到各节点的最短距离
        prev = {start: None}  # 前驱节点字典，记录路径前驱节点

        while priority_queue:  # 当优先队列不为空时
            current_dist, current_node, current_time = heapq.heappop(priority_queue)  # 弹出当前成本最低的节点

            # 终止条件：到达终点
            if current_node == end:
                path = []  # 初始化路径列表
                # 回溯构建路径
                while current_node is not None:
                    path.append(current_node)  # 将当前节点添加到路径中
                    current_node = prev[current_node]  # 更新当前节点为前驱节点
                return path[::-1], current_dist  # 反转路径并返回

            # 剪枝：已有更优路径
            if current_dist > dist.get(current_node, float('inf')):
                continue  # 如果当前路径成本大于已知最优路径，跳过该节点

            # 遍历邻接节点
            for neighbor, impedance_ranges in self.graph[current_node]:
                impedance = self.get_impedance(impedance_ranges, current_time)  # 获取当前时间的阻抗范围
                if not impedance:  # 如果没有阻抗数据，跳过
                    continue

                # 选择阻抗上下界
                cost = impedance[0] if lower_bound else impedance[1]  # 根据lower_bound选择最小或最大阻抗
                new_dist = current_dist + cost  # 计算到邻接节点的新距离
                new_time = current_time + cost  # 计算到邻接节点的新时间
                new_time %= 1440  # 时间循环处理

                # 松弛操作
                if new_dist < dist.get(neighbor, float('inf')):  # 如果新距离小于已知距离
                    dist[neighbor] = new_dist  # 更新邻接节点的最短距离
                    prev[neighbor] = current_node  # 更新前驱节点
                    heapq.heappush(priority_queue, (new_dist, neighbor, new_time))  # 将新节点加入优先队列

        return None, float('inf')  # 如果没有找到路径，返回None和无穷大

    def calculate_robust_cost(self, path, start_time):
        """
        计算路径鲁棒成本
        参数：
            path: 待评估路径
            start_time: 出发时间
        返回：
            tuple: (鲁棒成本, 最坏耗时, 备选最佳耗时, 备选路径)
        """
        cache_key = (tuple(path), start_time)  # 创建缓存键
        if cache_key in self.robust_cost_cache:  # 检查缓存
            return self.robust_cost_cache[cache_key]  # 如果缓存中有结果，直接返回

        # 计算原路径最坏情况耗时
        worst_case_time = 0  # 初始化最坏情况耗时
        current_time = start_time  # 设置当前时间为出发时间
        for i in range(len(path) - 1):  # 遍历路径中的每一对相邻节点
            u, v = path[i], path[i + 1]  # 获取当前边的起点和终点
            for neighbor, ranges in self.graph[u]:  # 遍历起点的邻接节点
                if neighbor == v:  # 找到目标节点
                    impedance = self.get_impedance(ranges, current_time)  # 获取当前时间的阻抗
                    worst_case_time += impedance[1]  # 使用最大阻抗值累加到最坏情况耗时
                    current_time = (current_time + impedance[1]) % 1440  # 更新时间
                    break  # 找到目标节点后跳出循环

        # 构建剩余图（排除原路径边）
        remaining_graph = defaultdict(list)  # 创建一个新的图结构
        for u in self.graph:  # 遍历原图的每个节点
            for v, ranges in self.graph[u]:  # 遍历每个节点的邻接节点
                if (u, v) not in zip(path[:-1], path[1:]):  # 如果边不在原路径中
                    remaining_graph[u].append((v, ranges))  # 将边添加到剩余图中

        # 计算备选路径
        remaining_net = EnhancedRoadNetwork(self.edges, [], [], {})  # 创建一个新的路网实例
        remaining_net.graph = remaining_graph  # 设置剩余图
        alt_path, best_alt_time = remaining_net.dijkstra(path[0], path[-1], start_time)  # 计算备选路径的最佳耗时

        # 计算鲁棒成本
        robust_cost = worst_case_time - best_alt_time if alt_path else float('inf')  # 计算鲁棒成本
        result = (robust_cost, worst_case_time, best_alt_time, alt_path)  # 结果元组
        self.robust_cost_cache[cache_key] = result  # 将结果存入缓存
        return result  # 返回结果

    def calculate_robust_cost_wrapper(self, args):
        """多进程包装方法"""
        return self.calculate_robust_cost(*args)  # 解包参数并调用鲁棒成本计算方法

    def find_min_robust_path(self, start, end, start_time):
        """
        寻找最小鲁棒路径
        返回：
            tuple: (最优路径, 鲁棒成本, 最坏耗时, 备选最佳耗时, 备选路径)
        """
        all_paths = []  # 存储所有可行路径
        # 使用双端队列存储路径
        priority_queue = [(0, deque([start]), start_time)]  # 初始化优先队列，起点的成本为0
        min_cost = float('inf')  # 初始化最小成本为无穷大

        # 路径搜索阶段
        while priority_queue:  # 当优先队列不为空时
            current_dist, current_path, current_time = heapq.heappop(priority_queue)  # 弹出当前成本最低的节点
            current_node = current_path[-1]  # 获取当前路径的最后一个节点

            if current_dist >= min_cost:  # 如果当前路径成本大于等于已知最优路径，跳过
                continue

            if current_node == end:  # 如果到达终点
                path = list(current_path)  # 记录可行路径
                all_paths.append(path)  # 将路径添加到可行路径列表
                continue

            # 扩展邻接节点
            for neighbor, ranges in self.graph[current_node]:  # 遍历当前节点的邻接节点
                if neighbor not in current_path:  # 防止环路
                    impedance = self.get_impedance(ranges, current_time)  # 获取当前时间的阻抗
                    new_dist = current_dist + impedance[0]  # 使用最小阻抗估计新距离
                    new_time = (current_time + impedance[0]) % 1440  # 更新时间
                    new_path = current_path.copy()  # 复制当前路径
                    new_path.append(neighbor)  # 将邻接节点添加到新路径
                    heapq.heappush(priority_queue, (new_dist, new_path, new_time))  # 将新节点加入优先队列

        # 并行计算鲁棒成本
        with multiprocessing.Pool() as pool:  # 创建进程池
            tasks = [(path, start_time) for path in all_paths]  # 准备任务列表
            results = pool.map(self.calculate_robust_cost_wrapper, tasks)  # 并行计算鲁棒成本

        # 寻找最优解
        optimal = (None, float('inf'), 0, 0, None)  # 初始化最优解
        for path, (cost, a, b, alt) in zip(all_paths, results):  # 遍历所有路径及其计算结果
            if cost < optimal[1]:  # 如果当前路径成本小于已知最优成本
                optimal = (path, cost, a, b, alt)  # 更新最优解
        return optimal  # 返回最优路径及其相关信息

# ======================= 主程序模块 ======================
if __name__ == '__main__':
    # === 数据验证 ===
    assert len(edges) == len(l_base) == len(u_base), "基础数据长度不匹配"
    # 检查边、最小阻抗和最大阻抗的长度是否一致，确保数据完整性

    # 时间覆盖验证
    total_coverage = sum(end - start for start, end in TIME_PERIODS.values())
    assert total_coverage == 1440, f"时间覆盖不完整，当前总时长：{total_coverage}分钟"
    # 计算所有时间段的总覆盖时间，确保覆盖完整的24小时（1440分钟）

    print(f"时间覆盖验证通过，总时长：{total_coverage}分钟")


    # 输出时间覆盖验证结果

    # === 时间转换函数 ===
    def time_to_minutes(t_str):
        """将HH:MM格式时间转换为分钟数"""
        try:
            hour, minute = map(int, t_str.split(':'))  # 将输入的时间字符串分割为小时和分钟
            if 0 <= hour < 24 and 0 <= minute < 60:  # 验证时间的有效性
                return hour * 60 + minute  # 将时间转换为分钟数
            return None  # 如果时间无效，返回None
        except ValueError:
            return None  # 如果转换失败，返回None


    # 初始化路网
    road_net = EnhancedRoadNetwork(edges, l_base, u_base, congestion_factors)
    # 创建增强版路网实例，传入边、最小阻抗、最大阻抗和拥堵因子

    # === 交互查询循环 ===
    while True:
        print("\n=== 路径规划查询 ===")  # 输出查询提示
        start_node = input("起点编号（1-20，q退出）: ").strip()  # 获取用户输入的起点编号
        if start_node.lower() == 'q':  # 如果用户输入'q'，退出循环
            break

        end_node = input("终点编号（1-20）: ").strip()  # 获取用户输入的终点编号
        time_str = input("出发时间（HH:MM）: ").strip()  # 获取用户输入的出发时间

        # 输入验证
        if not (start_node.isdigit() and end_node.isdigit()):  # 验证起点和终点是否为数字
            print("错误：节点编号必须为数字")  # 输出错误提示
            continue  # 继续下一次循环

        depart_time = time_to_minutes(time_str)  # 将输入的时间字符串转换为分钟数
        if depart_time is None:  # 如果时间转换失败
            print("错误：无效时间格式")  # 输出错误提示
            continue  # 继续下一次循环

        try:
            # 执行路径规划
            path, cost, worst, best, alt = road_net.find_min_robust_path(
                start_node, end_node, depart_time)  # 调用路径规划方法，获取路径和相关信息

            # 输出结果
            print("\n=== 规划结果 ===")  # 输出结果提示
            print(f"出发时间: {time_str}")  # 输出用户输入的出发时间
            print(f"推荐路径: {' → '.join(path)}")  # 输出推荐的路径
            print(f"鲁棒成本: {cost:.2f} 分钟")  # 输出鲁棒成本
            print(f"最坏情况耗时: {worst:.2f} 分钟")  # 输出最坏情况耗时
            print(f"备选路径最佳耗时: {best:.2f} 分钟")  # 输出备选路径的最佳耗时
            print(f"备选路径: {' → '.join(alt) if alt else '无可用备选'}")  # 输出备选路径，如果没有则提示无可用备选

        except Exception as e:  # 捕获路径规划过程中可能出现的异常
            print(f"路径规划失败: {str(e)}")  # 输出错误信息