#  不能运行，有问题的点：路径的长度各不相同，不知道怎么解决



import numpy as np
import heapq
import time

# 数据定义
nodes = np.arange(1, 27)  # 26个节点
edges = np.array([
    [1, 2], [1, 3], [1, 4], [1, 5],
    [2, 3], [2, 6], [2, 11],
    [3, 4], [3, 6], [3, 7],
    [4, 5], [4, 7], [4, 8], [4, 9],
    [5, 9], [5, 10],
    [6, 7], [6, 11], [6, 12], [6, 13],
    [7, 8], [7, 13],
    [8, 9], [8, 13], [8, 14],
    [9, 10], [9, 14], [9, 15],
    [10, 15], [10, 16],
    [11, 12],
    [12, 13], [12, 17],
    [13, 14], [13, 17], [13, 18],
    [14, 15], [14, 18], [14, 19],
    [15, 16], [15, 19], [15, 20],
    [16, 20], [16, 21],
    [17, 18], [17, 22],
    [18, 19], [18, 22], [18, 23],
    [19, 20], [19, 23], [19, 24],
    [20, 21], [20, 24],
    [21, 24], [21, 25],
    [22, 23], [22, 26],
    [23, 24], [23, 26],
    [24, 25], [24, 26],
    [25, 26]
])

# 边的下界和上界阻抗
l = np.array([0.2, 1, 0.9, 0.2, 0.9, 0.8, 1.5, 1.1, 1.5, 0.8, 0.1, 1.1, 0.5, 1.1, 0.9, 0.5, 0.7, 0.7, 1.4,
              0.9, 0.2, 0.4, 0.6, 1.1, 0.6, 0.7, 0.7, 1.2, 1.1, 1, 0.7, 0.3, 0.4, 1.2, 1.5, 0.9, 0.5, 0.9,
              0.6, 1.1, 1, 1, 1.2, 0.8, 1.2, 1, 0.5, 0.5, 0.6, 0.3, 0.9, 0.2, 1.1, 0.5, 1.2, 1.1, 0.7,
              0.9, 2, 0.8, 0.5, 0.5, 2.4])
u = np.array([4, 2, 1.8, 2.1, 1.7, 2.1, 3.2, 1.7, 2.1, 1.5, 1.8, 1.3, 1.3, 2.4, 1.5, 1.5, 1.2, 2.1, 2.3,
              1, 1.5, 2.5, 1.6, 3.5, 1.7, 1.4, 3, 1.8, 1.7, 2.4, 2.1, 1.5, 1.9, 1.9, 3.2, 1.9, 2.5, 2.5,
              2.1, 1.6, 2.8, 1.7, 2.7, 2.2, 2.5, 1.6, 1.5, 1.7, 2.2, 2.3, 2.5, 2.6, 2.4, 2.2, 2.8, 2.7, 1.8,
              2.8, 2.2, 2.3, 1.4, 3.8, 4.4])

# 起点和终点
start_node = 1
end_node = 26

# 遗传算法参数
population_size = 50  # 种群大小
num_generations = 100  # 迭代代数
mutation_rate = 0.1    # 变异率

# 随机生成初始种群
def initialize_population(size):
    population = []
    for _ in range(size):
        path = [start_node]
        while path[-1] != end_node:
            neighbors = get_neighbors(path[-1], edges)
            if len(neighbors) == 0:
                break
            next_node = np.random.choice(neighbors)
            path.append(next_node)
        population.append(path)
    return population
# 获取与指定节点相邻的所有节点
def get_neighbors(node, edges):
    return np.unique(edges[edges[:, 0] == node][:, 1])
# 初始化种群
population = initialize_population(population_size)
# 计算适应度
def calculate_fitness(population):
    fitness_scores = []
    for path in population:
        robust_cost = compute_robust_cost(path, edges, l, u)
        fitness_scores.append(1 / (robust_cost + 1e-6))  # 避免除以零
    return fitness_scores
# 选择过程（轮盘赌选择）
def select_parents(population, fitness_scores):
    total_fitness = sum(fitness_scores)
    probabilities = [f / total_fitness for f in fitness_scores]
    parents = np.random.choice(population, size=len(population), p=probabilities)
    return parents

# 计算鲁棒成本（复用之前的函数）
def compute_robust_cost(path, edges, l, u):
    max_cost = 0
    for i in range(len(path) - 1):
        edge_index = find_edge(path[i], path[i + 1], edges)
        if edge_index is not None:
            max_cost += u[edge_index]

    visited_edges = set(find_edge(path[i], path[i + 1], edges) for i in range(len(path) - 1))
    unused_edges = [i for i in range(len(edges)) if i not in visited_edges]

    min_cost = dijkstra_min_cost(unused_edges, edges, l)
    return max_cost - min_cost
# 获取与指定节点相邻的所有节点
def get_neighbors(node, edges):
    return np.unique(edges[edges[:, 0] == node][:, 1])
# 查找图中连接两个节点的边的索引
def find_edge(node1, node2, edges):
    index = np.where((edges[:, 0] == node1) & (edges[:, 1] == node2))
    return index[0][0] if index[0].size > 0 else None
def dijkstra_min_cost(unused_edges, edges, l, start_node=1, end_node=26):
    # 创建图的邻接列表
    graph = {}
    for edge in unused_edges:
        node1, node2 = edges[edge]
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        graph[node1].append((node2, l[edge]))  # (相邻节点, 下界阻抗)
        graph[node2].append((node1, l[edge]))  # 双向

    # Dijkstra 算法初始化
    min_heap = [(0, start_node)]  # (成本, 当前节点)
    min_cost = {node: float('inf') for node in graph}
    min_cost[start_node] = 0

    while min_heap:
        current_cost, current_node = heapq.heappop(min_heap)

        # 如果到达终点，返回当前成本
        if current_node == end_node:
            return current_cost

        # 遍历相邻节点
        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor))

    # 如果无法到达终点，返回无限大
    return float('inf')
def find_shortest_path(edges, l, u, start_node, end_node, cost_type='lower'):
    graph = {}
    for i, (node1, node2) in enumerate(edges):
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        if cost_type == 'lower':
            graph[node1].append((node2, l[i]))
            graph[node2].append((node1, l[i]))
        else:
            graph[node1].append((node2, u[i]))
            graph[node2].append((node1, u[i]))

    # Dijkstra 算法初始化
    min_heap = [(0, start_node, [])]  # (成本, 当前节点, 路径)
    min_cost = {node: float('inf') for node in graph}
    min_cost[start_node] = 0

    while min_heap:
        current_cost, current_node, path = heapq.heappop(min_heap)
        path = path + [current_node]

        # 如果到达终点，返回当前路径
        if current_node == end_node:
            return path

        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor, path))

    return []  # 如果无法到达终点，返回空路径

# 在这里可以调用种群和适应度评估
fitness_scores = calculate_fitness(population)
parents = select_parents(population, fitness_scores)

# 交叉操作
def crossover(parent1, parent2):
    # 随机选择交叉点
    crossover_point = np.random.randint(1, len(parent1) - 1)
    child = parent1[:crossover_point] + [node for node in parent2 if node not in parent1[:crossover_point]]
    return child
# 变异操作
def mutate(path, mutation_rate=0.1):
    for i in range(len(path)):
        if np.random.rand() < mutation_rate:
            # 随机选择一个新节点替换
            new_node = np.random.choice(nodes)
            path[i] = new_node
    return path
# 生成下一代
def create_next_generation(parents):
    next_generation = []
    num_parents = len(parents)

    for _ in range(num_parents // 2):
        parent1, parent2 = np.random.choice(parents, size=2, replace=False)
        child1 = crossover(parent1, parent2)
        child2 = crossover(parent2, parent1)

        # 变异
        child1 = mutate(child1)
        child2 = mutate(child2)

        next_generation.extend([child1, child2])

    return next_generation[:num_parents]  # 保证下一代数量一致


# 主程序
# 记录整个程序的运行时间
start_time = time.time()

# 初始化种群
population_size = 100
population = [construct_solution(pheromones, nodes, edges, l, u) for _ in range(population_size)]

# 迭代演化
num_generations = 100

best_path = []
best_cost = float('inf')

for generation in range(num_generations):
    # 计算每个个体的鲁棒成本
    costs = [compute_robust_cost(path, edges, l, u) for path in population]

    # 更新最佳路径
    for i, cost in enumerate(costs):
        if cost < best_cost:
            best_cost = cost
            best_path = population[i]

    # 选择优秀个体
    sorted_population = sorted(population, key=lambda x: x['cost'])
    parents = [ind['path'] for ind in sorted_population[:population_size // 2]]

    # 生成下一代
    next_generation = []

    # 轮盘赌选择
    costs = np.array([ind['cost'] for ind in sorted_population])
    probabilities = 1 / costs  # 使用成本的倒数
    probabilities /= probabilities.sum()  # 归一化

    # 按照概率选择父代
    for _ in range(population_size):
        selected_index = np.random.choice(range(len(parents)), p=probabilities[:len(parents)])
        next_generation.append(parents[selected_index])

    population = next_generation

# 计算总路网的下界和上界最短路径
lower_bound_path = find_shortest_path(edges, l, u, start_node, end_node, cost_type='lower')
upper_bound_path = find_shortest_path(edges, l, u, start_node, end_node, cost_type='upper')

end_time = time.time()

# 输出结果
print("最佳路径:", [int(node) for node in best_path])
print("最佳鲁棒成本:", best_cost)
print("运行时间:", end_time - start_time, "秒")
print("总路网下界最短路径:", [int(node) for node in lower_bound_path])
print("总路网上界最短路径:", [int(node) for node in upper_bound_path])


