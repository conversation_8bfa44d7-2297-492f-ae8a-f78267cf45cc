"""
还可以用于寻找情景最短路
"""

import networkx as nx
from soupsieve.util import lower

# 定义路网的边 (起点, 终点, 下界阻抗, 上界阻抗)
edges = [
    (1, 2, 3, 6), (2, 1, 3, 6), (1, 3, 2, 5), (3, 1, 2, 5), (1, 4, 4, 7), (4, 1, 4, 7), (1, 5, 3, 6), (5, 1, 3, 6),
    (2, 3, 3, 7), (3, 2, 3, 7), (2, 6, 5, 7), (6, 2, 5, 7), (2, 7, 2, 6), (7, 2, 2, 6), (3, 4, 2, 5), (4, 3, 2, 5),
    (3, 7, 5, 7), (7, 3, 5, 7), (3, 8, 3, 6), (8, 3, 3, 6), (4, 5, 3, 6), (5, 4, 3, 6), (4, 8, 5, 7), (8, 4, 5, 7),
    (4, 9, 2, 5), (9, 4, 2, 5), (5, 9, 2, 5), (9, 5, 2, 5), (5, 10, 3, 7), (10, 5, 3, 7), (6, 7, 2, 6), (7, 6, 2, 6),
    (6, 11, 3, 5), (11, 6, 3, 5), (7, 8, 3, 6), (8, 7, 3, 6), (7, 11, 2, 5), (11, 7, 2, 5), (7, 12, 3, 6),
    (12, 7, 3, 6),
    (8, 9, 5, 7), (9, 8, 5, 7), (8, 12, 2, 6), (12, 8, 2, 6), (8, 13, 3, 5), (13, 8, 3, 5), (9, 10, 2, 5),
    (10, 9, 2, 5),
    (9, 13, 5, 7), (13, 9, 5, 7), (9, 14, 3, 6), (14, 9, 3, 6), (10, 14, 3, 6), (14, 10, 3, 6), (10, 15, 5, 7),
    (15, 10, 5, 7),
    (11, 12, 4, 7), (12, 11, 4, 7), (11, 16, 2, 5), (16, 11, 2, 5), (11, 17, 3, 6), (17, 11, 3, 6), (12, 13, 5, 7),
    (13, 12, 5, 7),
    (12, 17, 2, 5), (17, 12, 2, 5), (12, 18, 3, 6), (18, 12, 3, 6), (13, 14, 2, 5), (14, 13, 2, 5), (13, 18, 3, 6),
    (18, 13, 3, 6),
    (13, 19, 5, 7), (19, 13, 5, 7), (14, 15, 2, 5), (15, 14, 2, 5), (14, 19, 3, 6), (19, 14, 3, 6), (15, 19, 4, 7),
    (19, 15, 4, 7),
    (16, 17, 3, 6), (17, 16, 3, 6), (16, 20, 4, 7), (20, 16, 4, 7), (17, 18, 2, 6), (18, 17, 2, 6), (17, 20, 3, 7),
    (20, 17, 3, 7),
    (18, 19, 3, 7), (19, 18, 3, 7), (18, 20, 2, 7), (20, 18, 2, 7), (19, 20, 2, 7), (20, 19, 2, 7)
]

# 创建图
G = nx.DiGraph()  # 有向图

# 添加边到图中，权重为下界阻抗
for edge in edges:
    u, v, lower_bound, upper_bound = edge
    G.add_edge(u, v, weight=lower_bound)

# 使用 Dijkstra 算法计算最短路径
start_node = 1
end_node = 20
shortest_path = nx.dijkstra_path(G, source=start_node, target=end_node, weight='weight')
shortest_path_edges = [(shortest_path[i], shortest_path[i+1]) for i in range(len(shortest_path)-1)]

# 计算路径的下界阻抗总和
total_lower_bound = sum(G[u][v]['weight'] for u, v in shortest_path_edges)

# 输出最短路径和下界阻抗总和
print("最短路径:", shortest_path)
print("路径的下界阻抗总和:", total_lower_bound)
