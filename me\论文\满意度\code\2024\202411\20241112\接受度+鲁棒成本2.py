# 改算法
# 目标函数 = 鲁棒成本 + （2yum - sum（u+l））
# 不行


import gurobipy as gp
from gurobipy import GRB
import networkx as nx
from gurobipy import *
import matplotlib.pyplot as plt


# 初始化模型
model = gp.Model("RobustShortestPath")

# 节点集合和路段数据
C = [i for i in range(1, 30)]  # 节点集合
V = [
    (1, 2, 6, 10), (1, 3, 6, 8), (2, 3, 6, 10), (1, 4, 5, 9), (3, 4, 6, 10), (1, 5, 3, 5), (4, 5, 1, 4),
    (2, 6, 6, 10), (3, 6, 6, 10), (3, 7, 6, 8), (6, 7, 6, 10), (3, 8, 6, 10), (4, 8, 6, 8), (7, 8, 6, 10),
    (4, 9, 3, 9), (8, 9, 6, 10), (4, 10, 6, 10), (5, 10, 3, 10), (9, 10, 6, 10), (6, 11, 6, 10), (7, 11, 6, 10),
    (7, 12, 6, 8), (11, 12, 6, 10), (7, 13, 6, 10), (8, 13, 6, 8), (12, 13, 6, 10), (8, 14, 6, 10), (9, 14, 6, 8),
    (13, 14, 6, 10), (9, 15, 3, 9), (10, 15, 6, 12), (14, 15, 6, 10), (10, 16, 3, 10), (15, 16, 6, 10),
    (11, 17, 6, 10),
    (12, 17, 6, 10), (12, 18, 6, 8), (13, 18, 6, 8), (17, 18, 6, 10), (13, 19, 6, 10), (14, 19, 6, 8),
    (18, 19, 6, 10),
    (14, 20, 6, 10), (15, 20, 4, 9), (19, 20, 6, 10), (15, 21, 6, 10), (16, 21, 3, 10), (20, 21, 6, 10),
    (17, 22, 6, 10),
    (18, 22, 6, 10), (18, 23, 6, 8), (19, 23, 6, 8), (22, 23, 6, 10), (19, 24, 6, 8), (20, 24, 5, 10),
    (23, 24, 6, 10),
    (20, 25, 6, 10), (21, 25, 3, 11), (24, 25, 6, 10), (22, 26, 6, 10), (23, 26, 6, 10), (23, 27, 6, 8),
    (24, 27, 4, 9),
    (26, 27, 2, 3), (24, 28, 6, 10), (25, 28, 3, 10), (27, 28, 6, 10), (26, 29, 5, 13), (27, 29, 2, 4),
    (28, 29, 3, 13),
    (2, 1, 6, 10), (3, 1, 6, 8), (3, 2, 6, 10), (4, 1, 5, 9), (4, 3, 6, 10), (5, 1, 3, 5), (5, 4, 1, 4),
    (6, 2, 6, 10), (6, 3, 6, 10), (7, 3, 6, 8), (7, 6, 6, 10), (8, 3, 6, 10), (8, 4, 6, 8), (8, 7, 6, 10),
    (9, 4, 3, 9), (9, 8, 6, 10), (10, 4, 6, 10), (10, 5, 3, 10), (10, 9, 6, 10), (11, 6, 6, 10), (11, 7, 6, 10),
    (12, 7, 6, 8), (12, 11, 6, 10), (13, 7, 6, 10), (13, 8, 6, 8), (13, 12, 6, 10), (14, 8, 6, 10), (14, 9, 6, 8),
    (14, 13, 6, 10), (15, 9, 3, 9), (15, 10, 6, 12), (15, 14, 6, 10), (16, 10, 3, 10), (16, 15, 6, 10),
    (17, 11, 6, 10),
    (17, 12, 6, 10), (18, 12, 6, 8), (18, 13, 6, 8), (18, 17, 6, 10), (19, 13, 6, 10), (19, 14, 6, 8),
    (19, 18, 6, 10),
    (20, 14, 6, 10), (20, 15, 4, 9), (20, 19, 6, 10), (21, 15, 6, 10), (21, 16, 3, 10), (21, 20, 6, 10),
    (22, 17, 6, 10),
    (22, 18, 6, 10), (23, 18, 6, 8), (23, 19, 6, 8), (23, 22, 6, 10), (24, 19, 6, 8), (24, 20, 5, 10),
    (24, 23, 6, 10),
    (25, 20, 6, 10), (25, 21, 3, 11), (25, 24, 6, 10), (26, 22, 6, 10), (26, 23, 6, 10), (27, 23, 6, 8),
    (27, 24, 4, 9),
    (27, 26, 2, 3), (28, 24, 6, 10), (28, 25, 3, 10), (28, 27, 6, 10), (29, 26, 5, 13), (29, 27, 2, 4),
    (29, 28, 3, 13)
]
G = nx.DiGraph()
for k in range(len(V)):
    G.add_edge(V[k][0], V[k][1], time_lb=V[k][2], time_ub=V[k][3], time_mid=(V[k][2] + V[k][3]) / 2)
l_ij = {(i, j): l for i, j, l, u in V}  # 下界阻抗
u_ij = {(i, j): u for i, j, l, u in V}  # 上界阻抗
y_u_m = 45  # 参照路径的区间平均值
r, t = 1, 29  # 起点和终点

# 决策变量
p = model.addVars([(i, j) for i, j, _, _ in V], vtype=GRB.BINARY, name="p")  # 路段选取变量
x = model.addVars(C, vtype=GRB.CONTINUOUS, name="x", lb=0)  # 情景变量
x[r] = 0  # 起点变量设为0
f = model.addVars([(i, j) for i, j, _, _ in V], vtype=GRB.BINARY, name="f")  # 情景最短路径变量
visited = model.addVars(C, vtype=GRB.BINARY, name="visited")  # 节点访问变量

# 用于存储最终连贯路径的列表
selected_path = []
# selected_path = [(i, j) for i, j in l_ij if p[i, j].x > 0.5]


# 计算 z_y 部分
z_y = gp.quicksum(u_ij[i, j] * p[i, j] for i, j in l_ij) - x[t]

# 计算 h(y < y^u) 部分
h_y = 2 * y_u_m - gp.quicksum((u_ij[i, j] + l_ij[i, j]) * p[i, j] for i, j in l_ij)


# 目标函数
def calculate_path_coherence_cost(path):
    cost = 0
    for i in range(len(path) - 1):
        if path[i][1]!= path[i + 1][0]:
            cost += 10  # 若相邻路段不连贯，增加较大惩罚值
    return cost

path_coherence_cost = calculate_path_coherence_cost(selected_path)
model.setObjective(z_y + h_y + path_coherence_cost, GRB.MINIMIZE)

# 约束条件
for i, j in l_ij:
    model.addConstr(x[t] <= x[i] + l_ij[i, j] + (u_ij[i, j] - l_ij[i, j]) * p[i, j])

model.addConstr(x[r] == 0)

for j in C:
    if j == r:
        model.addConstr(gp.quicksum(p[i, j] for i, j in l_ij if j == r) - gp.quicksum(p[j, k] for j, k in l_ij if j == r) == -1)
    elif j == t:
        model.addConstr(gp.quicksum(p[i, j] for i, j in l_ij if j == t) - gp.quicksum(p[j, k] for j, k in l_ij if j == t) == 1)
    else:
        incoming_segments = [p[i, j] for i, j in l_ij if j == j]
        outgoing_segments = [p[j, k] for j, k in l_ij if j == j]
        model.addConstr(gp.quicksum(incoming_segments) == visited[j])
        model.addConstr(gp.quicksum(outgoing_segments) == visited[j])

model.addConstr(2 * y_u_m - gp.quicksum((u_ij[i, j] + l_ij[i, j]) * p[i, j] for i, j in l_ij) >= 0)

# 起点和终点的特殊处理
model.addConstr(visited[r] == 1)
model.addConstr(visited[t] == 1)

# 求解模型
model.optimize()

# 检查模型是否求解成功并找到最优解
if model.status == GRB.OPTIMAL:
    # 获取优化后的目标值
    print(f"Objective Value (min W_y): {model.objVal}")

    # 计算 z_y 和 h_y 的具体值
    z_y_val = gp.quicksum(u_ij[i, j] * p[i, j].x for i, j in l_ij) - x[t].x
    print(f"Calculated z_y: {z_y_val}")

    h_y_val = 2 * y_u_m - gp.quicksum((u_ij[i, j] + l_ij[i, j]) * p[i, j].x for i, j in l_ij)
    print(f"Calculated h_y: {h_y_val}")

    # 输出选择的路径并构建连贯路径
    connected_path = []
    current_node = r
    for segment in selected_path:
        if segment[0] == current_node:
            connected_path.append(segment)
            current_node = segment[1]

    print("Connected Path:")
    for i in range(len(connected_path)):
        print(f"({connected_path[i][0]}, {connected_path[i][1]})", end="")
    if i < len(connected_path) - 1:
        print(" -> ", end="")
else:
    print("No optimal solution found.")