import networkx as nx
import matplotlib.pyplot as plt

# 路网坐标
coordinates = {
    1: (0.46035, 5.0452), 2: (0.73589, 7.177), 3: (1.4617, 6.0917), 4: (1.6095, 4.4897), 5: (1.0316, 3.1072),
    6: (1.5827, 8.4173), 7: (2.2749, 7.1641), 8: (2.7251, 5.3165), 9: (2.6781, 3.624), 10: (2.127, 1.7765),
    11: (2.7722, 9.1279), 12: (3.4778, 7.7842), 13: (3.8071, 6.2726), 14: (3.8542, 4.2313), 15: (3.6929, 2.5517),
    16: (3.2359, 0.67829), 17: (4.5128, 9.0504), 18: (5.0302, 7.5), 19: (5.2587, 5.3036), 20: (5.2386, 3.2235),
    21: (4.8353, 1.1305), 22: (6.0921, 8.6886), 23: (6.4751, 6.7119), 24: (6.5558, 4.593), 25: (6.334, 2.0995),
    26: (7.5034, 8.1202), 27: (7.8058, 5.8075), 28: (7.584, 3.1589), 29: (8.9751, 5.7041)
}

# 路网结构
roads = [
    (1, 2), (1, 3), (2, 3), (1, 4), (3, 4), (1, 5), (4, 5), (2, 6), (3, 6), (3, 7), (6, 7),
    (3, 8), (4, 8), (7, 8), (4, 9), (8, 9), (4, 10), (5, 10), (9, 10), (6, 11), (7, 11),
    (7, 12), (11, 12), (7, 13), (8, 13), (12, 13), (8, 14), (9, 14), (13, 14), (9, 15),
    (10, 15), (14, 15), (10, 16), (15, 16), (11, 17), (12, 17), (12, 18), (13, 18), (17, 18),
    (13, 19), (14, 19), (18, 19), (14, 20), (15, 20), (19, 20), (15, 21), (16, 21), (20, 21),
    (17, 22), (18, 22), (18, 23), (19, 23), (22, 23), (19, 24), (20, 24), (23, 24), (20, 25),
    (21, 25), (24, 25), (22, 26), (23, 26), (23, 27), (24, 27), (26, 27), (24, 28), (25, 28),
    (27, 28), (26, 29), (27, 29), (28, 29), (2, 1), (3, 1), (3, 2), (4, 1), (4, 3), (5, 1),
    (5, 4), (6, 2), (6, 3), (7, 3), (7, 6), (8, 3), (8, 4), (8, 7), (9, 4), (9, 8), (10, 4),
    (10, 5), (10, 9), (11, 6), (11, 7), (12, 7), (12, 11), (13, 7), (13, 8), (13, 12), (14, 8),
    (14, 9), (14, 13), (15, 9), (15, 10), (15, 14), (16, 10), (16, 15), (17, 11), (17, 12),
    (18, 12), (18, 13), (18, 17), (19, 13), (19, 14), (19, 18), (20, 14), (20, 15), (20, 19),
    (21, 15), (21, 16), (21, 20), (22, 17), (22, 18), (23, 18), (23, 19), (23, 22), (24, 19),
    (24, 20), (24, 23), (25, 20), (25, 21), (25, 24), (26, 22), (26, 23), (27, 23), (27, 24),
    (27, 26), (28, 24), (28, 25), (28, 27), (29, 26), (29, 27), (29, 28)
]

# 替换为实际找到的路径（示例路径）
path = [1, 3, 7, 12, 17, 22, 26, 29]

# 创建图
G = nx.Graph()
for node in coordinates:
    G.add_node(node)
for u, v in roads:
    G.add_edge(u, v)

# 绘制基础路网
pos = coordinates
plt.figure(figsize=(10, 8))
nx.draw_networkx_edges(G, pos, edge_color='gray', alpha=0.5, width=1)
nx.draw_networkx_nodes(G, pos, node_size=500, node_color='blue')
nx.draw_networkx_labels(G, pos, font_size=10)

# 绘制路径
path_edges = list(zip(path[:-1], path[1:]))
nx.draw_networkx_edges(G, pos, edgelist=path_edges, edge_color='red', width=2)

# 突出显示起点和终点
nx.draw_networkx_nodes(G, pos, nodelist=[path[0]], node_size=800, node_color='green')
nx.draw_networkx_nodes(G, pos, nodelist=[path[-1]], node_size=800, node_color='red')

# 设置坐标轴范围
x_coords = [coord[0] for coord in coordinates.values()]
y_coords = [coord[1] for coord in coordinates.values()]
plt.xlim(min(x_coords)-0.5, max(x_coords)+0.5)
plt.ylim(min(y_coords)-0.5, max(y_coords)+0.5)

plt.title("Optimal Path from Node 1 to Node 29")
plt.axis('off')
plt.show()