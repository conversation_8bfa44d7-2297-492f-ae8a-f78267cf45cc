import networkx as nx
from gurobipy import *
import matplotlib.pyplot as plt

alpha = 0.5  # 权重系数
SP_min = 21  # 下界最短路
SP_max = 52  # 上界最短路
r = 1  # 起点
t = 29  # 终点
N = [i for i in range(1, 30)]
N_rt = [r, t]
N_center = N.copy()
for k in [r, t]:
    N_center.remove(k)
Links = [(1, 2, 6, 10), (1, 3, 6, 8), (2, 3, 6, 10), (1, 4, 5, 9), (3, 4, 6, 10), (1, 5, 3, 5), (4, 5, 1, 4),
         (2, 6, 6, 10), (3, 6, 6, 10), (3, 7, 6, 8), (6, 7, 6, 10), (3, 8, 6, 10), (4, 8, 6, 8), (7, 8, 6, 10),
         (4, 9, 3, 9), (8, 9, 6, 10), (4, 10, 6, 10), (5, 10, 3, 10), (9, 10, 6, 10), (6, 11, 6, 10), (7, 11, 6, 10),
         (7, 12, 6, 8), (11, 12, 6, 10), (7, 13, 6, 10), (8, 13, 6, 8), (12, 13, 6, 10), (8, 14, 6, 10), (9, 14, 6, 8),
         (13, 14, 6, 10), (9, 15, 3, 9), (10, 15, 6, 12), (14, 15, 6, 10), (10, 16, 3, 10), (15, 16, 6, 10),
         (11, 17, 6, 10),
         (12, 17, 6, 10), (12, 18, 6, 8), (13, 18, 6, 8), (17, 18, 6, 10), (13, 19, 6, 10), (14, 19, 6, 8),
         (18, 19, 6, 10),
         (14, 20, 6, 10), (15, 20, 4, 9), (19, 20, 6, 10), (15, 21, 6, 10), (16, 21, 3, 10), (20, 21, 6, 10),
         (17, 22, 6, 10),
         (18, 22, 6, 10), (18, 23, 6, 8), (19, 23, 6, 8), (22, 23, 6, 10), (19, 24, 6, 8), (20, 24, 5, 10),
         (23, 24, 6, 10),
         (20, 25, 6, 10), (21, 25, 3, 11), (24, 25, 6, 10), (22, 26, 6, 10), (23, 26, 6, 10), (23, 27, 6, 8),
         (24, 27, 4, 9),
         (26, 27, 2, 3), (24, 28, 6, 10), (25, 28, 3, 10), (27, 28, 6, 10), (26, 29, 5, 13), (27, 29, 2, 4),
         (28, 29, 3, 13),
         (2, 1, 6, 10), (3, 1, 6, 8), (3, 2, 6, 10), (4, 1, 5, 9), (4, 3, 6, 10), (5, 1, 3, 5), (5, 4, 1, 4),
         (6, 2, 6, 10), (6, 3, 6, 10), (7, 3, 6, 8), (7, 6, 6, 10), (8, 3, 6, 10), (8, 4, 6, 8), (8, 7, 6, 10),
         (9, 4, 3, 9), (9, 8, 6, 10), (10, 4, 6, 10), (10, 5, 3, 10), (10, 9, 6, 10), (11, 6, 6, 10), (11, 7, 6, 10),
         (12, 7, 6, 8), (12, 11, 6, 10), (13, 7, 6, 10), (13, 8, 6, 8), (13, 12, 6, 10), (14, 8, 6, 10), (14, 9, 6, 8),
         (14, 13, 6, 10), (15, 9, 3, 9), (15, 10, 6, 12), (15, 14, 6, 10), (16, 10, 3, 10), (16, 15, 6, 10),
         (17, 11, 6, 10),
         (17, 12, 6, 10), (18, 12, 6, 8), (18, 13, 6, 8), (18, 17, 6, 10), (19, 13, 6, 10), (19, 14, 6, 8),
         (19, 18, 6, 10),
         (20, 14, 6, 10), (20, 15, 4, 9), (20, 19, 6, 10), (21, 15, 6, 10), (21, 16, 3, 10), (21, 20, 6, 10),
         (22, 17, 6, 10),
         (22, 18, 6, 10), (23, 18, 6, 8), (23, 19, 6, 8), (23, 22, 6, 10), (24, 19, 6, 8), (24, 20, 5, 10),
         (24, 23, 6, 10),
         (25, 20, 6, 10), (25, 21, 3, 11), (25, 24, 6, 10), (26, 22, 6, 10), (26, 23, 6, 10), (27, 23, 6, 8),
         (27, 24, 4, 9),
         (27, 26, 2, 3), (28, 24, 6, 10), (28, 25, 3, 10), (28, 27, 6, 10), (29, 26, 5, 13), (29, 27, 2, 4),
         (29, 28, 3, 13)]
G = nx.DiGraph()
for k in range(len(Links)):
    G.add_edge(Links[k][0], Links[k][1], time=[Links[k][2], Links[k][3]],
               time_lb=Links[k][2], time_ub=Links[k][3], time_mid=(Links[k][2] + Links[k][3]) / 2)
time = nx.get_edge_attributes(G, 'time')
time_lb = nx.get_edge_attributes(G, 'time_lb')
time_ub = nx.get_edge_attributes(G, 'time_ub')
# 路网中的所有边
L = []
for item in time_lb.items():
    L.append(item[0])
Lt = tuplelist(L)


# 主问题（列生成问题）
def create_master_problem():
    m = Model('masterproblem')
    z = m.addVar(vtype=GRB.CONTINUOUS, name='z')  # 目标变量
    p = m.addVars(((i, j) for i, j in L), vtype=GRB.BINARY, name='p')  # 路径决策变量
    m.setObjective(z, GRB.MINIMIZE)  # 目标函数
    return m, z, p


# 子问题设置
def solve_subproblem(p):
    d = Model('subproblem')
    w = d.addVars(L, vtype=GRB.BINARY, name='w')  # 边选择决策变量
    d.update()  # 确保变量添加到模型中

    # 将主问题的 p[i, j] 变量传递到子问题
    # 你可以通过设置变量的属性来传递 p[i, j] 的值
    for (i, j) in L:
        w[i, j].start = p[i, j].x  # 使用主问题中 p[i, j] 的值来初始化 w[i, j]

    # 重新构造目标函数
    objective = (alpha * (quicksum(time_ub[i, j] * p[i, j] for i, j in L) -
                          quicksum(
                              (time_lb[i, j] + (time_ub[i, j] - time_lb[i, j]) * p[i, j]) * w[i, j].start for i, j in
                              L)) +
                 (1 - alpha) * (quicksum((time_lb[i, j] + time_ub[i, j]) * p[i, j] for i, j in L) - SP_min - SP_max))

    d.setObjective(objective, GRB.MAXIMIZE)

    return d


# 主算法流程
def dantzig_wolfe():
    m, z, p = create_master_problem()
    iteration = 0
    columns_added = True  # 标记是否有新列被添加
    while iteration < 8 and columns_added:
        columns_added = False  # 每次迭代开始时，假设没有新增列
        print(f"*** 第 {iteration + 1} 次求解子问题 ***")
        d = solve_subproblem(p)
        d.optimize()  # 求解子问题
        print(f"第 {iteration + 1} 次子问题目标函数值: {d.objVal}")

        # 生成新的列并更新主问题
        # 根据子问题的最优解，决定是否生成新的列
        new_columns = []  # 存储新增的列
        for i, j in L:
            if d.getVarByName(f"w[{i},{j}]").x > 0.5:  # 选择最优的路径或列
                new_columns.append((i, j))
                columns_added = True  # 有新列被添加

        # 如果有新增列，则更新主问题
        if columns_added:
            print("生成并添加新的列到主问题")
            # 将新的列（决策变量）添加到主问题
            for (i, j) in new_columns:
                p[i, j].start = 1  # 添加决策变量并初始化
            m.optimize()
            print(f"第 {iteration + 1} 次主问题目标函数值: {m.objVal}")

        iteration += 1


# 调用主算法
dantzig_wolfe()
