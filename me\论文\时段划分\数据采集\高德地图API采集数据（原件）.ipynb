{"cells": [{"cell_type": "code", "id": "f1986961", "metadata": {"ExecuteTime": {"end_time": "2025-07-21T02:39:47.696560Z", "start_time": "2025-07-21T02:39:47.690599Z"}}, "source": ["import json\n", "from urllib import request\n", "import time\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题\n", "OD = open('D:/Desktop/高德数据采集/脚本节点.txt').read().splitlines()\n", "#speedlane_location.txt是存放起终点经纬度坐标的文件，其中精度、纬度和起点、终点都是以“Tab”键作为分隔。\n", "#需要明确TXT文件中每一组起终点坐标对应的是哪段路，同时需要手动记录下代码开始的时间，用于后续绘图、分析用。"], "outputs": [], "execution_count": 6}, {"cell_type": "markdown", "id": "8708836d", "metadata": {}, "source": ["抓取数据并保存"]}, {"metadata": {"ExecuteTime": {"end_time": "2025-07-21T02:39:52.517454Z", "start_time": "2025-07-21T02:39:52.499009Z"}}, "cell_type": "code", "outputs": [{"ename": "NameError", "evalue": "name 'csv' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 7\u001b[0m\n\u001b[0;32m      5\u001b[0m \u001b[38;5;66;03m# 打开CSV文件用于存放数据\u001b[39;00m\n\u001b[0;32m      6\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(output_path, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mw\u001b[39m\u001b[38;5;124m'\u001b[39m, newline\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m'\u001b[39m, encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m output:\n\u001b[1;32m----> 7\u001b[0m     writer \u001b[38;5;241m=\u001b[39m \u001b[43mcsv\u001b[49m\u001b[38;5;241m.\u001b[39mwriter(output)\n\u001b[0;32m      8\u001b[0m     \u001b[38;5;66;03m# 写入CSV文件的头部\u001b[39;00m\n\u001b[0;32m      9\u001b[0m     writer\u001b[38;5;241m.\u001b[39mwriterow([\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDistance (m)\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDuration (s)\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSpeed (km/h)\u001b[39m\u001b[38;5;124m'\u001b[39m])\n", "\u001b[1;31mNameError\u001b[0m: name 'csv' is not defined"]}], "execution_count": 7, "source": ["url_base = 'https://restapi.amap.com/v5/direction/driving?key=7fc11539ead7b69f460073d205714b4b&origin={0},{1}&destination={2},{3}&show_fields=cost'\n", "#根据高德地图路径规划（2.0版）形式创建需要访问的网页，上面的key是Web服务（不需要密码）key不是Web端（需要密码）key\n", "output_path = 'speed-result.csv'  # 输出文件路径，不需要提前创建这个csv文件。\n", "\n", "# 打开CSV文件用于存放数据\n", "with open(output_path, 'w', newline='', encoding='utf-8') as output:\n", "    writer = csv.writer(output)\n", "    # 写入CSV文件的头部\n", "    writer.writerow(['Distance (m)', 'Duration (s)', 'Speed (km/h)'])\n", "    # 设置循环次数限制（取决于采集时间长度，时间长度=max_iterations *  time.sleep()中的时间）\n", "    max_iterations = 24\n", "    iteration_count = 0\n", "    while iteration_count < max_iterations:\n", "        for i in OD:\n", "            x1, y1, x2, y2 = i.split('\\t')#起终点数据以‘Tab’（\\t）为分隔形式存储\n", "            url = url_base.format(x1, y1, x2, y2)\n", "            #利用try函数检查运行途中因网络等原因可能出现的错误\n", "            try:\n", "                html = request.urlopen(url, timeout=15).read()\n", "                #timeout设置访问间隔参数，不能设置过小。\n", "                #如果在15秒内没有收到服务器的响应，就会抛出一个 urllib.error.URLError 异常。\n", "                #可以避免代码无限期地等待一个可能永远不会完成的网络请求。\n", "                js = json.loads(html)\n", "#将网址复制到火狐浏览器中查看数据存放的位置（使用火狐浏览器是因为能够直接看到JSON页面信息）\n", "#由此写出下面代码中对应的d、c表达式（该步骤已完成，仅为需要抓取其他数据时用）\n", "#注意在复制网址时将其中一个起终点的经纬度代入到上面的url_base中。\n", "                d = js['route']['paths'][0]['distance']  # 返回起终点距离（米）\n", "                c = js['route']['paths'][0]['cost']['duration']  # 返回起终点驾车用时（秒）\n", "                v = round((float(d) / float(c)) * 3.6, 2)  # 计算速度（千米/小时）并进行单位换算\n", "                writer.writerow([d, c, v])  # 写入数据到CSV文件\n", "            except Exception as e:\n", "                print(repr(e))\n", "                writer.writerow([url])  # 发生错误时，写入URL到CSV文件\n", "        iteration_count += 1\n", "        time.sleep(3600)  # 抓取车速数据的时间间隔（s），建议不要设置太小，可能由于网络延迟报错。"], "id": "c520bafc"}, {"cell_type": "markdown", "id": "ecc46fdc", "metadata": {}, "source": ["处理csv文件，将其重新排序（等上面的代码运行完成之后再运行下面的代码）"]}, {"cell_type": "code", "execution_count": 11, "id": "8dcf94e4", "metadata": {}, "outputs": [], "source": ["# 读取CSV文件\n", "data = pd.read_csv('speed-result.csv')#注意修改成自己的路径\n", "\n", "# 使用pandas进行排序\n", "data_sorted = data.sort_values(by='Distance (m)')\n", "\n", "# 写入排序后的数据到CSV文件\n", "data_sorted.to_csv('speed-result.csv', index=False)#注意修改成自己的路径，与上面的data为同一路径。"]}, {"cell_type": "markdown", "id": "c71d176b", "metadata": {}, "source": ["绘制时间-速度折线图（需要自己在csv文件中把time字段填充好，并且是时：分：秒格式）"]}, {"metadata": {}, "cell_type": "code", "outputs": [{"ename": "NameError", "evalue": "name 'pd' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 3\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# 加载CSV文件\u001b[39;00m\n\u001b[0;32m      2\u001b[0m file_path \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mspeed-result.csv\u001b[39m\u001b[38;5;124m'\u001b[39m  \u001b[38;5;66;03m# 替换为你的CSV文件路径\u001b[39;00m\n\u001b[1;32m----> 3\u001b[0m df \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mread_csv(file_path)\n\u001b[0;32m      5\u001b[0m \u001b[38;5;66;03m# 将'time'列转换为时间对象\u001b[39;00m\n\u001b[0;32m      6\u001b[0m df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtime\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m pd\u001b[38;5;241m.\u001b[39mto_datetime(df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtime\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;241m.\u001b[39mastype(\u001b[38;5;28mstr\u001b[39m), \u001b[38;5;28mformat\u001b[39m\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mH:\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mM:\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mS\u001b[39m\u001b[38;5;124m'\u001b[39m)\u001b[38;5;241m.\u001b[39mdt\u001b[38;5;241m.\u001b[39mtime\n", "\u001b[1;31mNameError\u001b[0m: name 'pd' is not defined"]}], "execution_count": 1, "source": ["# 加载CSV文件\n", "file_path = 'speed-result.csv'  # 替换为你的CSV文件路径\n", "df = pd.read_csv(file_path)\n", "\n", "# 将'time'列转换为时间对象\n", "df['time'] = pd.to_datetime(df['time'].astype(str), format='%H:%M:%S').dt.time\n", "\n", "# 定义一个函数，将时间转换为一天开始以来的秒数\n", "def time_to_seconds(t):\n", "    return t.hour * 3600 + t.minute * 60 + t.second\n", "\n", "# 应用函数到'time'列，创建一个'time_since_start'列\n", "df['time_since_start'] = df['time'].apply(lambda t: time_to_seconds(t))\n", "\n", "# 定义一个函数，将秒数格式化为小时和分钟\n", "def time_to_hours_minutes(s):\n", "    return f\"{int(s // 3600):02d}:{int((s % 3600) // 60):02d}\"\n", "\n", "# 创建图表\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 获取唯一的距离值以进行绘图\n", "unique_distances = df['Distance (m)'].unique()\n", "\n", "# 定义一个字典来映射距离到图例名称\n", "#注意csv文件在重排序后会按照Distance (m)由小到大的顺序排列，所以需要提前记下每段路的对应的长度，用于确定路段对应的速度。\n", "#因为此时与TXT文本中路段的顺序会不一致\n", "#下面的图例内容自定义\n", "distance_to_legend = {\n", "    unique_distances[0]: '路段1',\n", "    unique_distances[1]: '路段2',\n", "    unique_distances[2]: '路段3',\n", "    unique_distances[3]: '路段4'\n", "}\n", "\n", "# 绘制每个组的数据\n", "for distance in unique_distances:\n", "    subset = df[df['Distance (m)'] == distance]\n", "    # 使用字典来获取对应的图例名称\n", "    legend_name = distance_to_legend.get(distance, f\"Distance: {distance} m\")\n", "    plt.plot(subset['time_since_start'], subset['Speed (km/h)'], marker='o', label=legend_name)\n", "\n", "# 设置x轴的定制刻度标签，只显示小时和分钟\n", "xticks = plt.xticks()[0]\n", "plt.xticks(xticks, labels=[time_to_hours_minutes(x) for x in xticks])\n", "\n", "# 完成图表\n", "plt.xlabel('采集时间')\n", "plt.ylabel('速度(km/h)')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"], "id": "b297cd89"}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}, "vp": {"vp_config_version": "1.0.0", "vp_menu_width": 273, "vp_note_display": false, "vp_note_width": 0, "vp_position": {"width": 278}, "vp_section_display": false, "vp_signature": "VisualPython"}}, "nbformat": 4, "nbformat_minor": 5}