import pyomo.environ as pyo
from pyomo.opt import SolverFactory

# 数据准备（示例数据，需根据实际调整）
nodes = list(range(1, 54))  # 53个节点
centers = [1, 2, 3]  # 应急物资集散中心
disaster_nodes = list(range(17, 47))  # 受灾点
vehicles = list(range(1, 7))  # 6辆车
arcs = [(i, j) for i in nodes for j in nodes if i != j]  # 所有可能路段
time_interval = {(i, j): (t_lower, t_upper) for (i, j) in arcs}  # 区间阻抗示例
demand = {17: 192, 18: 197, ...}  # 受灾点需求（需补全）
Q = 1500  # 车辆载重
T_max = 45  # 最大行驶时间

# Benders分解算法主循环
upper_bound = float('-inf')
lower_bound = float('inf')
iteration = 0
tolerance = 1e-3
max_iter = 20


# 主模型定义
def build_master():
    model = pyo.ConcreteModel()

    # 变量定义
    model.x = pyo.Var(arcs, vehicles, domain=pyo.Binary)
    model.z = pyo.Var(domain=pyo.Reals)

    # 目标函数
    model.obj = pyo.Objective(expr=model.z, sense=pyo.minimize)

    # 初始约束（论文式32-43）
    # 此处需添加所有主模型约束条件
    # 示例：站点约束
    def center_out(model, s):
        return sum(model.x[i, j, k] for i in centers for j in nodes if (i, j) in arcs for k in vehicles) >= 1

    model.center_out_constr = pyo.Constraint(centers, rule=center_out)

    return model


# 子模型定义
def build_sub(x_val):
    model = pyo.ConcreteModel()

    # 变量定义
    model.w = pyo.Var(arcs, vehicles, domain=pyo.Binary)
    model.y = pyo.Var(arcs, vehicles, domain=pyo.NonNegativeReals)
    model.T = pyo.Var(nodes, vehicles, domain=pyo.NonNegativeReals)

    # 目标函数（论文式17）
    obj_expr = sum(time_interval[(i, j)][1] * x_val[(i, j), k] for (i, j) in arcs for k in vehicles) - \
               sum((time_interval[(i, j)][1] * x_val[(i, j), k] + time_interval[(i, j)][0] * (1 - x_val[(i, j), k])) *
                   model.w[(i, j), k]
                   for (i, j) in arcs for k in vehicles)
    model.obj = pyo.Objective(expr=obj_expr, sense=pyo.maximize)

    # 添加子模型约束（论文式18-29）
    # 示例：流量守恒
    def flow_conservation(model, j, k):
        return sum(model.w[(i, j), k] for i in nodes if (i, j) in arcs) == \
            sum(model.w[(j, i), k] for i in nodes if (j, i) in arcs)

    model.flow_constr = pyo.Constraint(nodes, vehicles, rule=flow_conservation)

    return model


# 算法主循环
master_model = build_master()
solver = SolverFactory('cplex')

while iteration < max_iter and (upper_bound - lower_bound) > tolerance:
    # 求解主模型
    results = solver.solve(master_model)
    x_current = {(arc, k): pyo.value(master_model.x[arc, k]) for arc in arcs for k in vehicles}
    z_current = pyo.value(master_model.z)

    # 更新下界
    if z_current > lower_bound:
        lower_bound = z_current

    # 构建并求解子模型
    sub_model = build_sub(x_current)
    results_sub = solver.solve(sub_model)
    C = pyo.value(sub_model.obj)

    # 更新上界
    if C < upper_bound:
        upper_bound = C

    # 添加Benders割约束（论文式31）
    cut_expr = master_model.z >= sum(time_interval[(i, j)][1] * master_model.x[(i, j), k] - \
                                     sum((time_interval[(i, j)][1] * x_current[(i, j), k] +
                                          time_interval[(i, j)][0] * (1 - x_current[(i, j), k])) *
                                         pyo.value(sub_model.w[(i, j), k])
                                         for (i, j) in arcs for k in vehicles)
    master_model.cuts.add(master_model.z >= cut_expr)

    iteration += 1
    print(f"Iteration {iteration}: LB={lower_bound}, UB={upper_bound}")

    # 输出最终结果
    print("Optimal Robust Cost:", upper_bound)
    print("Vehicle Routes:")
# 解析x变量得到路径...