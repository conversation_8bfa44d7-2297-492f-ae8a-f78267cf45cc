长沙理工大学学报(自然科学版)
Journal of Changsha University of Science & Technology(Natural Science)
ISSN 1672-9331,CN 43-1444/N
《长沙理工大学学报(自然科学版)》网络首发论文
题目： 时段区间阻抗下基于限时到达的行程鲁棒优化模型
作者： 周和平，吴三浪
DOI： 10.19951/j.cnki.1672-9331.20250413001
收稿日期： 2025-03-16
网络首发日期： 2025-06-05
引用格式： 周和平，吴三浪．时段区间阻抗下基于限时到达的行程鲁棒优化模型[J/OL]．长
沙理工大学学报(自然科学版).
https://doi.org/10.19951/j.cnki.1672-9331.20250413001
网络首发：在编辑部工作流程中，稿件从录用到出版要经历录用定稿、排版定稿、整期汇编定稿等阶
段。录用定稿指内容已经确定，且通过同行评议、主编终审同意刊用的稿件。排版定稿指录用定稿按照期
刊特定版式（包括网络呈现版式）排版后的稿件，可暂不确定出版年、卷、期和页码。整期汇编定稿指出
版年、卷、期、页码均已确定的印刷或数字出版的整期汇编稿件。录用定稿网络首发稿件内容必须符合《出
版管理条例》和《期刊出版管理规定》的有关规定；学术研究成果具有创新性、科学性和先进性，符合编
辑部对刊文的录用要求，不存在学术不端行为及其他侵权行为；稿件内容应基本符合国家有关书刊编辑、
出版的技术标准，正确使用和统一规范语言文字、符号、数字、外文字母、法定计量单位及地图标注等。
为确保录用定稿网络首发的严肃性，录用定稿一经发布，不得修改论文题目、作者、机构名称和学术内容，
只可基于编辑规范进行少量文字的修改。
出版确认：纸质期刊编辑部通过与《中国学术期刊（光盘版）》电子杂志社有限公司签约，在《中国
学术期刊（网络版）》出版传播平台上创办与纸质期刊内容一致的网络版，以单篇或整期出版形式，在印刷
出版之前刊发论文的录用定稿、排版定稿、整期汇编定稿。因为《中国学术期刊（网络版）》是国家新闻出
版广电总局批准的网络连续型出版物（ISSN 2096-4188，CN 11-6037/Z），所以签约期刊的网络版上网络首
发论文视为正式出版。
第 2X 卷第 X 期 长沙理工大学学报（自然科学版） Vol.2X No.X
202X年X月 Journal of Changsha University of Science & Technology (Natural Science) XXX. 202X
收稿日期：2025-03-16；修回日期: 2025-06-02；接受日期: 2025-06-02
基金项目：国家自然科学基金（51178061）；湖南省科技创新计划项目(2019JJ40311)
通信作者：周和平（1971—）(ORCID：0009-0007-3427-5765)，男，主要从事交通运输规划与管理方面研究。E-mail：
<EMAIL>
投稿网址:http://cslgxbzk.csust.edu.cn/cslgdxxbzk/home
DOI:10.19951/j.cnki.1672-9331.20250413001 文章编号：1672-9331(202X)0X-xxxx-xx
时段区间阻抗下基于限时到达的行程鲁棒优化模型
周和平 1，吴三浪 1
（1.长沙理工大学 交通学院，湖南 长沙 410114）
摘 要：【目的】针对现有区间网络优化方法不考虑时段阻抗的差异，可能导致行程时间区间范围过大而失效的问题，本文
考虑分时段阻抗的区间不确定性，提出了一种基于限时到达场景的鲁棒行程优化模型。【方法】基于鲁棒偏差原理，针对传
统两阶段建模方法的不足，本文提出一种单一鲁棒优化模型构建方法，以降低计算复杂度并提升效率。首先，以最晚出发时
刻为目标，考虑时间传播、时段连接、时段一致性、流量守恒等约束条件，构建了时段固定阻抗下基于限时到达的行程混合
整数规划模型，并推导其对应的对偶模型。其次，将对偶模型与行程优化模型进行整合，建立时段区间阻抗下基于限时到达
的行程最小最大后悔值优化模型。【结果】与固定区间阻抗下的传统优化模型相比，本文提出的模型能显著缩小行程区间范
围，在算例分析中两个路网的行程区间范围及鲁棒成本都降低了近 50%左右，表明模型能够提升优化结果的可靠性。【结论】
本文提出的模型通过引入时段阻抗的不确定性，为限时到达场景提供了更可靠的行程优化方案，兼具理论意义与实际应用价
值。
关键词：时段区间阻抗；行程规划；到达时刻；不确定性；鲁棒优化
中图分类号：U116.5 文献标志码：A
A Robust Optimization Model for Trips Based on Time-limited Arrival under
Time-dependent Interval Impedance
ZHOU Heping1
, WU Sanlang 1
(1.Changsha University of Science & Technology, School of Transportation, Changsha Hunan 410114, China)
Abstract: [Purposes] Existing interval-based network optimization methods ignore time-dependent impedance
variations, potentially resulting in excessively wide travel time intervals and methodological invalidity. This paper
considers the interval uncertainty of time-dependent impedance and proposes a robust travel optimization model
based on time-limited arrival. [Methods] Guided by the robust deviation principle and overcoming the limitations
of conventional two-stage modeling, this paper proposes a single-stage robust optimization framework to reduce
computational complexity and enhance efficiency. First, a mixed-integer programming model for trip planning
under time-dependent fixed impedance is developed based on time-limited arrival, aiming to minimize the latest
departure time. Key constraints include time propagation, time connection, time consistency, and flow conservation.
The corresponding dual model is derived to enable further optimization. Second, the dual model is integrated with
the trip optimization model to establish a minimum-maximum regret value optimization model for trip planning
under time-dependent interval impedance based on time-limited arrival. [Findings] Compared with the traditional
optimization model using fixed interval impedance, the proposed model can significantly reduce the range of travel
intervals. In the case study, both the travel time ranges and robust costs in the two road networks were reduced by
approximately 50%, demonstrating the model's capability to enhance the reliability of optimization results.
[Conclusions] By introducing the uncertainty of time interval impedance, the model proposed in this paper
provides a more reliable trip optimization scheme for time-limited arrival scenarios, which has both theoretical
significance and practical application value.
网络首发时间：2025-06-05 11:25:18 网络首发地址：https://link.cnki.net/urlid/43.1444.n.20250604.1728.003
第 2X 卷第 X 期 周和平，等：时段区间阻抗下基于限时到达的行程鲁棒优化模型
投稿网址:http://cslgxbzk.csust.edu.cn/cslgdxxbzk/home
Key words: Time-dependent interval impedance; Trip planning; Arrival time; Uncertainty; Robust optimization
Manuscript received: 2025-03-16; revised: 2025-06-02; accepted: 2025-06-02
Foundation item: National Natural Science Foundation of China(51178061); Hunan Provincial Science and Technology Innovation
Program(2019JJ40311)
Corresponding author:ZHOU Heping(1971—)(ORCID: 0009-0007-3427-5765), male, professor, research interest: Transportation
Planning and Management. E-mail: <EMAIL>
0 引言
随着数字化与人工智能技术的快速发展，基于
场景需求的个性化行程规划已成为智能交通领域的
研究热点。当前，百度地图、高德地图等导航应用
已实现覆盖旅行、通勤、城市探索等多场景服务，
传统行程规划主要采用确定性或随机阻抗。近年来，
区间分析被引入交通网络研究，通过区间数度量阻
抗的不确定性，成为一种具有潜力的替代方法。该
方法不仅能够表征不确定性，还能部分反映动态特
性，同时保留对最坏情形的评估能力。然而，现有
区间方法存在一个关键局限：未考虑阻抗的时段差
异性，可能导致路径时间区间过大而失去实际指导
意义。因此，在行程规划研究中，亟需发展融合时
段特性的不确定性建模方法。
在行程规划的研究中，大多将阻抗视为固定值，
如 ZANA MAJIDI M 等[1]在固定行驶时间下研究了
不同出发时间下对通勤者出行规划的影响。定值阻
抗下，路径方案求解简单，但道路阻抗受不确定因
素如天气、交通组成等影响因而也具有不确定性，
因此定值阻抗不符合实际交通状况。部分学者注意
到了阻抗的不确定性，如 LIANG Z 等[2]，VAN
OOSTEROM S 等[3]基于不确定性行驶时间建立了
对应的模型解决了车辆通勤中的规划调度问题。随
着不确定性问题成为一个研究热点，相关学者注意
到以随机数、模糊数等方式来表达阻抗的不确定性。
如 AMIRSAHAMI A 等[4]，GARRIDO A 等[5]针对供
应链系统中的决策优化问题，将模糊数与鲁棒优化
技术相结合处理不确定性风险。WAN S 等[6]，
MONDAL A等[7]使用模糊数来优化紧急情况中的路
径规划和行程安排。虽然利用模糊数能够考虑到不
确定性，但其隶属度函数的确定却过于主观，由此
对于路径优化的结果也造成了一定影响。因此，随
机数因为其在考虑不确定性时较低的门槛受到青
睐，如 OSORIO-MORA A 等 [8] ， HOSSEINI
DEHSHIRI S J 等[9]，WU Z 等[10]结合随机数与鲁棒
优化对路径与行程规划的中不确定性问题进行优
化。SHEN Y D 等[11]，TSANG M Y 等[12]，LI S 等[13]，
DING Y 等[14]考虑到物流行业中服务时间与行驶时
间的不确定性，利用随机规划对时间不确定性建模
以解决路径规划问题。CHEN D 等[15]，YEH W-C
[16]
关注到路径与时间的可靠性问题，在旅行商问题中
进行了随机建模与优化。随机数无疑是处理不确定
性的有效工具，但利用随机数处理不确定性必须事
先确定其概率分布。
随着 Ramon E. Moore 于 1966 年提出区间分析
理论以来，其在交通运输领域的应用不断增多，近
年来，采用区间数度量阻抗的不确定性成为一个新
的方向。区间数不需要事先做出任何假设，可以反
应数据的不确定性以及动态变化的范围，并能给出
最坏情形下的方案，受到了研究者的重视，成为在
分布情况未知条件下的一种可行替代方法。如谭倩
等[17]，郭亚然等[18]，陈红等[19]采用区间值衡量路段
阻抗，建立了区间阻抗下的路网模型并使用鲁棒优
化求解鲁棒最短路径，但以上研究均未考虑时段阻
抗的差异。
从以上分析可以看出，考虑阻抗的区间不确定
性成为提升特殊场景的行程规划服务水平具有重要
意义，但需解决以下三个问题：一是如何避免区间
失效的问题；二是如何构建分时段区间阻抗下的时
间依赖交通网络，最大限度减少计算规模；三是如
何构建能够处理分时段区间阻抗的鲁棒优化模型，
并将其转化为单一值优化模型。
鉴于此，针对限时到达这一特殊场景下的行程
规划问题，本文采用分时段区间数度量阻抗的不确
定性，通过减少阻抗的区间宽度避免区间失效，继
而提出一种基于起始节点到达时刻的时间依赖交通
网络构建方法，在此基础上建立一种基于分时段依
赖区间阻抗的鲁棒优化模型。该方法通过整合上界
 长沙理工大学学报（自然科学版） 202X 年 X 月
投稿网址:http://cslgxbzk.csust.edu.cn/cslgdxxbzk/home
与下界阻抗下的路径规划模型，构建了统一的优化
框架，能同时确定最晚可靠出发时刻和最优路径方
案。研究采用最小最大后悔值准则处理行程时间不
确定性，有效平衡了规划方案的时效性与鲁棒性。
1 问题描述与网络构建
1.1 问题描述
建立一个路网图
G V A  ( , )
，其中
V
是路网中
节点的集合，
A
是路网中路段的集合。路段
( ) i, j
随
时 间 段 变 化 的 阻 抗 区 间 为
[ , ] m m L Uij ij
， 且
0
m m   L U ij ij
，给定到达时刻
T
，车辆在起始时刻
0
t
从源节点出发，选择一条车辆通行路径，并使得在
最坏情境下出发时刻最晚。
1.2 基于节点到达时段的时间依赖交通网络构建
考虑路段阻抗的动态变化，需要构建时间依赖
交通网络，通常有两种构建方式，一是时间扩展网
络，二是时间划分网络，前者通过在相应节点按时
段增加边来实现，比如一个 100 条边的网络如果分
为 10 个时段，则该网络的边数将扩展为 1000 个，
计算复杂性增长显著，后者不需要加边，只需将每
个路段的阻抗分时段划分即可。本文借鉴后者，建
立基于节点到达时段的时间依赖交通网络。
(a) 路网区间阻抗 (b) 区间阻抗取值
图 1 时间依赖交通网络
Fig 1 Time-dependent Road Network Diagram
传统的时间划分方法按时段列出各路段的阻
抗，其时段与起始节点所处的时段无关。如果路段
通行时间长，容易出现跨时段的现象，路段的通行
时间需要按某种标准进行分段计算，当考虑出发时
刻时，可能会出现非线性的目标函数。CHABINI I
等[20]提出路段行驶时间是关于其到达节点的时间依
赖函数，本文根据这个思路建立了一种基于起始节
点到达时段的时间依赖交通网络，时段划分不再按
路段，而是按起始节点到达时段进行划分，即将路
段的时段划分与起始节点的到达时段对应，路段的
阻抗按此时段进行划分。起始节点时段确定，则路
段的时段阻抗也随之确定。由于每个路段只能取一
个时段的阻抗，避免了跨时段计算的问题。如图 1
所示，假定时段为两个，分别为[0,4)和[4,8)，各路
段分时段阻抗如图 1(a)所示，如路段(1,2)的两个时
段的阻抗分别为 1 和 2。当
1
t  3
时，节点所在的时
段在[0,4)内，即为第 1 时段，则路段(1,2)的阻抗取
第 1 时段，等于 1 ，到达节点 2 的时间为
4
t    3 1 4
，同理可求出其他路段的阻抗和节点
的到达时刻，如图 1(b)所示。由于节点 3 有两条路
径经过，其节点的到达时间取最小值。1 到 4 的最
短路径为 1-2-4，到达时间为 8。
2 时段固定阻抗下的行程规划模型
2.1 符号定义
集合：
V
：节点集合。
A
：边集合。
M
：时间段集合，每个
m
对应一个时间段。
参数：
T ：目标节点的最晚到达时间。
B：一个很大的常数，处理时间窗口约束。
m Wij ：路段
( , ) i j
在第
m
个时段的阻抗
start
m
t ：第
m
个时间段的起始时间
end
m
t ：第
m
个时间段的结束时间。
决策变量：
0
t ：出发时刻。
j t ：节点
j
的到达时刻。
d
t ：终点
d
的限制到达时刻。
m
ij x ：二进制变量，表示在第
m
个时间段是否
经过路段
( , ) i j 。
第 2X 卷第 X 期 周和平，等：时段区间阻抗下基于限时到达的行程鲁棒优化模型
投稿网址:http://cslgxbzk.csust.edu.cn/cslgdxxbzk/home 1, ( , )
0,
m
ij
m i j
x

 

第 个时段经过路段
其他
(1)
2.2 基于限时到达的行程规划模型
（1）目标函数
到达时刻确定，行程规划的目标即为行程时间
最短：
min Z t t  d 0
(2)
d
t
为确定数。因此该目标函数可转换为出发时
刻最晚，即
max 0
t
(3)
（2）约束条件
① 时间传播约束
, ( , ) m m
j i ij ij
m M
t t W x i j A

     
(4)
s 0
t t 
(5)
式(4)与式(5)表示对于每条路段
( , ) i j
，节点
j
的时
间
j t
与节点
i
的时间
i t
之间满足传播关系。
② 目标节点时间约束
d
t T 
(6)
式(6)表示目标节点
d
的时间不能超过最晚到达时
间。
③ 时段连接约束
, , ,
m n
ij jk
m M i N n M k N
m x n x j A j s d
   
          (7)
式(7)对于每个节点，当前节点所在的时间段一定小
于等于下一个节点所在的时间段。
④ 路段经过次数约束
1, ( , ) m
ij
m M
x i j A

   
(8)
式(8)表示一条路段在所有时间段内最多只会被选择
一次。
⑤ 流量守恒约束
1,
1,
0,
m m
jk ij
k N m M i N m M
j s
x x j d
   
 

    


   
其它
(9)
式(9)表示只会形成一条路径。
⑥ 时段一致性约束
B (1 ), , , ( . )
B (1 ), , , ( . )
start m
i m ij
end m
i m ij
t t x i V m M i j A
t t x i V m M i j A
           
           
 (10)
式(10)表示对于每个起始节点所在的时段，与路段的
时段一致。
⑦ 决策变量范围约束
min 0 max t t t  
(11)
{0,1} m
ij x (12)
式(11)和式(12)表示决策变量的取值范围。
3 时段区间阻抗下基于限时到达的行程规
划模型
3.1 鲁棒成本的计算
鲁棒优化是充分考虑不确定性的一种优化方
法，对于区间阻抗下的不确定性优化有很好的效果。
1997 年，PANOS K[21]提出鲁棒离散优化的三个建模
准则，即绝对鲁棒、鲁棒偏差、相对鲁棒，建议绝
对鲁棒采用 minimax 模型，鲁棒偏差采用最小最大
后悔值模型，相对鲁棒采用最小最大偏离度模型。
绝对鲁棒是一种保守策略，其核心逻辑是预设最坏
情景必然发生，是应对最坏情景（如最拥堵状态）
的对抗策略。鲁棒偏差是针对所有可行方案，计算
每一方案的最坏情景与最好情景，寻求两者偏差最
小的方案。相对鲁棒是针对所有可行方案，计算每
一方案的最坏情景对最好情景的偏离度，寻求偏离
度最小的方案，需要预设偏离度约束条件。
在限时到达场景中，以上三者准则均可使用，
但绝对鲁棒准则针对的是预设最坏情景必然发生的
特殊情况，适用面较窄。而相对鲁棒准则需要预设
偏离度约束条件，普通用户难以理解并提供，故本
文采用鲁棒偏差准则进行建模。
设路网中所有可能的情景集合为
F
，在某一情
景
f F
下任意路径方案
p
都有一个阻抗值
pI，若
*
p
为情景
f
下的最优路径方案，则有路径
p的鲁
棒偏差为
p p *
p
D I I  
(13)
其中，
D
p
是方案
p
的鲁棒偏差，即后悔值，
*
pI是
 长沙理工大学学报（自然科学版） 202X 年 X 月
投稿网址:http://cslgxbzk.csust.edu.cn/cslgdxxbzk/home
情景
f
下最优路径
*
p
的阻抗值，鲁棒成本即决策者
在某一情景
f
下选择方案
p
的最大，即
max{ } C D p p

(14)
式中，
Cp
为方案
p
的鲁棒成本，即最大后悔值。
3.2 时段区间阻抗下的鲁棒成本计算
时段区间阻抗下，各路段的各时段阻抗为区间
值，鲁棒成本的计算就是要找到最大后悔值。
(a) 路网时段区间阻抗 (b) 时段区间阻抗取值
图 2 区间阻抗下时间依赖交通网络
Fig 2 Diagram of Time-dependent Road Network under Interval
Impedance
如图 2(a)所示，最大后悔是在如下情景下发生：
如果已存在一条路径，所经过路段的各时段阻抗均
取上界，未经过的路段各时段阻抗都取下界，由此
可以得到两种方案：一是已存在的路径，按各时段
的上界可计算出该路径的时间成本。如图 2(b)所示，
路径 1-2-4 经过了路段 1-2 和 2-4，各时段的阻抗分
别取上界。二是将阻抗更新后，变成了分时段固定
阻抗下的行程规划问题，在此情景下可用上文所述
的方法求解得到一条新的路径和时间成本。将两个
成本相减，即为鲁棒成本，或者是路径 1-2-4 情景下
的最大后悔值。
3.3 模型构建
3.3.1 建模思路
由于采用鲁棒偏差准则建模，需要分两阶段建
模，才能得到鲁棒成本，即：先构建上界阻抗下的
行程规划模型，得到路径方案后，更新阻抗，在某
个时段经过的路段阻抗取上界值，该路段的其它时
段以及未经过路段的各时段阻抗均取下界值，然后
构建此情景下的行程规划模型，两个模型的目标函
数值的差值即为鲁棒成本。两阶段模型需要反复迭
代，计算复杂性过大。
本文的基本建模思路为：通过整合上界与下界
阻抗下的行程规划模型，构建了统一的优化框架，
采用最小最大后悔值准则处理行程时间不确定性。
令
m
Lij 、 m Uij
分别为路段
( , ) i j
第
m
个时段的阻
抗下界和上界。如果存在一条路径经过路段 ，则阻
抗 取
m Uij
， 如 果 不 经 过 路 段
( , ) i j
，则阻抗取
( ) m m m m L U L x ij ij ij ij   
。计算鲁棒成本只需要此情景
下的目标函数值，而不需要路径方案，一个可行的
方案就是构建此情景下的对偶模型，将其嵌入到路
径优化模型之中。
3.3.2 对偶模型
原问题即为前文所述的固定区间阻抗下的行程
规划模型，只不过路段的分段阻抗为：
( ) m m m m L U L x ij ij ij ij   
。引入对偶变量
d
,  j
,  s
, i
,
 min
, max
, ij
, j
,k
,
j
 , ij
,
m ij
,
m
ij
，对应原问题
中的约束条件。
（1）对偶模型的目标函数：
   0
* start end
0
, , min B( )
m
ij j
m m m m
s s d d ij ij ij m ij m ij
ij A m M t x t
t t t t t       
 
           

(15)
（2）对偶模型的约束条件：
min max 1 0 s
     v v
(16)
0
m m     j i ij ij    
(17)
( ( ) )
B B 0
m m m m
j ij ij ij ij j
m m
k ij j ij ij
L U L x m
n
 
    
    
     
(18)
0 d 
(19)
0, N, , j      j j s d
(20)
0, ( , ) ij     i j A
(21)
, 0, , , ( , ) m m
ij ij          i V m M i j A
(22)
max min v v, 0 
(23)
3.3.3 基于限时到达的行程鲁棒优化模型
给定一条路径，所经过路段的时段阻抗全取上
界，得到最晚出发时刻
0
t
，在此情景下更新所有未
经过路段的时段阻抗，全取下界，运用对偶模型求
第 2X 卷第 X 期 周和平，等：时段区间阻抗下基于限时到达的行程鲁棒优化模型
投稿网址:http://cslgxbzk.csust.edu.cn/cslgdxxbzk/home
得
*
0
t
，最大后悔值为两个情景下的最晚出发时刻的
差值，即：
*
0 0 t t  ，目标函数是最小最大后悔值，
为
* min 0 0 t t  (24)
为了和原模型保持一致，目标函数变为
*
max 0 0 t t  (25)
由于
*
0
t
是对偶模型的目标函数值，为了限定它，还
需要增加一个约束条件，即
*
0
( , )
(B( ) ) m m start m end m
s s d d ij ij ij m ij m ij
i j A m M
t t t t t       
 
               
(26)
根据对原问题以及对偶模型的推导可以得出本文提
出的时段区间阻抗下考到达时刻的行程规划模型。
（1）目标函数：
*
max 0 0 t t  (27)
（2）约束条件：
*
0
( , )
0
(B( ) )
, , ,
1, ( , )
1,
1,
0,
m m start m end m
s s d d ij ij ij m ij m ij
i j A m M
s
m m
j i ij ij
m M
d
m n
ij jk
m M i N n M k N
m
ij
m M
m m
jk ij
k N m M i N m M
t t t t t
t t
t t U x
t T
m x n x j V j s d
x i j A
j
x x
      
 

   

   
             

  

     
  
 
   


 

   

   
min 0 max
min max
B(1 ), , , ( . )
B(1 ), , , ( . )
{0,1}
1 0
0
( ( ) ) B B 0
0
0
start m
i m ij
end m
i m ij
m
ij
s
m m
j i ij ij
m m m m m m
j ij ij ij ij j k ij j ij ij
d
j
s
j d
t t x i V m M i j A
t t x i V m M i j A
t t t
x
v v
L U L x m n

   
      



        
        
 

   
   
          


其它
max min
, , ,
0, ( , )
, 0, , , ( , )
, 0
ij
m m
ij ij
j V j s d
i j A
i V m M i j A
v v

 































   
    
       

 
(28)
该模型为混合整数规划模型，可用 Cplex 求解
器求解。
4 算例分析
考虑数据的可得性以及地区的代表性，选取了
具有地级市典型特征的宣城市、以及具有高密度路
网的洪山区作为验证案例。
原始数据为宣城市交通数据中心提供的 2021
年 7 月的各路段所有车辆的行程时间以及武汉市交
通局提供的洪山区 2022 年 5 月的各路段所有网约车
的行程时间数据。采用时间约束 K-Means 方法进行
时段划分。分时段区间阻抗用历史数据各时段的
5%-95%分位数确定。对其构建简单路网图如图 3 所
示，图 3(a)为宣城市部分区域主干道路网络，共有
12 个节点，17 条有向路段，图 3(b)为武汉市洪山区
区域道路网共有 19 个节点，34 条有向路段。表 1
是测试路网中部分路段对应的时段区间阻抗数据。
以 8:00 为 0 值，节点 1 作为起点，分别以节点 12、
16 为终点，以分钟为间隔。
路网中路段数量较多，这里仅展示部分路段的
时段区间阻抗和固定区间阻抗。
(a) 宣城市道路网 (b) 洪山区道路网
图 3 测试路网
Fig 3 Test Road Network
表 1 区间阻抗数据
Table 1 Interval Impedance Data
路段(1,2)/min … 路段(11,12)/min
8:00-9:00 [11.04,13.37] … [10.99,13.43]
9:00-9:15 [7.70,10.09] … [8.98,10.88]
9:15-9:30 [6.58,9.68] … [5.39,9.30]
9:30-9:45 [4.53,7.47] … [5.37,8.14]
9:45-10:00 [4.06,6.23] … [3.97,6.35]
10:00-11:00 [3.29,4.30] … [3.18,4.34]
8:00-11:00 [4.53,10.69] … [4.36,10.82]
设有车辆从起点出发，分别以出发时刻 8:00，
8:30 为例，应用以上模型，采用 AMPL 数学规划建
模语言建立模型与数据文件，调用 Cplex 求解器进
行求解，以验证模型的有效性。采用分支分割法求
解，Cplex 参数设置为：threads=4，timelimit=100，
mip_strategy_branch=1，mip_cuts_gomory=2。得到
时段区间阻抗下，不同出发时刻出行的最优路径如
图 4 所示。
 长沙理工大学学报（自然科学版） 202X 年 X 月
投稿网址:http://cslgxbzk.csust.edu.cn/cslgdxxbzk/home
(a) 宣城市道路网 t0=8:00 (b) 宣城市道路网 t0=8:30
(c) 洪山区道路网 t0=8:00 (d) 洪山区道路网 t0=8:30
图 4 时段区间阻抗下指定出发时刻的最短路
Fig4 Shortest Path with Specified Departure Time under
Time-Dependent Impedance
根据图 4，在两个不同的道路网络中，时段区
间阻抗下不同出发时刻的最短路均不相同。时段区
间阻抗下的行程方案，考虑了到站时刻不确定性的
影响，采用鲁棒优化方法计算得到，因而是一种相
对保守的出行方案。
在前文基础上，对时段区间阻抗下指定到站时
刻要求下的路径选择进行分析计算。分别指定到站
时刻为 10:40，11:00。其最优路径如图 5 所示，
(a) 宣城市道路网 td=10:40 (b) 宣城市道路网 td=11:00
(c) 洪山区道路网 td=10:40 (d) 洪山区道路网 td=11:00
图 5 时段区间阻抗下指定到达时刻的最短路
Fig5 Shortest Path with Specified Arrival Time under
Time-Dependent Impedance
可见，不同到站时刻要求下的路径方案不相同。
其中，宣城市道路网络中两条路径的出发时刻分别
是 9:36，10:05，洪山区道路网络中两条路径的出发
时刻分别为 9:14，9:48，如表 2。虽然两种行程方案
都会经过一些相同的路段，但由于其经过的时段存
在差别，因此即使是相同路段也存在阻抗不等的情
况。
根据前文所述，计算出固定区间阻抗下的最短
路，并将其与给定到站时刻为 11:00 时的时段区间
阻抗下的最短路进行对比如图 6 与图 7。
(a) 宣城市道路网 (b) 洪山区道路网
图 6 固定区间阻抗与时段区间阻抗最短路对比
Fig 6 Comparison of Shortest Path Under Fixed interval
Impedance and Time-dependent Interval Impedance
表 2 计算结果对比表
Table 2 Results Comparison Table
地区 指定到达时刻 出发时刻 路径阻抗/min 鲁棒成本/min
宣城市
10:40 9:36 64.03 19.33
11:00 10:05 55.86 11.38
洪山区
10:40 9:14 86.15 26.57
11:00 9:48 72.84 17.62
根据图 6 可知，固定区间阻抗与时段区间阻抗
下的最短路径存在较大差别。此外，当指定了到站
时刻，时段区间阻抗会根据不同的到站时刻选择出
发时刻及其对应最短路，而固定区间阻抗的最短路
一直相同，不发生任何改变。
32.29
63.91
20.66
44.67
55.86
11.38 下界阻抗 上界阻抗 鲁棒成本
0
10
20
30
40
50
60
70 阻抗值/min
 时段固定阻抗
 时段区间阻抗
(a) 宣城市道路网
46.18
85.29
33.28
51.33
72.84
17.62 下界阻抗 上界阻抗 鲁棒成本0
20
40
60
80
100 阻抗值/min
 固定区间阻抗 时段区间阻抗(b) 洪山区道路网
图 7 时段与固定区间阻抗路径成本对比
Fig 7 Cost Comparison of Time-dependent Interval Impedance
and fixed time Interval Impedance
根据图 7 可知，时段区间阻抗下最优路径阻抗
区间长度分别是 11.19 和 21.51 分钟，而固定区间阻
抗下最优路径阻抗区间长度 31.72 和 39.09 分钟，显
然时段区间阻抗下的路径区间更窄，且时段区间阻
抗下的鲁棒成本都要明显低于固定区间阻抗下的鲁
棒成本。这表明，时段区间阻抗下的最优路径有更
高的稳定性。
第 2X 卷第 X 期 周和平，等：时段区间阻抗下基于限时到达的行程鲁棒优化模型
投稿网址:http://cslgxbzk.csust.edu.cn/cslgdxxbzk/home
5 讨论
传统区间阻抗行程规划中，采用固定区间阻抗，
未能反映实际路况的时段特征差异，忽略了时段差
异引起的阻抗变化，由此会造成以下两个问题：一
是产生的行程时间区间范围过大而失去实际指导价
值；二是无法处理限时到达这种特殊场景需求，因
为任何时段出发，路段阻抗都是固定的区间值，路
径方案完全一样，无法灵活响应到达时间限制需求。
本文的模型引入时段感知的区间阻抗表征方法，可
有效解决以上问题。相比不分时段的固定区间阻抗
下的行程规划方法，各时段的区间范围更窄，如表
1 所示，各时段的阻抗区间长度相比固定阻抗区间
明显较小，计算得到的行程时间区间也会更窄（如
图 7 所示），宣城市道路网在时段区间阻抗下的行
程方案区间长度为 11.19 分钟，相比固定阻抗区间
下的 31.72 分钟下降了 64.7%，洪山区道路网在时段
区间阻抗下的行程方案区间长度为 21.51 分钟，相
比固定阻抗区间下的 39.09 分钟下降了 45%。同时，
本文提出的模型不仅可计算得到路径方案，还能响
应限时到达场景需求，能得到更具个性化的灵活方
案。从算例可以看出，根据用户限时达需求，路径
方案避开了时段阻抗较大的路段（如图 5 所示），
并得到了最晚出发时刻。
另外，传统区间阻抗行程规划中使用两阶段建
模，需要分别构建上下界优化模型进行交互迭代求
解，不仅计算效率低，且难以保证两阶段解的统一
性。本文构建统一的鲁棒优化框架，将上下界优化
模型通过对偶理论进行整合，保证了解的一致性与
可靠性。
本研究在以下两个方面还存在局限性：一是仅
考虑了限时到达这个单一场景，没有考虑乘客的路
径偏好等其它特定需求；二是仅考虑了时间成本，
没有考虑费用成本，可在后续研究中加以考虑。
6 结论
本文结合现有阻抗不确定性的研究，引入时段
区间阻抗概念，对限时到达场景下的行程规划问题
进行了研究，得到以下结论：
（1）时段区间阻抗能够更好地考虑了时段阻抗
的不确定性，同时缩小了行程时间的区间范围，可
得到更可靠的行程方案。
（2）在本文提出的模型基础上，可考虑费用以
及其它特定要求，建立更加符合实际需要的模型。
（3）在本研究基础上，可考虑乘客的其它特定
需求，制定更个性化的行程方案。
（4）本研究可拓展应用于多模式出行规划，为
实际交通规划与管理提供理论指导。