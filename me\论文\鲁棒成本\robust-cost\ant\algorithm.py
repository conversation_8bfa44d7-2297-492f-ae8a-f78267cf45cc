# 蚁群算法
import numpy as np
import heapq
from data import edges, l, u, start_node, end_node


# 蚁群算法参数
num_ants = 50
num_iterations = 100
alpha = 1.0
beta = 2.0
rho = 0.1
Q = 100

# 初始化信息素矩阵
pheromones = np.ones(len(edges))  # 初始信息素

# 构造路径
def construct_solution(pheromones, nodes, edges, l, u):
    current_node = start_node
    path = [current_node]

    while current_node != end_node:
        neighbors = get_neighbors(current_node, edges)
        if len(neighbors) == 0:
            break

        # 计算每个邻居节点的选择概率
        probabilities = []
        for neighbor in neighbors:
            edge_index = find_edge(current_node, neighbor, edges)
            if edge_index is not None:
                tau = pheromones[edge_index] ** alpha
                eta = (1 / u[edge_index]) ** beta  # 启发函数
                probabilities.append(tau * eta)
            else:
                probabilities.append(0)

        # 归一化概率
        probabilities = np.array(probabilities)
        probabilities /= np.sum(probabilities)

        # 按概率选择下一个节点
        next_node = np.random.choice(neighbors, p=probabilities)
        path.append(next_node)
        current_node = next_node

    return path

# 更新信息素
def update_pheromones(paths, costs):
    global pheromones
    pheromones *= (1 - rho)  # 挥发信息素

    for path, cost in zip(paths, costs):
        for i in range(len(path) - 1):
            edge_index = find_edge(path[i], path[i + 1], edges)
            if edge_index is not None:
                pheromones[edge_index] += Q / cost  # 更新信息素

# 计算鲁棒成本（复用之前的函数）
def compute_robust_cost(path, edges, l, u):
    max_cost = 0
    for i in range(len(path) - 1):
        edge_index = find_edge(path[i], path[i + 1], edges)
        if edge_index is not None:
            max_cost += u[edge_index]

    visited_edges = set(find_edge(path[i], path[i + 1], edges) for i in range(len(path) - 1))
    unused_edges = [i for i in range(len(edges)) if i not in visited_edges]

    min_cost = dijkstra_min_cost(unused_edges, edges, l)
    return max_cost - min_cost
def dijkstra_min_cost(unused_edges, edges, l, start_node=1, end_node=26):
    # 创建图的邻接列表
    graph = {}
    for edge in unused_edges:
        node1, node2 = edges[edge]
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        graph[node1].append((node2, l[edge]))  # (相邻节点, 下界阻抗)
        graph[node2].append((node1, l[edge]))  # 双向

    # Dijkstra 算法初始化
    min_heap = [(0, start_node)]  # (成本, 当前节点)
    min_cost = {node: float('inf') for node in graph}
    min_cost[start_node] = 0

    while min_heap:
        current_cost, current_node = heapq.heappop(min_heap)

        # 如果到达终点，返回当前成本
        if current_node == end_node:
            return current_cost

        # 遍历相邻节点
        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor))

    # 如果无法到达终点，返回无限大
    return float('inf')

# 获取与指定节点相邻的所有节点
def get_neighbors(node, edges):
    return np.unique(edges[edges[:, 0] == node][:, 1])

# 查找图中连接两个节点的边的索引
def find_edge(node1, node2, edges):
    index = np.where((edges[:, 0] == node1) & (edges[:, 1] == node2))
    return index[0][0] if index[0].size > 0 else None

def find_shortest_path(edges, l, u, start_node, end_node, cost_type='lower'):
    graph = {}
    for i, (node1, node2) in enumerate(edges):
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        if cost_type == 'lower':
            graph[node1].append((node2, l[i]))
            graph[node2].append((node1, l[i]))
        else:
            graph[node1].append((node2, u[i]))
            graph[node2].append((node1, u[i]))

    # Dijkstra 算法初始化
    min_heap = [(0, start_node, [])]  # (成本, 当前节点, 路径)
    min_cost = {node: float('inf') for node in graph}
    min_cost[start_node] = 0

    while min_heap:
        current_cost, current_node, path = heapq.heappop(min_heap)
        path = path + [current_node]

        # 如果到达终点，返回当前路径
        if current_node == end_node:
            return path

        for neighbor, cost in graph.get(current_node, []):
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor, path))

    return []  # 如果无法到达终点，返回空路径