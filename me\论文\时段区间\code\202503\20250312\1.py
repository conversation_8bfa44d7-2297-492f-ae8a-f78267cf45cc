import heapq  # 用于实现优先队列（堆结构）
from collections import defaultdict, deque  # defaultdict用于图结构存储，deque用于高效路径操作
import multiprocessing  # 用于并行计算加速
import time  # 用于记录运行时间


# 定义路网类
class RoadNetwork:
    def __init__(self, network_data):
        self.graph = defaultdict(list)  # 使用defaultdict存储图结构，默认值为列表
        self._initialize_graph(network_data)  # 初始化图数据
        self.robust_cost_cache = {}  # 缓存鲁棒成本计算结果，避免重复计算

    def _initialize_graph(self, network_data):
        # 从输入数据初始化图结构
        for source, neighbors in network_data.items():  # 遍历每个起始节点
            for target, time_ranges in neighbors.items():  # 遍历每个邻居节点
                # 将时间区间和阻抗范围存储为元组列表
                self.graph[source].append((target, time_ranges))

    def get_impedance(self, impedance_ranges, time):
        # 根据当前时间选择对应的阻抗值
        for start, end, low, high in impedance_ranges:  # 遍历所有时间区间
            if start <= time < end:  # 匹配当前时间所在区间
                return low, high  # 返回该区间的阻抗范围（下界和上界）
        return None  # 未找到匹配区间返回None

    def dijkstra(self, start, end, time, lower_bound=True):
        # Dijkstra算法计算最短路径，支持时间依赖的阻抗
        priority_queue = [(0, start, time)]  # 优先队列初始化：(当前总时间, 当前节点, 当前时间)
        dist = {start: 0}  # 记录最短时间
        prev = {start: None}  # 记录前驱节点用于路径回溯

        while priority_queue:
            current_dist, current_node, current_time = heapq.heappop(priority_queue)

            if current_node == end:  # 到达终点，生成路径并返回
                path = []
                while current_node is not None:
                    path.append(current_node)
                    current_node = prev[current_node]
                path.reverse()  # 反转路径得到正确顺序
                return path, current_dist

            if current_dist > dist.get(current_node, float('inf')):
                continue  # 当前路径非最优，跳过

            for neighbor, impedance_ranges in self.graph[current_node]:
                # 获取当前时间对应的阻抗
                impedance = self.get_impedance(impedance_ranges, current_time)
                if impedance is None:
                    continue

                # 根据参数选择使用下界或上界阻抗
                cost = impedance[0] if lower_bound else impedance[1]

                new_dist = current_dist + cost  # 计算新的总时间
                new_time = current_time + cost  # 更新到达时间
                if new_time >= 1440:  # 处理时间循环（1440分钟=24小时）
                    new_time %= 1440

                # 更新最优路径
                if new_dist < dist.get(neighbor, float('inf')):
                    dist[neighbor] = new_dist
                    prev[neighbor] = current_node
                    heapq.heappush(priority_queue, (new_dist, neighbor, new_time))

        return None, float('inf')  # 未找到路径返回空

    def calculate_robust_cost(self, path, start_time):
        # 计算路径的鲁棒成本（最坏时间 - 备选路径最佳时间）
        key = (tuple(path), start_time)  # 生成缓存键
        if key in self.robust_cost_cache:  # 检查缓存
            return self.robust_cost_cache[key]

        # 计算原路径的最坏时间
        a = 0
        current_time = start_time
        for i in range(len(path) - 1):
            u = path[i]
            v = path[i + 1]
            # 查找当前边的阻抗范围
            for neighbor, impedance_ranges in self.graph[u]:
                if neighbor == v:
                    # 使用上界阻抗计算最坏时间
                    impedance = self.get_impedance(impedance_ranges, current_time)
                    if impedance is None:
                        continue
                    a += impedance[1]
                    current_time += impedance[1]
                    if current_time >= 1440:
                        current_time %= 1440
                    break

        # 构建排除当前路径边的剩余图
        remaining_graph = defaultdict(list)
        for u in self.graph:
            for v, impedance_ranges in self.graph[u]:
                # 排除当前路径中的边
                if (u, v) not in zip(path[:-1], path[1:]):
                    remaining_graph[u].append((v, impedance_ranges))

        # 在剩余图中计算备选路径的最佳时间
        remaining_network = RoadNetwork({})
        remaining_network.graph = remaining_graph
        alternative_path, b = remaining_network.dijkstra(path[0], path[-1], start_time, lower_bound=True)

        robust_cost = a - b  # 鲁棒成本 = 最坏时间 - 备选最佳时间
        result = (robust_cost, a, b, alternative_path)
        self.robust_cost_cache[key] = result  # 缓存结果
        return result

    def find_min_robust_path(self, start, end, start_time):
        # 寻找最小鲁棒成本的路径
        all_paths = []  # 存储所有完整路径
        priority_queue = [(0, deque([start]), start_time)]  # 使用双端队列优化路径操作
        min_robust_cost = float('inf')  # 初始化最小鲁棒成本

        while priority_queue:
            current_dist, current_path, current_time = heapq.heappop(priority_queue)
            current_node = current_path[-1]

            # 剪枝策略：跳过不可能成为最优解的路径
            if current_dist >= min_robust_cost:
                continue

            if current_node == end:  # 保存完整路径
                path_list = list(current_path)
                all_paths.append(path_list)
                continue

            # 扩展当前路径
            for neighbor, impedance_ranges in self.graph[current_node]:
                if neighbor not in current_path:  # 避免重复节点
                    impedance = self.get_impedance(impedance_ranges, current_time)
                    if impedance is None:
                        continue

                    new_dist = current_dist + impedance[0]  # 使用下界阻抗计算路径长度
                    new_time = current_time + impedance[0]
                    if new_time >= 1440:
                        new_time %= 1440

                    new_path = current_path.copy()  # 复制当前路径
                    new_path.append(neighbor)  # 添加新节点
                    heapq.heappush(priority_queue, (new_dist, new_path, new_time))

        min_robust_path = None
        worst_case_time = 0
        best_case_time = 0
        alternative_path = None

        # 并行计算所有路径的鲁棒成本
        pool = multiprocessing.Pool()
        results = pool.map(calculate_robust_cost_wrapper, [(self, path, start_time) for path in all_paths])
        pool.close()
        pool.join()

        # 寻找最优解
        for path, (robust_cost, a, b, alt_path) in zip(all_paths, results):
            if robust_cost < min_robust_cost:
                min_robust_cost = robust_cost
                min_robust_path = path
                worst_case_time = a
                best_case_time = b
                alternative_path = alt_path

        return min_robust_path, min_robust_cost, worst_case_time, best_case_time, alternative_path

def calculate_robust_cost_wrapper(args):
    # 并行计算的包装函数
    network, path, start_time = args
    return network.calculate_robust_cost(path, start_time)

# 示例使用
network_data = {
  "1": {
    "2": [[0, 480, 2, 10], [480, 600, 5, 15], [600, 1440, 3, 20]],
    "3": [[0, 480, 4, 12], [480, 600, 6, 16], [600, 1440, 5, 18]],
    "4": [[0, 480, 3, 11], [480, 600, 7, 17], [600, 1440, 4, 19]],
    "5": [[0, 480, 5, 13], [480, 600, 8, 18], [600, 1440, 6, 20]]
  },
  "2": {
    "1": [[0, 480, 2, 10], [480, 600, 5, 15], [600, 1440, 3, 20]],
    "3": [[0, 480, 3, 9], [480, 600, 7, 14], [600, 1440, 4, 12]],
    "6": [[0, 480, 6, 14], [480, 600, 9, 17], [600, 1440, 7, 19]],
    "7": [[0, 480, 4, 11], [480, 600, 6, 15], [600, 1440, 5, 13]]
  },
  "3": {
    "1": [[0, 480, 4, 12], [480, 600, 6, 16], [600, 1440, 5, 18]],
    "2": [[0, 480, 3, 9], [480, 600, 7, 14], [600, 1440, 4, 12]],
    "4": [[0, 480, 5, 13], [480, 600, 8, 16], [600, 1440, 6, 17]],
    "7": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "8": [[0, 480, 4, 11], [480, 600, 6, 14], [600, 1440, 5, 13]]
  },
  "4": {
    "1": [[0, 480, 3, 11], [480, 600, 7, 17], [600, 1440, 4, 19]],
    "3": [[0, 480, 5, 13], [480, 600, 8, 16], [600, 1440, 6, 17]],
    "5": [[0, 480, 6, 14], [480, 600, 9, 18], [600, 1440, 7, 20]],
    "8": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "9": [[0, 480, 5, 12], [480, 600, 7, 15], [600, 1440, 6, 14]]
  },
  "5": {
    "1": [[0, 480, 5, 13], [480, 600, 8, 18], [600, 1440, 6, 20]],
    "4": [[0, 480, 6, 14], [480, 600, 9, 18], [600, 1440, 7, 20]],
    "9": [[0, 480, 7, 15], [480, 600, 10, 19], [600, 1440, 8, 21]],
    "10": [[0, 480, 6, 13], [480, 600, 8, 16], [600, 1440, 7, 17]]
  },
  "6": {
    "2": [[0, 480, 6, 14], [480, 600, 9, 17], [600, 1440, 7, 19]],
    "7": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "11": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "7": {
    "2": [[0, 480, 4, 11], [480, 600, 6, 15], [600, 1440, 5, 13]],
    "3": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "6": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "8": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "11": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "12": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "8": {
    "3": [[0, 480, 4, 11], [480, 600, 6, 14], [600, 1440, 5, 13]],
    "4": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "7": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "9": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "12": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "13": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "9": {
    "4": [[0, 480, 5, 12], [480, 600, 7, 15], [600, 1440, 6, 14]],
    "5": [[0, 480, 7, 15], [480, 600, 10, 19], [600, 1440, 8, 21]],
    "8": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "10": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "13": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "14": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "10": {
    "5": [[0, 480, 6, 13], [480, 600, 8, 16], [600, 1440, 7, 17]],
    "9": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "14": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "15": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "11": {
    "6": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "7": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "12": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "16": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "17": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "12": {
    "7": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "8": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "11": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "13": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "17": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "18": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "13": {
    "8": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "9": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "12": [[0, 480, 10, 18], [480, 600, 13, 21], [600, 1440, 11, 23]],
    "14": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "18": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "19": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "14": {
    "9": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "10": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "13": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "15": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "19": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "15": {
    "10": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "14": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "19": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "16": {
    "11": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "17": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "20": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]]
  },
  "17": {
    "11": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "12": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "16": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "18": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "20": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]]
  },
  "18": {
    "12": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "13": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "17": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]],
    "19": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "20": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]]
  },
  "19": {
    "13": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "14": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "15": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "18": [[0, 480, 8, 16], [480, 600, 11, 19], [600, 1440, 9, 21]],
    "20": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]]
  },
  "20": {
    "16": [[0, 480, 5, 12], [480, 600, 8, 15], [600, 1440, 6, 14]],
    "17": [[0, 480, 6, 13], [480, 600, 9, 16], [600, 1440, 7, 15]],
    "18": [[0, 480, 7, 15], [480, 600, 10, 18], [600, 1440, 8, 20]],
    "19": [[0, 480, 9, 17], [480, 600, 12, 20], [600, 1440, 10, 22]]
  }
}

if __name__ == '__main__':
    start_time_total = time.time()
    network = RoadNetwork(network_data)
    start = '1'
    end = '20'
    start_time = 470

    min_robust_path, min_robust_cost, worst_case_time, best_case_time, alternative_path = network.find_min_robust_path(
        start, end, start_time)

    print("最小鲁棒成本的路径:", min_robust_path)
    print("最小鲁棒成本:", min_robust_cost)
    print("原路径最坏时间:", worst_case_time)
    print("备选路径最佳时间:", best_case_time)
    print("备选路径:", alternative_path)