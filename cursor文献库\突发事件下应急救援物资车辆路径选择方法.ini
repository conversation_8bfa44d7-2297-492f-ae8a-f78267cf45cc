突发事件下应急救援物资车辆路径选择方法
陈红1，2
，文若兰1，3
，周和平1
（1.长沙理工大学 交通运输工程学院，湖南 长沙 410114；
2.衡阳技师学院，湖南 衡阳 421101；
3.湖南高速铁路职业技术学院，湖南 衡阳 421002）
摘　要：突发事件救援的紧迫性凸显了及时、合理地向灾区分发应急物资的重要性，应急物资配送中心车辆的调度
方案成为应急物流的关键。该研究考虑在交通路网行程时间不确定的情况下，以车辆调度方案的最大后悔值（鲁
棒成本）最小化为目标，建立应急救援车辆调度优化模型，并设计 Benders分解算法来求解该模型。该算法先将模
型分解为主模型和子模型，再加入Benders最优割约束，不断迭代，直到得到最优解。研究结果表明：该模型能以最
小的鲁棒成本实现应急物资配送中心车辆的合理调度，满足车辆最大救援时限、受灾点需求优先级等要求。与其
他模型相比，该模型得到的车辆行程安排更合理。该研究可为应急物流决策方案提供参考。
关键词：应急救援物资配送；车辆路径优化模型；Benders分解算法；区间阻抗
中图分类号：X4;F252 　　　文献标志码：A
Vehicle path selection method for emergency relief materials in
emergencies
CHEN Hong1，2
，WEN Ruolan1，3
，ZHOU Heping1
（1.School of Traffic and Transportation Engineering， Changsha University of Science & Technology， Changsha 410114， China；
2.Hengyang Technician College，Hengyang 421101，China；
3. Hunan Technical College of Railway High-Speed， Hengyang 421002， China）
Abstract： In the event of an emergency， due to the urgency of rescue， it was very important to
distribute emergency supplies to the disaster area in a timely and reasonable manner. Therefore， the
decision-making of the vehicle dispatching scheme of multiple emergency supplies distribution centers
became the key link of emergency logistics. Considering the uncertain travel time of the traffic
network， to minimize the maximum regret value （robust cost） of the vehicle scheduling scheme， an
optimization model for emergency rescue vehicle scheduling was established. Benders decomposition
algorithm was designed to solve the model. First， the model was decomposed to obtain the main model
and sub-model， and then the Benders optimal cut constraint was added to the solution for continuous
iteration. The result of the example shows that the research model can achieve the reasonable
scheduling of vehicles in multiple emergency material distribution centers with the minimum robust
cost， meet the requirements of the maximum rescue time limit of vehicles， the priority of the demand
of the disaster site. The vehicle travel time obtained from this model is more reasonable. This study can
provide a reference for emergency logistics decision-making plans.
Key words： distribution of emergency relief materials； vehicle path optimization model； benders
decomposition algorithm； interval impedance
收稿日期：2022-03-11
基金项目：国家自然科学基金资助项目（51178061）；湖南省自然科学基金资助项目（2019JJ40311）
作者简介：陈红（1998—），女，长沙理工大学硕士生。

第4期 陈红，等：突发事件下应急救援物资车辆路径选择方法
近年来，应急救援系统在许多突发事件中发挥
了不可替代的作用。在突发事件中，面对紧迫的受
灾点物资需求和不确定的路网行程时间，如何科学
地调度应急救援物资、选择车辆运输路线是应急救
援的关键。许多学者深入研究了应急救援物资车
辆调度问题。ALINAGHIAN 等［1］考虑动态变化的
受灾点需求、位置等要素，构建了应急救援物资车
辆的路径选择模型。MARINAKIS 等［2］结合了 3 种
自适应策略改进粒子群优化算法，得到优选的配
送路径。盛虎宜等［3］综合考虑灾后路网的损毁程
度、随机行驶时间等因素，设计了两阶段启发式算
法来求解以物资需求点损失最大化和配送时间最
小化为目标的应急物资分配-路径选择问题。赵
星等［4］
考虑受灾点紧急级别、受灾后行程时间变化
等因素，以交通量可靠性和路阻函数的行程时间
为目标函数来建模，并采用禁忌搜索和非支配排
序算法对模型进行了求解。宋英华等［5］结合受
灾点人群的年龄分布、伤情严重状况等信息来划
分受灾点的受灾严重级别，考虑同一级别的受灾点
物资满意度，建立了物资调度模型，并采用经典遗
传算法对其进行求解。韩孟宜等［6］设计了结合
节约算法、大规模邻域搜索的遗传算法，该算法的
速度和结果均优于传统遗传算法的。卢建锋等［7］
以运输时间与总环境风险、缺货损失费用最小化
为目标函数，构建了多目标的多种类物资调度模
型，并设计了 NSGAⅡ算法对其进行求解。在求
解过程中，先采用锦标赛法进行个体选择，再结合
多点交叉算子，加快算法搜索速度，最后采用逼近
理想解排序法得到最优解。张文晖［8］
考虑多种类
应急物资需求，采用拉格朗日松弛算法分解问题，
提高了解的收敛速度。石崇玉［9］
考虑了不确定的受
灾点物资需求，构建多目标车辆调度鲁棒优化模
型，确保车辆及时地将应急食品物资配送到各受
灾点。许德刚等［10］以行驶时间最小化、需求点的
满意度最大化为目标，建立应急调度模型，并提出
优化烟花算法对其进行求解，通过改变变异策略、
引入禁忌表，提升了算法寻找局部最优解的能力。
这些研究主要集中在应急救援车辆路径问题
的模型构建以及求解算法优化两方面。对于应急
救援车辆的路径问题，很多学者考虑了受灾点物
资需求的不确定性或优先级，对其进行研究［11］
，还
有部分学者考虑到了路段行程时间的不确定性，
对其进行研究［12-13］。目前，同时考虑以上两个条
件的研究鲜见。因此，本研究基于交通路网区间
阻抗，在受灾点需求优先级约束、车辆行驶时间约
束等约束条件下，以鲁棒成本最小化为目标，构建
应急救援车辆物资配送路径优化模型，并设计
Benders 分解算法来求解该模型，以便快速、合理地
进行多应急物资集散中心的救援与协同调度，以
期为应急管理部门的决策制定提供借鉴。
1　问题描述与符号说明
车辆在行驶中会受到交通拥堵、恶劣天气、交
通管制等不确定性因素的影响，车辆的行程时间也
不会是一个固定值。因此，本研究采用区间来度量
交通路网行程时间，该区间内最短与最长行程时间
分别对应在道路交通的最好与最差状态下，车辆在
该路段上所花费的时间。车辆在该路段上的行程
时间可以是处于最长、最短行程时间之间的任意
值。本研究以这种方式来表征在实际交通场景中
车辆行驶时间的不确定性。
假设在突发事件发生时，交通网络图为 G =
( N，D)，其 中 ，D 为路段集，(i，j) ∈ D，路段 (i，j) 的
行驶时间处于区间阻抗[ t
l
ij
，t
u
ij ]中，t
l
ij
、t
u
ij 分别是车辆
在路段(i，j) 最好情况与最差情况下的行程时间，且
t
l
ij < t
u
ij
，t
l
ij ∈ Tl
，t
u
ij ∈ Tu；N 为 节 点 集 ，任 意 节 点
i ∈ N；S为应急物资集散中心点集，∀s ∈ S；M为受
灾点集，∀m ∈ M；K为应急救援车辆集，∀k ∈ K；E
为配送路径方案集，∀e ∈ E；pi为受灾点i的物资需
求量；Tmax为车辆的最长行驶时间。
本研究讨论多个应急物资集散中心车辆的协
同调度优化问题，依据灾区实际情境，科学地评估
各受灾点的物资需求量，并优先考虑配送需求量
大的受灾点。应急救援车辆 k 从应急物资集散中
心 s 出发，依次给受灾点配送物资，车辆的配送路
径为 e。在突发事件发生时，需要快速地制定可靠
的应急资源调度方案来满足所有受灾点 i 的物资
需求。
115
交 通 科 学 与 工 程 第39卷
2　模型建立
考虑交通路网行程时间的不确定性，建立区间
阻抗下应急物资配送路径优化模型，以辅助应急管
理部门进行决策，得到最优车辆调度方案。求解此
模型的思路为：先寻找出所有符合约束条件的车辆
调度方案；再基于该调度方案的情景找到最优调度
方案；最后，获得最优调度方案的最大后悔值（鲁棒
成本）。该模型旨在找到所有车辆调度方案的最小
最大后悔值（最小鲁棒成本），从而最小化决策者的
后悔值。
2.1　目标函数与决策变量
以车辆调度方案鲁棒成本最小为目标函数：
min∑i ∈ N
∑
j ∈ N
∑k ∈ K
t
u
ij xk
ij - Xr
 （1）
Xr = min∑i ∈ N
∑
j ∈ N
∑k ∈ K
(t
u
ij xk
ij + t
l
ij (1 - xk
ij ))wk
ij （2）
式中：t
u
ij xk
ij为最差情况下车辆在路段的行程时间，即
车辆k在路段(i，j) 的行程时间取路段(i，j) 区间阻抗
上界值 t
u
ij
。t
u
ij xk
ij + t
l
ij (1 - xk
ij
）表示特定情景下路段
(i，j) 的阻抗，当xk
ij = 1时，式（2）将取到路段(i，j) 阻
抗的区间上界值 t
u
ij
；当 xk
ij = 0时，式（2）将取到路段
(i，j) 阻抗的区间下界值t
l
ij
。wk
ij为特定情景下的车辆
路径方案。
式（1）中，
i
∑∈ N j
∑∈ N k
∑∈ K
t
u
ij xk
ij 为行程最差情况下车
辆调度方案的总行程时间，Xr为基于车辆调度方案
的特定路网阻抗情景下的最佳车辆调度方案的总
行程时间，而在行程最差情况下，车辆调度方案总
行程时间与其情景下最佳车辆调度方案的总行程
时间的差值就是此车辆调度方案的最大后悔值。
因此，式（1）旨在找到所有满足约束条件的车辆调
度方案最大后悔值的最小值。
该模型决策变量为：
xk
ij = ■
■
■
1，车辆k从节点i驶向节点j
0，其他情形 （3）
wk
ij = ■
■
■
1，特定情景下车辆k从节点i驶向节点j
0，其他情形 （4）
其中，wk
ij为变量 xk
ij基于特定情景下的最佳车辆
调度方案；连续变量 yk
ij 为路段 (i，j) 上车辆 k 的物资
载重量；T k
i 为车辆k行驶到节点i的时刻。
2.2　约束条件
2.2.1　站点约束
■
■
■
|
|
|
|
|
|
|
|
||
|
|
∑
j ∈ M
∑k ∈ K
xk
ij ≥ 1，∀i ∈ S （5）
∑
j ∈ M
∑k ∈ K
xk
ji ≥ 1，∀i ∈ S （6）
∑i ∈ N
∑k ∈ K
xk
ij = 1，∀j ∈ M （7）
∑i ∈ N
xk
ij -∑i ∈ N
xk
ji = 0，∀j ∈ N，k ∈ K （8）
每个应急物资集散中心 i都能发出与接收多辆
车，式（5）表示每个集散中心 i最少发出一辆车到受
灾点；式（6）表示每个集散中心 i最少有一辆车从受
灾点返回来；式（7）表示任意受灾点 j只由一辆车从
一个集散中心出发，为其配送物资；式（8）表示任意
车辆k在任意节点j进出流量守恒。
2.2.2　车辆约束
■
■
■
||
|
|
∑
j ∈ N
xk
ji =∑
j ∈ N
xk
ij ≤ 1，∀k ∈ K，i ∈ S （9）
∑i ∈ N
xk
ij =∑i ∈ N
xk
ji ≤ 1，∀k ∈ K，j ∈ M （10）
式（9）表示车辆 k 的行程只能以同一个应急物
资集散中心i作为起终点；式（10）表示任意车辆k在
任意受灾点j至多服务一次。
2.2.3　边约束
∑k ∈ K
xk
ij ≤ 1，∀i ∈ N，j ∈ N （11）
式（11）表示从受灾点 i 驶向受灾点 j 的车辆最
多有一辆，保证所有受灾点只被一辆车服务。
2.2.4　车辆容量限制约束
■
■
■
|
|
y k
ij ≤ Q × xk
ij
，∀i ∈ N，j ∈ N，k ∈ K （12）
∑i ∈ N
yk
ji -∑i ∈ N
yk
ij = pj
，∀j ∈ M，k ∈ K （13）
式（12）表示从受灾点i驶向受灾点j的车辆k上
的物资量 yk
ij 要小于车辆额定载量 Q；式（13）表示应
急救援车辆 k在受灾点 j卸下了其物资需求量 pj
，即
车辆k在受灾点j前的车内物资量与经过受灾点j后
的车内物资量的差值。
2.2.5　时间约束与优先级约束
T k
j - T k
i ≤ t
l
ij + (t
u
ij - t
l
ij )xk
ij
，∀i ∈ M，j ∈ M，k ∈ K（14）
(T k
j - T k
i )( pj - pi ) ≤ 0，∀i ∈ M，j ∈ M，k ∈ K
（15）
116
第4期 陈红，等：突发事件下应急救援物资车辆路径选择方法
∑i ∈ M
∑
j ∈ M
t
u
ij xk
ij ≤ Tmax
，∀k ∈ K （16）
式（14）表示车辆行驶时间顺序，即若车辆 k 从
受灾点i驶向受灾点j，则该车到达j点的时刻与其到
达 i点的时刻的差值要小于等于路段(i，j) 最大行程
时间；式（15）表示受灾点的优先配送权与车辆到达
受灾点时刻的关系，即若某受灾点需求越急迫，则
其优先配送权就越高，车辆会越早配送物资到该受
灾点；式（16）表示车辆 k 的 总行驶时间 i
∑∈ M j
∑∈ M
t
u
ij xk
ij
不能超过最长行驶时间Tmax。
2.2.6　特定情景Xr中的车辆路径wk
ij方案约束
在特点情景中，路网时间阻抗是确定的，在此
确定的阻抗下求解的车辆路径wk
ij方案，同样要满足
车辆路径变量xk
ij的站点、车辆、优先级等方面的约束。
3　算法设计
应急救援车辆物资配送路径优化模型是容量
限制的车辆路径问题。Benders 分解算法常用来求
解同时包含了 0-1 整数变量和连续变量的极值问
题。其将问题分解为许多小问题来做。其具体思
想是采用割平面法，固定复杂变量（0-1 整数变
量），得到子问题，不断迭代，产生 Benders 割约束，
并将其加入主问题，直至得到最优解。该算法不断
迭代，生成新约束条件，故又被称为行生成算法。
在基于阻抗区间的应急救援车辆调度模型中，
第一步是确定一个车辆调度方案，第二步是得到基
于该方案的特定路网阻抗情景下的最佳车辆调度
方案，第三步是通过计算两个方案的总行程时间差
值，得到最小鲁棒成本。
3.1　应急救援车辆路径优化模型分解
3.1.1　子模型
先确定车辆路径xk
ij
，式（17）中 i
∑∈ N j
∑∈ N k
∑∈ K
t
u
ij
-
xk
ij为
车辆 k 的路径行驶时间成本，再把没被车辆通过的
路段行程时间替换成区间阻抗的区间下界值，
i
∑∈ N j
∑∈ N k
∑∈ K
[ t
u
ij
-
xk
ij + t
l
ij (1 - -
xk
ij )wk
ij 为基于车辆路径 -
xk
ij
的特定路网阻抗情景下的最优路径的最小行驶时
间成本，两者差为鲁棒成本C。
max C =∑i ∈ N
∑
j ∈ N
∑k ∈ K
t
u
ij
-
xk
ij -∑i ∈ N
∑
j ∈ N
∑k ∈ K
[ t
u
ij
-
xk
ij + t
l
ij (1 - -
xk
ij )wk
ij ]
（17）
∑
j ∈ M
∑k ∈ K
wk
ij ≥ 1，∀i ∈ S （18）
∑
j ∈ M
∑k ∈ K
wk
ji ≥ 1，∀i ∈ S （19）
∑i ∈ N
∑k ∈ K
wk
ij = 1，∀j ∈ M （20）
∑i ∈ N
wk
ij -∑i ∈ N
wk
ji = 0，∀j ∈ N，k ∈ K （21）
∑
j ∈ N
wk
ji =∑
j ∈ N
wk
ij ≤ 1，∀k ∈ K，i ∈ S （22）
∑i ∈ N
wk
ij =∑i ∈ N
wk
ji ≤ 1，∀k ∈ K，j ∈ M （23）
∑k ∈ K
wk
ij ≤ 1，∀i ∈ N，j ∈ N （24）
yk
ij ≤ Q ⋅ wk
ij
，∀i ∈ N，j ∈ N，k ∈ K （25）
∑i ∈ N
yk
ij -∑i ∈ N
yk
ji ≤ pj
，∀j ∈ M，k ∈ K （26）
T k
j - T k
i ≤ t
l
ij + (t
u
ij - t
l
ij )wk
ij
，∀i ∈ M，j ∈ M，k ∈ K（27）
(T k
j - T k
i )( pj - pi ) ≤ 0，∀i ∈ M，j ∈ M，k ∈ K（28）
(T k
j - T k
i )( pj - pi ) ≤ 0，∀i ∈ M，j ∈ M，k ∈ K（29）
■
■
■
|
|
|
|
|
|
|
|
|
|
|
|
||
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
s.t.
随着迭代次数增加，应急车辆调度方案的鲁棒
成本逐渐向下界靠拢。
3.1.2　主模型
min z （30）
■
■
■
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
|
||
|
|
|
|
|
|
|
|
|
|
|
|
|
z ≥ ∑i ∈ M
∑
j ∈ M
∑k ∈ K
t
u
ij xk
ij -∑i ∈ M
∑
j ∈ M
∑k ∈ K
[ t
u
ij + (t
u
ij - t
l
ij )xk
ij ] wk
ij
（31）
∑
j ∈ M
∑k ∈ K
xk
ij ≥ 1，∀i ∈ S （32）
∑
j ∈ M
∑k ∈ K
xk
ji ≥ 1，∀i ∈ S （33）
∑i ∈ N
∑k ∈ K
xk
ij = 1，∀j ∈ M （34）
∑i ∈ N
xk
ij -∑i ∈ N
xk
ji = 0，∀j ∈ N，k ∈ K （35）
∑
j ∈ N
xk
ji =∑
j ∈ N
xk
ij ≤ 1，∀k ∈ K，i ∈ S （36）
∑i ∈ N
xk
ij =∑i ∈ N
xk
ji ≤ 1，∀k ∈ K，j ∈ M （37）
∑k ∈ K
xk
ij ≤ 1，∀i ∈ N，j ∈ N （38）
yk
ij ≤ Q ⋅ xk
ij
，∀i ∈ N，j ∈ N，k ∈ K （39）
∑i ∈ N
yk
ij -∑i ∈ N
yk
ji ≤ pj
，∀j ∈ M，k ∈ K （40）
T k
j - T k
i ≤ t
e
ij + (t
u
ij - t
l
ij )xk
ij
，∀i ∈ M，j ∈ M，k ∈ K（41）
(T k
j - T k
i )( pj - pi ) ≤ 0，∀i ∈ M，j ∈ M，k ∈ K（42）
∑i ∈ M
∑
j ∈ M
t
u
ij xk
ij ≤ Tmax
，∀k ∈ K （43）
s.t.
117
交 通 科 学 与 工 程 第39卷
主模型每被迭代一次， Benders 可行性分割约
束将被添加一次，使鲁棒成本逐渐向其上界靠拢。
当上界值等于下界值时，迭代停止。
3.2　计算步骤
1）设置模型初始上界值 bl为 - ∞，初始下界
值 bu为 + ∞，路网行程时间取区间阻抗下界值，求
解松弛主问题，即得可行方案xk
ij
；
2）把 -
xk
ij 为 1 的路段行程时间取为区间阻抗
的上界值，
-
xk
ij 为 0 的路段行程时间取为区间阻抗
下 界 值 ，求 解 子 模 型 得 到 wk
ij
、yk
ij，若 C < bu，则
更新 bu；
3）把 wk
ij 代进主模型，新加约束（31），对其进行
求解，得到新调度方案xk
ij
、yk
ij
，若Z > bl
，则更新bl
；
4）若 bl = bu，则输出最优解，停止计算，否则
进入5）；
5）令xk
ij = -
xk
ij
，返回2），进行迭代，直至bl = bu。
4　算例分析
本研究采用仿真交通路网来测试模型与算
法。在该仿真交通路网中 ，共存在 53 个节点、
125 条路段，区间阻抗为图中路段上括号内的数
据。其中，设节点 1～3 为应急物资集散中心，
容量限制为 3 500 kg；设节点 17～46 为受灾点；
受灾点物资需求数据见表 1，该数据由数值软件
MATLAB 随机生成，也可根据受灾点特性对受灾
点进行需求评估，并且以此为物资配送需求量，
配送时将优先配送物资需求量大的受灾点。假
定应急物资配送中心有 6 辆车，每辆车出发时刻
均为 9∶00，车辆额定载重均为 1 500 kg，每辆车
的最大行驶时间均限制为 45 min，既每辆车总救
援时间不超过 45 min。
采用 AMPL 软件，编写应急救援车辆路径优化
模型的 Benders 算法，调用 CPLEX 对算例进行求
解。应急救援车辆路径优化模型迭代了 5 次，求解
时间为 0.15 s，总鲁棒成本为 93.8，鲁棒车辆调度方
案见表 2，应急救援车辆物资配送路径图如图 1 所
示，表 2 中最短路径的括号的点表示该点仅经过但
不停留。
表1 受灾点物资需求数据
Table 1 Material demand data
受灾点
17
18
19
20
21
22
23
24
25
26
需求量/ kg
192
197
107
183
197
232
153
217
193
216
受灾点
27
28
29
30
31
32
33
34
35
36
需求量/ kg
196
243
218
277
186
184
107
203
178
247
受灾点
37
38
39
40
41
42
43
44
45
46
需求量/ kg
194
179
183
184
206
229
246
223
220
200
表2 鲁棒车辆调度方案
Table 2 Robust vehicle scheduling scheme
集散
中心
1
2
3
车辆
1
2
3
4
5
6
上界最短路径
1-45-46-18-（6）-17-（7）-
20-1
1-（29）-30-36-（30）-43-28-
44-29-1
2--22-21-（40）-（10）-31-
32-19-33-2
2-（16）-34-37-（4）-40-39-
38-35-23-2
3-（41）-24-41-27-25-3
3-42-26-（42）-3
载重量/
kg
992
1 454
1 013
1 274
812
445
返回
时刻
9∶34
9∶37
9∶36
9∶41
9∶21
9∶14
总鲁棒
成本
93.8
从图 1 可以看出，加粗的车辆路线为车辆调度
方案中各应急救援车辆物资配送路径，路段上的区
间值表示为车辆在路段上不确定的行程时间。各
车辆以应急物资集散中心节点 1、2、3 为起终点，在
不确定的路况下，沿途经过各受灾点进行物资配
送，满足每个受灾点的物资需求。
由表 2 可知，每个应急集散中心均派出了 2 辆
车，所有受灾点物资需求均得到了满足，每辆车的
最大行驶时间（总救援时间）均没超过时间限制，如
1号集散中心的车辆最大行程时间分别为34、37 min；
且满足了受灾点的物资需求量大而优先救援的策
略，如 45 号受灾点的需求量为 220 kg，该需求量大
118
第4期 陈红，等：突发事件下应急救援物资车辆路径选择方法
于46号的需求量200 kg，故45号受灾点得到了优先
配送。本研究得到的配送方案的鲁棒成本更小，行
程时间更稳定可靠，可供应急物流决策参考。
5　结论
突发事件发生后的救援具有紧迫性，需要及时
地把应急物资从应急物资集散中心配送到受灾点。
因此，应急救援车辆调度方案的确定是应急救援的
关键。本研究以车辆调度方案最大后悔值最小化
为目标，考虑受灾点需求优先级、车辆最大行驶时
间、车辆载重量限制等约束条件，基于路段区间阻
抗来构建应急救援车辆调度模型，在交通路网阻抗
不确定的情况下，保证得到最优的车辆调度方案。
通过AMPL软件，采用Benders分解算法对模型进行
编程，求解算例。研究结果表明，本模型在满足所
有受灾点物资需求的情况下，应急救援车辆路径方
案具有较强的稳定性与鲁棒性，可为突发事件下考
虑不确定性因素的应急救援调度方案的决策提供
借鉴。