
{
  "1": {
    "2": [[600, 1440, 9, 13]],
    "3": [[600, 1440, 7, 15]],
    "4": [[600, 1440, 6, 12]],
    "5": [[600, 1440, 10, 14]]
  },
  "2": {
    "1": [[600, 1440, 9, 13]],
    "3": [[600, 1440, 7, 11]],
    "6": [[600, 1440, 9, 13]],
    "7": [[600, 1440, 6, 10]]
  },
  "3": {
    "1": [[600, 1440, 7, 15]],
    "2": [[600, 1440, 7, 11]],
    "4": [[600, 1440, 8, 14]],
    "7": [[600, 1440, 9, 15]],
    "8": [[600, 1440, 7, 13]]
  },
  "4": {
    "1": [[600, 1440, 6, 12]],
    "3": [[600, 1440, 8, 14]],
    "5": [[600, 1440, 8, 12]],
    "8": [[600, 1440, 9, 14]],
    "9": [[600, 1440, 7, 13]]
  },
  "5": {
    "1": [[600, 1440, 10, 14]],
    "4": [[600, 1440, 8, 12]],
    "9": [[600, 1440, 8, 12]],
    "10": [[600, 1440, 7, 11]]
  },
  "6": {
    "2": [[600, 1440, 9, 13]],
    "7": [[600, 1440, 6, 10]],
    "11": [[600, 1440, 8, 13]]
  },
  "7": {
    "2": [[600, 1440, 6, 10]],
    "3": [[600, 1440, 9, 15]],
    "6": [[600, 1440, 6, 10]],
    "8": [[600, 1440, 10, 14]],
    "11": [[600, 1440, 7, 13]],
    "12": [[600, 1440, 8, 12]]
  },
  "8": {
    "3": [[600, 1440, 7, 13]],
    "4": [[600, 1440, 9, 14]],
    "7": [[600, 1440, 10, 14]],
    "9": [[600, 1440, 8, 13]],
    "12": [[600, 1440, 10, 14]],
    "13": [[600, 1440, 6, 10]]
  },
  "9": {
    "4": [[600, 1440, 7, 13]],
    "5": [[600, 1440, 8, 12]],
    "8": [[600, 1440, 8, 13]],
    "10": [[600, 1440, 8, 13]],
    "13": [[600, 1440, 10, 14]],
    "14": [[600, 1440, 7, 10]]
  },
  "10": {
    "5": [[600, 1440, 7, 11]],
    "9": [[600, 1440, 8, 13]],
    "14": [[600, 1440, 9, 14]],
    "15": [[600, 1440, 7, 11]]
  },
  "11": {
    "6": [[600, 1440, 8, 13]],
    "7": [[600, 1440, 7, 13]],
    "12": [[600, 1440, 8, 12]],
    "16": [[600, 1440, 7, 11]],
    "17": [[600, 1440, 10, 14]]
  },
  "12": {
    "7": [[600, 1440, 8, 12]],
    "8": [[600, 1440, 10, 14]],
    "11": [[600, 1440, 8, 12]],
    "13": [[600, 1440, 8, 12]],
    "17": [[600, 1440, 10, 14]],
    "18": [[600, 1440, 7, 11]]
  },
  "13": {
    "8": [[600, 1440, 6, 10]],
    "9": [[600, 1440, 10, 14]],
    "12": [[600, 1440, 8, 12]],
    "14": [[600, 1440, 7, 11]],
    "18": [[600, 1440, 10, 14]],
    "19": [[600, 1440, 9, 12]]
  },
  "14": {
    "9": [[600, 1440, 7, 10]],
    "10": [[600, 1440, 9, 14]],
    "13": [[600, 1440, 7, 11]],
    "15": [[600, 1440, 8, 12]],
    "19": [[600, 1440, 7, 11]]
  },
  "15": {
    "10": [[600, 1440, 7, 11]],
    "14": [[600, 1440, 8, 12]],
    "19": [[600, 1440, 8, 12]]
  },
  "16": {
    "11": [[600, 1440, 7, 11]],
    "17": [[600, 1440, 10, 14]],
    "20": [[600, 1440, 6, 10]]
  },
  "17": {
    "11": [[600, 1440, 10, 14]],
    "12": [[600, 1440, 10, 14]],
    "16": [[600, 1440, 10, 14]],
    "18": [[600, 1440, 10, 14]],
    "20": [[600, 1440, 6, 10]]
  },
  "18": {
    "12": [[600, 1440, 7, 11]],
    "13": [[600, 1440, 10, 14]],
    "17": [[600, 1440, 10, 14]],
    "19": [[600, 1440, 10, 14]],
    "20": [[600, 1440, 9, 12]]
  },
  "19": {
    "13": [[600, 1440, 9, 12]],
    "14": [[600, 1440, 7, 11]],
    "15": [[600, 1440, 8, 12]],
    "18": [[600, 1440, 10, 14]],
    "20": [[600, 1440, 10, 14]]
  },
  "20": {
    "16": [[600, 1440, 6, 10]],
    "17": [[600, 1440, 6, 10]],
    "18": [[600, 1440, 9, 12]],
    "19": [[600, 1440, 10, 14]]
  }
}

"""


下界最短路径: [1, 2, 7, 11, 16, 20]，路径的下界阻抗总和: 35
上界最短路径: [1, 2, 7, 11, 16, 20]，路径的上界阻抗总和: 57
鲁棒成本最短路: [1, 2, 7, 11, 16, 20]，上界阻抗: 57     情景最短路：[1, 4， 9， 14， 19， 20]，下界阻抗: 37    鲁棒成本: 20
"""

{
  "1": {
    "2": [[540, 660, 3, 6]],
    "3": [[540, 660, 2, 5]],
    "4": [[540, 660, 4, 7]],
    "5": [[540, 660, 3, 6]]
  },
  "2": {
    "1": [[540, 660, 3, 6]],
    "3": [[540, 660, 3, 7]],
    "6": [[540, 660, 5, 7]],
    "7": [[540, 660, 2, 6]]
  },
  "3": {
    "1": [[540, 660, 2, 5]],
    "2": [[540, 660, 3, 7]],
    "4": [[540, 660, 2, 5]],
    "7": [[540, 660, 5, 7]],
    "8": [[540, 660, 3, 6]]
  },
  "4": {
    "1": [[540, 660, 4, 7]],
    "3": [[540, 660, 2, 5]],
    "5": [[540, 660, 3, 6]],
    "8": [[540, 660, 5, 7]],
    "9": [[540, 660, 2, 5]]
  },
  "5": {
    "1": [[540, 660, 3, 6]],
    "4": [[540, 660, 3, 6]],
    "9": [[540, 660, 2, 5]],
    "10": [[540, 660, 3, 7]]
  },
  "6": {
    "2": [[540, 660, 5, 7]],
    "7": [[540, 660, 2, 6]],
    "11": [[540, 660, 3, 5]]
  },
  "7": {
    "2": [[540, 660, 2, 6]],
    "3": [[540, 660, 5, 7]],
    "6": [[540, 660, 2, 6]],
    "8": [[540, 660, 3, 6]],
    "11": [[540, 660, 2, 5]],
    "12": [[540, 660, 3, 6]]
  },
  "8": {
    "3": [[540, 660, 3, 6]],
    "4": [[540, 660, 5, 7]],
    "7": [[540, 660, 3, 6]],
    "9": [[540, 660, 5, 7]],
    "12": [[540, 660, 2, 6]],
    "13": [[540, 660, 3, 5]]
  },
  "9": {
    "4": [[540, 660, 2, 5]],
    "5": [[540, 660, 2, 5]],
    "8": [[540, 660, 5, 7]],
    "10": [[540, 660, 2, 5]],
    "13": [[540, 660, 5, 7]],
    "14": [[540, 660, 3, 6]]
  },
  "10": {
    "5": [[540, 660, 3, 7]],
    "9": [[540, 660, 2, 5]],
    "14": [[540, 660, 3, 6]],
    "15": [[540, 660, 5, 7]]
  },
  "11": {
    "6": [[540, 660, 3, 5]],
    "7": [[540, 660, 2, 5]],
    "12": [[540, 660, 4, 7]],
    "16": [[540, 660, 2, 5]],
    "17": [[540, 660, 3, 6]]
  },
  "12": {
    "7": [[540, 660, 3, 6]],
    "8": [[540, 660, 2, 6]],
    "11": [[540, 660, 4, 7]],
    "13": [[540, 660, 5, 7]],
    "17": [[540, 660, 2, 5]],
    "18": [[540, 660, 3, 6]]
  },
  "13": {
    "8": [[540, 660, 3, 5]],
    "9": [[540, 660, 5, 7]],
    "12": [[540, 660, 5, 7]],
    "14": [[540, 660, 2, 5]],
    "18": [[540, 660, 3, 6]],
    "19": [[540, 660, 5, 7]]
  },
  "14": {
    "9": [[540, 660, 3, 6]],
    "10": [[540, 660, 3, 6]],
    "13": [[540, 660, 2, 5]],
    "15": [[540, 660, 2, 5]],
    "19": [[540, 660, 3, 6]]
  },
  "15": {
    "10": [[540, 660, 5, 7]],
    "14": [[540, 660, 2, 5]],
    "19": [[540, 660, 4, 7]]
  },
  "16": {
    "11": [[540, 660, 2, 5]],
    "17": [[540, 660, 3, 6]],
    "20": [[540, 660, 4, 7]]
  },
  "17": {
    "11": [[540, 660, 3, 6]],
    "12": [[540, 660, 2, 5]],
    "16": [[540, 660, 3, 6]],
    "18": [[540, 660, 2, 6]],
    "20": [[540, 660, 3, 7]]
  },
  "18": {
    "12": [[540, 660, 3, 6]],
    "13": [[540, 660, 3, 6]],
    "17": [[540, 660, 2, 6]],
    "19": [[540, 660, 3, 7]],
    "20": [[540, 660, 2, 7]]
  },
  "19": {
    "13": [[540, 660, 5, 7]],
    "14": [[540, 660, 3, 6]],
    "15": [[540, 660, 4, 7]],
    "18": [[540, 660, 3, 7]],
    "20": [[540, 660, 2, 7]]
  },
  "20": {
    "16": [[540, 660, 4, 7]],
    "17": [[540, 660, 3, 7]],
    "18": [[540, 660, 2, 7]],
    "19": [[540, 660, 2, 7]]
  }
}

"""
(1,2,3,6),(2,1,3,6),(1,3,2,5),(3,1,2,5),(1,4,4,7),(4,1,4,7),(1,5,3,6),(5,1,3,6),
(2,3,3,7),(3,2,3,7),(2,6,5,7),(6,2,5,7),(2,7,2,6),(7,2,2,6),(3,4,2,5),(4,3,2,5),
(3,7,5,7),(7,3,5,7),(3,8,3,6),(8,3,3,6),(4,5,3,6),(5,4,3,6),(4,8,5,7),(8,4,5,7),
(4,9,2,5),(9,4,2,5),(5,9,2,5),(9,5,2,5),(5,10,3,7),(10,5,3,7),(6,7,2,6),(7,6,2,6),
(6,11,3,5),(11,6,3,5),(7,8,3,6),(8,7,3,6),(7,11,2,5),(11,7,2,5),(7,12,3,6),(12,7,3,6),
(8,9,5,7),(9,8,5,7),(8,12,2,6),(12,8,2,6),(8,13,3,5),(13,8,3,5),(9,10,2,5),(10,9,2,5),
(9,13,5,7),(13,9,5,7),(9,14,3,6),(14,9,3,6),(10,14,3,6),(14,10,3,6),(10,15,5,7),(15,10,5,7),
(11,12,4,7),(12,11,4,7),(11,16,2,5),(16,11,2,5),(11,17,3,6),(17,11,3,6),(12,13,5,7),(13,12,5,7),
(12,17,2,5),(17,12,2,5),(12,18,3,6),(18,12,3,6),(13,14,2,5),(14,13,2,5),(13,18,3,6),(18,13,3,6),
(13,19,5,7),(19,13,5,7),(14,15,2,5),(15,14,2,5),(14,19,3,6),(19,14,3,6),(15,19,4,7),(19,15,4,7),
(16,17,3,6),(17,16,3,6),(16,20,4,7),(20,16,4,7),(17,18,2,6),(18,17,2,6),(17,20,3,7),(20,17,3,7),
(18,19,3,7),(19,18,3,7),(18,20,2,7),(20,18,2,7),(19,20,2,7),(20,19,2,7)

下界最短路径: [1，3，8，12，17，20]，路径的下界阻抗总和: 12
上界最短路径: [1，3，8，13，18，20]，路径的下界阻抗总和: 29
鲁棒成本最短路: [1，3，8，12，17，20]，上界阻抗:29     情景最短路：[1，2，7，11，16，20]，下界阻抗:13     鲁棒成本:16 
"""