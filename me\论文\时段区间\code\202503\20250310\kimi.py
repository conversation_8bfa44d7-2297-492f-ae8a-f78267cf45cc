import heapq
from collections import defaultdict

# 定义路网结构
road_network = {
    'A': {
        'B': [
            [0, 480, 20, 30],
            [480, 600, 30, 50],
            [600, 1440, 20, 40]
        ],
        'C': [
            [0, 480, 11, 40],
            [480, 600, 20, 55],
            [600, 1440, 15, 35]
        ],
        'D': [
            [0, 480, 15, 35],
            [480, 600, 17, 44],
            [600, 1440, 18, 42]
        ]
    },
    'B': {
        'A': [
            [0, 480, 20, 30],
            [480, 600, 30, 50],
            [600, 1440, 20, 40]
        ],
        'C': [
            [0, 480, 8, 40],
            [480, 600, 20, 60],
            [600, 1440, 11, 35]
        ],
        'E': [
            [0, 480, 11, 35],
            [480, 600, 20, 44],
            [600, 1440, 21, 42]
        ]
    },
    'C': {
        'A': [
            [0, 480, 11, 40],
            [480, 600, 20, 55],
            [600, 1440, 15, 35]
        ],
        'B': [
            [0, 480, 8, 40],
            [480, 600, 20, 60],
            [600, 1440, 11, 35]
        ],
        'D': [
            [0, 480, 11, 44],
            [480, 600, 21, 50],
            [600, 1440, 21, 42]
        ],
        'E': [
            [0, 480, 11, 41],
            [480, 600, 12, 60],
            [600, 1440, 8, 35]
        ]
    },
    'D': {
        'A': [
            [0, 480, 15, 35],
            [480, 600, 17, 44],
            [600, 1440, 18, 42]
        ],
        'C': [
            [0, 480, 11, 44],
            [480, 600, 21, 50],
            [600, 1440, 21, 42]
        ],
        'E': [
            [0, 480, 20, 40],
            [480, 600, 15, 55],
            [600, 1440, 11, 35]
        ]
    },
    'E': {}  # 添加终点E的空字典，避免KeyError
}

# 计算路径的鲁棒成本
def calculate_robust_cost(path, start_time):
    total_upper = 0
    for i in range(len(path) - 1):
        current_node = path[i]
        next_node = path[i + 1]
        edges = road_network[current_node][next_node]
        # 根据当前时间选择对应的时段
        for edge in edges:
            if edge[0] <= start_time < edge[1]:
                total_upper += edge[3]  # 取上界
                break
        # 更新开始时间，假设通过该路段需要1个时间单位
        start_time += 1
    # 计算备选路径的最佳时间
    min_lower = float('inf')
    for node in road_network:
        if node not in path:
            # 找到不包括当前路径的其他路段的下界总和的最小值
            lower_sum = 0
            for neighbor in road_network[node]:
                for interval in road_network[node][neighbor]:
                    lower_sum += interval[2]  # 取下界
            if lower_sum < min_lower:
                min_lower = lower_sum
    robust_cost = total_upper - min_lower
    return robust_cost

# 使用Dijkstra算法找到最短路径
def find_shortest_path(start_node, end_node):
    # 初始化距离字典
    distances = {node: float('inf') for node in road_network}
    distances[start_node] = 0
    # 优先队列，存储(当前时间, 当前节点, 当前路径)
    priority_queue = [(0, start_node, [start_node])]
    while priority_queue:
        current_time, current_node, current_path = heapq.heappop(priority_queue)
        if current_node == end_node:
            return current_path, current_time
        if current_time > distances[current_node]:
            continue
        for neighbor in road_network[current_node]:
            # 计算通过该边的时间成本，假设为1
            new_time = current_time + 1
            if new_time < distances[neighbor]:
                distances[neighbor] = new_time
                new_path = current_path + [neighbor]
                heapq.heappush(priority_queue, (new_time, neighbor, new_path))
    return None, float('inf')

# 主函数
def main():
    start_node = 'A'
    end_node = 'E'
    # 找到最短路径
    shortest_path, _ = find_shortest_path(start_node, end_node)
    if shortest_path:
        print(f"最短路径: {shortest_path}")
        # 计算鲁棒成本
        robust_cost = calculate_robust_cost(shortest_path, 0)  # 假设开始时间为0
        print(f"鲁棒成本: {robust_cost}")
    else:
        print("未找到路径")

if __name__ == "__main__":
    main()