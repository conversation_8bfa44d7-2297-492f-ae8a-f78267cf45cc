
{
  "1": {
    "2": [[480, 600, 12, 25]],
    "3": [[480, 600, 8, 22]],
    "4": [[480, 600, 15, 28]],
    "5": [[480, 600, 20, 30]]
  },
  "2": {
    "1": [[480, 600, 12, 25]],
    "3": [[480, 600, 11, 27]],
    "6": [[480, 600, 18, 29]],
    "7": [[480, 600, 10, 23]]
  },
  "3": {
    "1": [[480, 600, 8, 22]],
    "2": [[480, 600, 11, 27]],
    "4": [[480, 600, 17, 28]],
    "7": [[480, 600, 20, 30]],
    "8": [[480, 600, 13, 25]]
  },
  "4": {
    "1": [[480, 600, 15, 28]],
    "3": [[480, 600, 17, 28]],
    "5": [[480, 600, 19, 30]],
    "8": [[480, 600, 12, 27]],
    "9": [[480, 600, 8, 22]]
  },
  "5": {
    "1": [[480, 600, 20, 30]],
    "4": [[480, 600, 19, 30]],
    "9": [[480, 600, 20, 30]],
    "10": [[480, 600, 13, 24]]
  },
  "6": {
    "2": [[480, 600, 18, 29]],
    "7": [[480, 600, 17, 29]],
    "11": [[480, 600, 10, 26]]
  },
  "7": {
    "2": [[480, 600, 10, 23]],
    "3": [[480, 600, 20, 30]],
    "6": [[480, 600, 17, 29]],
    "8": [[480, 600, 11, 27]],
    "11": [[480, 600, 20, 29]],
    "12": [[480, 600, 9, 24]]
  },
  "8": {
    "3": [[480, 600, 13, 25]],
    "4": [[480, 600, 12, 27]],
    "7": [[480, 600, 11, 27]],
    "9": [[480, 600, 19, 30]],
    "12": [[480, 600, 12, 26]],
    "13": [[480, 600, 8, 22]]
  },
  "9": {
    "4": [[480, 600, 8, 22]],
    "5": [[480, 600, 20, 30]],
    "8": [[480, 600, 19, 30]],
    "10": [[480, 600, 10, 24]],
    "13": [[480, 600, 17, 29]],
    "14": [[480, 600, 9, 23]]
  },
  "10": {
    "5": [[480, 600, 13, 24]],
    "9": [[480, 600, 10, 24]],
    "14": [[480, 600, 13, 26]],
    "15": [[480, 600, 8, 22]]
  },
  "11": {
    "6": [[480, 600, 10, 26]],
    "7": [[480, 600, 20, 29]],
    "12": [[480, 600, 20, 30]],
    "16": [[480, 600, 11, 25]],
    "17": [[480, 600, 17, 29]]
  },
  "12": {
    "7": [[480, 600, 9, 24]],
    "8": [[480, 600, 12, 26]],
    "11": [[480, 600, 20, 30]],
    "13": [[480, 600, 19, 30]],
    "17": [[480, 600, 10, 24]],
    "18": [[480, 600, 8, 22]]
  },
  "13": {
    "8": [[480, 600, 8, 22]],
    "9": [[480, 600, 17, 29]],
    "12": [[480, 600, 19, 30]],
    "14": [[480, 600, 10, 23]],
    "18": [[480, 600, 11, 25]],
    "19": [[480, 600, 20, 30]]
  },
  "14": {
    "9": [[480, 600, 9, 23]],
    "10": [[480, 600, 13, 26]],
    "13": [[480, 600, 10, 23]],
    "15": [[480, 600, 10, 23]],
    "19": [[480, 600, 13, 26]]
  },
  "15": {
    "10": [[480, 600, 8, 22]],
    "14": [[480, 600, 10, 23]],
    "19": [[480, 600, 19, 30]]
  },
  "16": {
    "11": [[480, 600, 11, 25]],
    "17": [[480, 600, 12, 29]],
    "20": [[480, 600, 8, 22]]
  },
  "17": {
    "11": [[480, 600, 17, 29]],
    "12": [[480, 600, 10, 24]],
    "16": [[480, 600, 12, 29]],
    "18": [[480, 600, 20, 29]],
    "20": [[480, 600, 11, 23]]
  },
  "18": {
    "12": [[480, 600, 8, 22]],
    "13": [[480, 600, 11, 25]],
    "17": [[480, 600, 20, 29]],
    "19": [[480, 600, 18, 30]],
    "20": [[480, 600, 12, 24]]
  },
  "19": {
    "13": [[480, 600, 20, 30]],
    "14": [[480, 600, 13, 26]],
    "15": [[480, 600, 19, 30]],
    "18": [[480, 600, 18, 30]],
    "20": [[480, 600, 20, 30]]
  },
  "20": {
    "16": [[480, 600, 8, 22]],
    "17": [[480, 600, 11, 23]],
    "18": [[480, 600, 12, 24]],
    "19": [[480, 600, 20, 30]]
  }
}

"""
(1,2,12,25),(2,1,12,25),(1,3,8,22),(3,1,8,22),(1,4,15,28),(4,1,15,28),(1,5,20,30),(5,1,20,30),
(2,3,11,27),(3,2,11,27),(2,6,18,29),(6,2,18,29),(2,7,10,23),(7,2,10,23),(3,4,17,28),(4,3,17,28),
(3,7,20,30),(7,3,20,30),(3,8,13,25),(8,3,13,25),(4,5,19,30),(5,4,19,30),(4,8,12,27),(8,4,12,27),
(4,9,8,22),(9,4,8,22),(5,9,20,30),(9,5,20,30),(5,10,13,24),(10,5,13,24),(6,7,17,29),(7,6,17,29),
(6,11,10,26),(11,6,10,26),(7,8,11,27),(8,7,11,27),(7,11,20,29),(11,7,20,29),(7,12,9,24),(12,7,9,24),
(8,9,19,30),(9,8,19,30),(8,12,12,26),(12,8,12,26),(8,13,8,22),(13,8,8,22),(9,10,10,24),(10,9,10,24),
(9,13,17,29),(13,9,17,29),(9,14,9,23),(14,9,9,23),(10,14,13,26),(14,10,13,26),(10,15,8,22),(15,10,8,22),
(11,12,20,30),(12,11,20,30),(11,16,11,25),(16,11,11,25),(11,17,17,29),(17,11,17,29),(12,13,19,30),(13,12,19,30),
(12,17,10,24),(17,12,10,24),(12,18,8,22),(18,12,8,22),(13,14,10,23),(14,13,10,23),(13,18,11,25),(18,13,11,25),
(13,19,20,30),(19,13,20,30),(14,15,10,23),(15,14,10,23),(14,19,13,26),(19,14,13,26),(15,19,19,30),(19,15,19,30),
(16,17,12,29),(17,16,12,29),(16,20,8,22),(20,16,8,22),(17,20,11,23),(20,17,11,23),(18,19,18,30),(19,18,18,30),
(18,20,12,24),(20,18,12,24),(19,20,20,30),(20,19,20,30)

下界最短路径: [1, 2, 7, 12, 18, 20]，路径的下界阻抗总和: 51
上界最短路径: [1, 3, 8, 13, 18, 20], 路径的上界阻抗总和: 118
鲁棒成本最短路: [1, 3, 7, 12, 18, 20]，鲁棒成本: 63
"""

{
  "1": {
    "2": [[420, 540, 9, 14]],
    "3": [[420, 540, 7, 13]],
    "4": [[420, 540, 6, 12]],
    "5": [[420, 540, 10, 15]]
  },
  "2": {
    "1": [[420, 540, 9, 14]],
    "3": [[420, 540, 6, 11]],
    "6": [[420, 540, 12, 15]],
    "7": [[420, 540, 10, 14]]
  },
  "3": {
    "1": [[420, 540, 7, 13]],
    "2": [[420, 540, 6, 11]],
    "4": [[420, 540, 11, 15]],
    "7": [[420, 540, 8, 12]],
    "8": [[420, 540, 10, 15]]
  },
  "4": {
    "1": [[420, 540, 6, 12]],
    "3": [[420, 540, 11, 15]],
    "5": [[420, 540, 9, 14]],
    "8": [[420, 540, 7, 13]],
    "9": [[420, 540, 6, 12]]
  },
  "5": {
    "1": [[420, 540, 10, 15]],
    "4": [[420, 540, 9, 14]],
    "9": [[420, 540, 7, 12]],
    "10": [[420, 540, 9, 15]]
  },
  "6": {
    "2": [[420, 540, 12, 15]],
    "7": [[420, 540, 8, 14]],
    "11": [[420, 540, 6, 12]]
  },
  "7": {
    "2": [[420, 540, 10, 14]],
    "3": [[420, 540, 8, 12]],
    "6": [[420, 540, 8, 14]],
    "8": [[420, 540, 9, 14]],
    "11": [[420, 540, 8, 14]],
    "12": [[420, 540, 6, 11]]
  },
  "8": {
    "3": [[420, 540, 10, 15]],
    "4": [[420, 540, 7, 13]],
    "7": [[420, 540, 9, 14]],
    "9": [[420, 540, 8, 12]],
    "12": [[420, 540, 11, 15]],
    "13": [[420, 540, 6, 11]]
  },
  "9": {
    "4": [[420, 540, 6, 12]],
    "5": [[420, 540, 7, 12]],
    "8": [[420, 540, 8, 12]],
    "10": [[420, 540, 10, 14]],
    "13": [[420, 540, 11, 15]],
    "14": [[420, 540, 6, 11]]
  },
  "10": {
    "5": [[420, 540, 9, 15]],
    "9": [[420, 540, 10, 14]],
    "14": [[420, 540, 10, 14]],
    "15": [[420, 540, 8, 12]]
  },
  "11": {
    "6": [[420, 540, 6, 12]],
    "7": [[420, 540, 8, 14]],
    "12": [[420, 540, 10, 15]],
    "16": [[420, 540, 7, 13]],
    "17": [[420, 540, 8, 12]]
  },
  "12": {
    "7": [[420, 540, 6, 11]],
    "8": [[420, 540, 11, 15]],
    "11": [[420, 540, 10, 15]],
    "13": [[420, 540, 8, 14]],
    "17": [[420, 540, 6, 11]],
    "18": [[420, 540, 9, 15]]
  },
  "13": {
    "8": [[420, 540, 6, 11]],
    "9": [[420, 540, 11, 15]],
    "12": [[420, 540, 8, 14]],
    "14": [[420, 540, 7, 13]],
    "18": [[420, 540, 9, 15]],
    "19": [[420, 540, 9, 15]]
  },
  "14": {
    "9": [[420, 540, 6, 11]],
    "10": [[420, 540, 10, 14]],
    "13": [[420, 540, 7, 13]],
    "15": [[420, 540, 10, 13]],
    "19": [[420, 540, 6, 11]]
  },
  "15": {
    "10": [[420, 540, 8, 12]],
    "14": [[420, 540, 10, 13]],
    "19": [[420, 540, 7, 12]]
  },
  "16": {
    "11": [[420, 540, 7, 13]],
    "17": [[420, 540, 8, 12]],
    "20": [[420, 540, 6, 11]]
  },
  "17": {
    "11": [[420, 540, 8, 12]],
    "12": [[420, 540, 6, 11]],
    "16": [[420, 540, 8, 12]],
    "18": [[420, 540, 7, 15]],
    "20": [[420, 540, 6, 12]]
  },
  "18": {
    "12": [[420, 540, 9, 15]],
    "13": [[420, 540, 9, 15]],
    "17": [[420, 540, 7, 15]],
    "19": [[420, 540, 9, 10]],
    "20": [[420, 540, 6, 15]]
  },
  "19": {
    "13": [[420, 540, 9, 15]],
    "14": [[420, 540, 6, 11]],
    "15": [[420, 540, 7, 12]],
    "18": [[420, 540, 9, 10]],
    "20": [[420, 540, 8, 13]]
  },
  "20": {
    "16": [[420, 540, 6, 11]],
    "17": [[420, 540, 6, 12]],
    "18": [[420, 540, 6, 15]],
    "19": [[420, 540, 8, 13]]
  }
}

"""
(1,2,9,14),(2,1,9,14),(1,3,7,13),(3,1,7,13),(1,4,6,12),(4,1,6,12),(1,5,10,15),(5,1,10,15),
(2,3,6,11),(3,2,6,11),(2,6,12,15),(6,2,12,15),(2,7,10,14),(7,2,10,14),(3,4,11,15),(4,3,11,15),
(3,7,8,12),(7,3,8,12),(3,8,10,15),(8,3,10,15),(4,5,9,14),(5,4,9,14),(4,8,7,13),(8,4,7,13),
(4,9,6,12),(9,4,6,12),(5,9,7,12),(9,5,7,12),(5,10,9,15),(10,5,9,15),(6,7,8,14),(7,6,8,14),
(6,11,6,12),(11,6,6,12),(7,8,9,14),(8,7,9,14),(7,11,8,14),(11,7,8,14),(7,12,6,11),(12,7,6,11),
(8,9,8,12),(9,8,8,12),(8,12,11,15),(12,8,11,15),(8,13,6,11),(13,8,6,11),(9,10,10,14),(10,9,10,14),
(9,13,11,15),(13,9,11,15),(9,14,6,11),(14,9,6,11),(10,14,10,14),(14,10,10,14),(10,15,8,12),(15,10,8,12),
(11,12,10,15),(12,11,10,15),(11,16,7,13),(16,11,7,13),(11,17,8,12),(17,11,8,12),(12,13,8,14),(13,12,8,14),
(12,17,6,11),(17,12,6,11),(12,18,9,15),(18,12,9,15),(13,14,7,13),(14,13,7,13),(13,18,9,15),(18,13,9,15),
(13,19,9,15),(19,13,9,15),(14,15,10,13),(15,14,10,13),(14,19,6,11),(19,14,6,11),(15,19,7,12),(19,15,7,12),
(16,17,8,12),(17,16,8,12),(16,20,6,11),(20,16,6,11),(17,18,7,15),(18,17,7,15),(17,20,6,12),(20,17,6,12),
(18,19,9,10),(19,18,9,10),(18,20,6,15),(20,18,6,15),(19,20,8,13),(20,19,8,13)

下界最短路径: [1，4，9，14，19，20]，路径的下界阻抗总和: 32
上界最短路径: [1，4，9，14，19，20]，路径的上界阻抗总和: 59
鲁棒成本最短路: [1，4，9，14，19，20]，上界阻抗:59    情景最短路：[1，3，7，12，17，20]，下界阻抗:33     鲁棒成本:26 
"""