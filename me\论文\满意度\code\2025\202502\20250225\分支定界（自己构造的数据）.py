
""""用的robust_cost里的branch_and_bound里的三合一"""


"""失败：权重变了，但是路线没变"""

import numpy as np
import heapq
import time
from heapq import heappush, heappop

# 数据定义
nodes = np.arange(1, 30)  # 生成节点编号 1 到 29 的数组，总共 29 个节点
edges = np.array([
    [1, 2], [1, 3], [1, 4], [1, 5],
    [2, 3], [2, 6], [2, 11],
    [3, 4], [3, 6], [3, 7],
    [4, 5], [4, 7], [4, 8], [4, 9],
    [5, 9], [5, 10],
    [6, 7], [6, 11], [6, 12], [6, 13],
    [7, 8], [7, 13],
    [8, 9], [8, 13], [8, 14],
    [9, 10], [9, 14], [9, 15],
    [10, 15], [10, 16],
    [11, 12],
    [12, 13], [12, 17],
    [13, 14], [13, 17], [13, 18],
    [14, 15], [14, 18], [14, 19],
    [15, 16], [15, 19], [15, 20],
    [16, 20], [16, 21],
    [17, 18], [17, 22],
    [18, 19], [18, 22], [18, 23],
    [19, 20], [19, 23], [19, 24],
    [20, 21], [20, 24],
    [21, 24], [21, 25],
    [22, 23], [22, 26],
    [23, 24], [23, 26],
    [24, 25], [24, 26],
    [25, 26],
    [2, 1], [3, 1], [4, 1], [5, 1],
    [3, 2], [6, 2], [11, 2],
    [4, 3], [6, 3], [7, 3],
    [5, 4], [7, 4], [8, 4], [9, 4],
    [9, 5], [10, 5],
    [7, 6], [11, 6], [12, 6], [13, 6],
    [8, 7], [13, 7],
    [9, 8], [13, 8], [14, 8],
    [10, 9], [14, 9], [15, 9],
    [15, 10], [16, 10],
    [12, 11],
    [13, 12], [17, 12],
    [14, 13], [17, 13], [18, 13],
    [15, 14], [18, 14], [19, 14],
    [16, 15], [19, 15], [20, 15],
    [20, 16], [21, 16],
    [18, 17], [22, 17],
    [19, 18], [22, 18], [23, 18],
    [20, 19], [23, 19], [24, 19],
    [21, 20], [24, 20],
    [24, 21], [25, 21],
    [23, 22], [26, 22],
    [24, 23], [26, 23],
    [25, 24], [26, 24],
    [26, 25]
])   # 定义网络中的边，每一对 [a, b] 表示节点 a 到节点 b 的一条边

# 边的下界和上界阻抗（需要与边的数量一致）
l = np.array([
    2, 5, 3, 2, 4, 6, 5, 3, 5, 2, 3, 4, 2, 6, 6, 6, 5, 5, 5, 7, 7, 3, 3, 3, 4, 4, 4, 4, 4, 5, 6,
    5, 4, 4, 4, 3, 3, 4, 5, 5, 5, 3, 9, 5, 2, 5, 4, 5, 6, 3, 5, 4, 7, 5, 4, 5, 4, 5, 2, 4, 5, 5, 4,
    2, 5, 3, 2, 4, 6, 5, 3, 5, 2, 3, 4, 2, 6, 6, 6, 5, 5, 5, 7, 7, 3, 3, 3, 4, 4, 4, 4, 4, 5, 6,
    5, 4, 4, 4, 3, 3, 4, 5, 5, 5, 3, 9, 5, 2, 5, 4, 5, 6, 3, 5, 4, 7, 5, 4, 5, 4, 5, 2, 4, 5, 5, 4])   # 每条边的下界阻抗
u = np.array([
    4, 10, 8, 10, 7, 10, 8, 7, 10, 9, 8, 7, 8, 11, 11, 11, 10, 10, 10, 10, 10, 9, 9, 9, 8, 8, 8, 10,
    10, 10, 10, 8, 9, 9, 10, 11, 8, 7, 8, 11, 11, 10, 13, 15, 12, 11, 15, 9, 12, 11, 8, 9, 10, 12, 8,
    7, 8, 8, 11, 11, 14, 8, 14,
    4, 10, 8, 10, 7, 10, 8, 7, 10, 9, 8, 7, 8, 11, 11, 11, 10, 10, 10, 10, 10, 9, 9, 9, 8, 8, 8, 10,
    10, 10, 10, 8, 9, 9, 10, 11, 8, 7, 8, 11, 11, 10, 13, 15, 12, 11, 15, 9, 12, 11, 8, 9, 10, 12, 8,
    7, 8, 8, 11, 11, 14, 8, 14])   # 每条边的上界阻抗

# 起点和终点
start_node = 1
end_node = 26

alpha = 0.5  # 权重系数
Z_max = 241  # 节点的最大鲁棒成本(计算方法：运行各种小模块-最大化鲁棒成本得到的上界阻抗总和减去各种小模块-寻找上下界最短路得到的下界阻抗总和)
S_min = 21  # 下界最短路
S_max = 48  # 上界最短路
Smiddle_max = 123.5  # 中值最长路(计算方法：各种小模块-寻找最长路，可得)146/65

# 为了后续找出上界阻抗总和、未经过路段的最小下界阻抗
# 定义一个辅助函数，用于找到边的索引
def find_edge(node1, node2, edges):
    index = np.where((edges[:, 0] == node1) & (edges[:, 1] == node2))  # 找到满足条件的边的索引
    return index[0][0] if index[0].size > 0 else None  # 返回索引，如果未找到则返回 None

# 分支定界算法主函数(实现分支定界算法，结合启发式搜索，找到起点到终点的最优路径)
def branch_and_bound_with_heuristic(nodes, edges, l, u, start, end):
    optimal_path = []  # 最优路径
    R_ort = float('inf')  # 初始化当前最优鲁棒成本为无穷大
    visited = {}  # 字典，用于记录每个节点的最优路径成本以便剪枝

    # 优先队列初始化：存储 (实际路径成本 g, 当前节点, 当前路径)
    pq = []
    heappush(pq, (0, start, [start]))  # 将起点加入队列，路径成本为 0

    while pq:  # 当队列不为空时，持续搜索
        g_cost, current_node, current_path = heappop(pq)  # 从队列中取出路径成本最低的节点

        # 如果当前节点是终点，检查是否更新最优路径
        if current_node == end:
            R_j = compute_robust_cost(current_path, edges, l, u)  # 计算当前路径的鲁棒成本
            if R_j < R_ort:  # 如果发现更优路径，则更新
                R_ort = R_j
                optimal_path = current_path
            continue

        # 剪枝：若当前路径成本不优于已记录的成本，则跳过
        if current_node in visited and visited[current_node] <= g_cost:
            continue
        visited[current_node] = g_cost

        # 遍历所有相邻节点
        neighbors = get_neighbors(current_node, edges)  # 获取当前节点的所有相邻节点
        for next_node in neighbors:
            if next_node not in current_path:  # 避免循环路径
                new_path = current_path + [next_node]  # 新路径
                new_g_cost = compute_robust_cost(new_path, edges, l, u)  # 计算实际成本

                # 剪枝条件：仅当实际成本小于当前最优鲁棒成本时才继续探索
                if new_g_cost <= R_ort:
                    heappush(pq, (new_g_cost, next_node, new_path))  # 加入队列

    return optimal_path, R_ort  # 返回最优路径及其鲁棒成本

# 简化的启发式函数(定义启发式估计函数，返回当前节点到终点的一个估计成本)
def heuristic_cost(current_node, end_node, edges, l):
    """
    简单的启发式函数，暂时返回常数 0，用于占位符。
    """
    return 0  # 启发式估计为零，可设计更复杂的估计逻辑

# 计算鲁棒成本(结合公式计算鲁棒成本，其中考虑已访问路径和未访问路径的阻抗)
def compute_robust_cost(path, edges, l, u):
    max_cost = 0  # 累加路径上的上界阻抗
    aaa = 0  # 累加路径上的上界阻抗与下界阻抗之和

    # 遍历路径上的每一条边，计算成本
    for i in range(len(path) - 1):
        edge_index = find_edge(path[i], path[i + 1], edges)  # 找到边的索引
        if edge_index is not None:
            max_cost += u[edge_index]  # 累加上界阻抗
            aaa += u[edge_index] + l[edge_index]  # 累加路径上的上界阻抗与下界阻抗之和

    # 找到未经过的边，并计算这些边的最小下界阻抗
    visited_edges = set()
    for i in range(len(path) - 1):
        visited_edges.add(find_edge(path[i], path[i + 1], edges))  # 已访问的边索引

    unused_edges = [i for i in range(len(edges)) if i not in visited_edges]  # 未访问的边索引列表
    min_cost = dijkstra_min_cost(unused_edges, edges, l)  # 使用 Dijkstra 算法计算未经过的边的最小下界阻抗

    # 计算鲁棒成本，根据给定的公式
    return 1 - (alpha * (1 - (max_cost - min_cost) / Z_max) + (1 - alpha) * (1 - (aaa - S_min - S_max) / (2 * Smiddle_max - S_min - S_max)))

# Dijkstra算法(用于计算未访问边组成的路径的最小下界阻抗，即xrt)
def dijkstra_min_cost(unused_edges, edges, l, start_node=1, end_node=26):
    # 构造图的邻接列表
    graph = {}
    for edge in unused_edges:
        node1, node2 = edges[edge]
        if node1 not in graph:
            graph[node1] = []
        if node2 not in graph:
            graph[node2] = []
        graph[node1].append((node2, l[edge]))  # 添加邻居及下界阻抗
        graph[node2].append((node1, l[edge]))  # 双向图

    # 初始化 Dijkstra 算法
    min_heap = [(0, start_node)]  # 优先队列存储 (成本, 当前节点)
    min_cost = {node: float('inf') for node in graph}  # 初始化成本为无穷大
    min_cost[start_node] = 0

    while min_heap:  # 当队列不为空时
        current_cost, current_node = heapq.heappop(min_heap)  # 弹出当前成本最低的节点

        if current_node == end_node:  # 如果到达终点，返回当前成本
            return current_cost

        for neighbor, cost in graph.get(current_node, []):  # 遍历邻居节点
            new_cost = current_cost + cost
            if new_cost < min_cost[neighbor]:  # 如果发现更小的成本，更新
                min_cost[neighbor] = new_cost
                heapq.heappush(min_heap, (new_cost, neighbor))

    return float('inf')  # 如果无法到达终点，返回无穷大

# 获取相邻节点(返回给定节点的所有相邻节点)
def get_neighbors(node, edges):
    return np.unique(edges[edges[:, 0] == node][:, 1])  # 获取所有从节点出发的目标节点

# 主程序运行逻辑
start_time = time.time()  # 记录程序开始时间

# 调用分支定界算法
optimal_path, robust_cost = branch_and_bound_with_heuristic(nodes, edges, l, u, start_node, end_node)

# 计算路径的上界阻抗总和
max_cost = sum(u[find_edge(optimal_path[i], optimal_path[i + 1], edges)] for i in range(len(optimal_path) - 1))

# 找到未经过的边
visited_edges = set(find_edge(optimal_path[i], optimal_path[i + 1], edges) for i in range(len(optimal_path) - 1))
unused_edges = [i for i in range(len(edges)) if i not in visited_edges]

# 使用 Dijkstra 算法计算未经过路段的最小下界阻抗
min_cost = dijkstra_min_cost(unused_edges, edges, l)

end_time = time.time()  # 记录程序结束时间

# 输出结果
print("最优路径:", [int(node) for node in optimal_path])  # 输出最优路径
print("最大满意度:", 1 - robust_cost)  # 输出满意度（鲁棒成本的反值）
print("上界阻抗总和:", max_cost)  # 输出上界阻抗总和
print("未经过路段的最小下界阻抗:", min_cost)  # 输出未经过路段的最小下界阻抗
print("运行时间:", end_time - start_time, "秒")  # 输出程序运行时间

