from algorithm import (construct_solution, update_pheromones, compute_robust_cost,
                       num_ants, num_iterations, pheromones,find_shortest_path)
from data import nodes, edges, l, u, start_node, end_node
import time

# 记录整个程序的运行时间
start_time = time.time()

best_path = []
best_cost = float('inf')

for iteration in range(num_iterations):
    all_paths = []
    all_costs = []

    for ant in range(num_ants):
        path = construct_solution(pheromones, nodes, edges, l, u)
        robust_cost = compute_robust_cost(path, edges, l, u)
        all_paths.append(path)
        all_costs.append(robust_cost)

        if robust_cost < best_cost:
            best_cost = robust_cost
            best_path = path

    update_pheromones(all_paths, all_costs)

# 计算总路网的下界和上界最短路径
lower_bound_path = find_shortest_path(edges, l, u, start_node, end_node, cost_type='lower')
upper_bound_path = find_shortest_path(edges, l, u, start_node, end_node, cost_type='upper')

end_time = time.time()

# 输出结果
print("最佳路径:", [int(node) for node in best_path])
print("最佳鲁棒成本:", best_cost)
print("运行时间:", end_time - start_time, "秒")
print("总路网下界最短路径:", [int(node) for node in lower_bound_path])
print("总路网上界最短路径:", [int(node) for node in upper_bound_path])


"""
最佳路径: [1, 4, 8, 14, 19, 23, 26]
最佳鲁棒成本: 8.2
运行时间: 1.6657025814056396 秒
总路网下界最短路径: [1, 5, 4, 8, 14, 19, 24, 26]
总路网上界最短路径: [1, 3, 6, 13, 18, 22, 26]
"""